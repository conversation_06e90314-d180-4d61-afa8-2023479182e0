//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:auto_size_text/auto_size_text.dart';

import 'package:flutter/material.dart';
import '/resources/widgets/store_logo_widget.dart';
import '/resources/widgets/notification_icon_widget.dart';
import '/resources/widgets/product_item_container_widget.dart';
import '/resources/pages/browse_category_page.dart';
import '/resources/pages/product_detail_page.dart';
import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';
import '../../app/services/woocommerce_service.dart';
import '/bootstrap/helpers.dart';
import '/resources/widgets/app_loader_widget.dart';
import '/resources/widgets/buttons.dart';
import '/resources/widgets/cached_image_widget.dart';
import '/resources/widgets/home_drawer_widget.dart';
import 'package:flutter_swiper_view/flutter_swiper_view.dart';
import 'package:nylo_framework/nylo_framework.dart';


class CompoHomeWidget extends StatefulWidget {
  const CompoHomeWidget({super.key});

  // WooSignalApp removed - using WooCommerceService instead

  @override
  createState() => _CompoHomeWidgetState();
}

class _CompoHomeWidgetState extends NyState<CompoHomeWidget> {
  @override
  get init => () async {
        await _loadHome();
      };

  _loadHome() async {
    try {
      // Simplified approach - get all categories and products
      categories = await WooCommerceService().getProductCategories(
        parent: 0,
        perPage: 50,
        hideEmpty: true,
      );

      // Sort categories by menu order
      categories.sort((category1, category2) =>
          (category1.menuOrder ?? 0).compareTo(category2.menuOrder ?? 0));

      for (var category in categories) {
        List<WooProduct> products = await WooCommerceService().getProducts(
          perPage: 10,
          category: category.id,
          status: WooFilterStatus.publish,
          stockStatus: WooProductStockStatus.instock,
        );
        if (products.isNotEmpty) {
          categoryAndProducts.addAll({category: products});
        }
      }
    } catch (e) {
      NyLogger.error("Error loading home data: $e");
    }
  }

  List<WooProductCategory> categories = [];
  Map<WooProductCategory, List<WooProduct>> categoryAndProducts = {};

  @override
  Widget view(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    List<String> bannerImages = []; // TODO: Add banner images configuration
    return Scaffold(
      drawer: HomeDrawerWidget(
          productCategories: categories),
      appBar: AppBar(
        centerTitle: true,
        title: StoreLogo(),
        actions: [
          Flexible(
              child: Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: NotificationIcon(),
          )),
        ],
        elevation: 0,
      ),
      body: SafeArea(
        child: categoryAndProducts.isEmpty
            ? AppLoaderWidget()
            : ListView(
                shrinkWrap: true,
                children: [
                  if (bannerImages.isNotEmpty)
                    SizedBox(
                      height: size.height / 2.5,
                      child: Swiper(
                        itemBuilder: (BuildContext context, int index) {
                          return CachedImageWidget(
                            image: bannerImages[index],
                            fit: BoxFit.cover,
                          );
                        },
                        itemCount: bannerImages.length,
                        viewportFraction: 0.8,
                        scale: 0.9,
                      ),
                    ),
                  ...categoryAndProducts.entries.map((catProds) {
                    double containerHeight = size.height / 1.1;
                    bool hasImage = catProds.key.image != null;
                    if (hasImage == false) {
                      containerHeight = (containerHeight / 2);
                    }
                    return Container(
                      height: containerHeight,
                      width: size.width,
                      margin: EdgeInsets.only(top: 10),
                      child: Column(
                        children: [
                          if (hasImage)
                            InkWell(
                              child: CachedImageWidget(
                                image: catProds.key.image!.src,
                                height: containerHeight / 2,
                                width: MediaQuery.of(context).size.width,
                                fit: BoxFit.cover,
                              ),
                              onTap: () => _showCategory(catProds.key),
                            ),
                          ConstrainedBox(
                            constraints: BoxConstraints(
                              minHeight: 50,
                              minWidth: double.infinity,
                              maxHeight: 80.0,
                              maxWidth: double.infinity,
                            ),
                            child: Container(
                              padding: EdgeInsets.symmetric(horizontal: 8),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Expanded(
                                    child: AutoSizeText(
                                      parseHtmlString(catProds.key.name!),
                                      style: Theme.of(context)
                                          .textTheme
                                          .titleMedium!
                                          .copyWith(
                                              fontWeight: FontWeight.bold,
                                              fontSize: 22),
                                      maxLines: 1,
                                    ),
                                  ),
                                  Flexible(
                                    child: SizedBox(
                                      width: size.width / 4,
                                      child: LinkButton(
                                        title: trans("View All"),
                                        action: () =>
                                            _showCategory(catProds.key),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          Container(
                            height: hasImage
                                ? (containerHeight / 2) / 1.2
                                : containerHeight / 1.2,
                            padding: EdgeInsets.symmetric(horizontal: 8),
                            child: ListView.builder(
                              scrollDirection: Axis.horizontal,
                              shrinkWrap: false,
                              itemBuilder: (cxt, i) {
                                WooProduct product = catProds.value[i];
                                return SizedBox(
                                  height: MediaQuery.of(cxt).size.height,
                                  width: size.width / 2.5,
                                  child: ProductItemContainer(
                                      product: product,
                                      onTap: () => _showProduct(product)),
                                );
                              },
                              itemCount: catProds.value.length,
                            ),
                          )
                        ],
                      ),
                    );
                  }),
                ],
              ),
      ),
    );
  }

  _showCategory(WooProductCategory productCategory) {
    routeTo(BrowseCategoryPage.path, data: productCategory);
  }

  _showProduct(WooProduct product) =>
      routeTo(ProductDetailPage.path, data: product);
}
