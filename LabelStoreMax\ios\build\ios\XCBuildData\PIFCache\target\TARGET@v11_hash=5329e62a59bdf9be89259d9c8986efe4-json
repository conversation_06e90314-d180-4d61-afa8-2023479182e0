{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a326c30a3b700f5b88fb66862afaf076", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/StripeApplePay/StripeApplePay-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/StripeApplePay/StripeApplePay-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/StripeApplePay/StripeApplePay.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "StripeApplePay", "PRODUCT_NAME": "StripeApplePay", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9851354d13340131dea69df90daa3b8626", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988f14a9285786a87a372b1d19b6ee0d0e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/StripeApplePay/StripeApplePay-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/StripeApplePay/StripeApplePay-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/StripeApplePay/StripeApplePay.modulemap", "PRODUCT_MODULE_NAME": "StripeApplePay", "PRODUCT_NAME": "StripeApplePay", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98683ccb725a3fa3f911bda5f7d4272c64", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988f14a9285786a87a372b1d19b6ee0d0e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/StripeApplePay/StripeApplePay-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/StripeApplePay/StripeApplePay-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/StripeApplePay/StripeApplePay.modulemap", "PRODUCT_MODULE_NAME": "StripeApplePay", "PRODUCT_NAME": "StripeApplePay", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cc0c03565fc2fda0611eb19531c19983", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f2c7bbe2535b17730a301334aa7c8839", "guid": "bfdfe7dc352907fc980b868725387e98a82c4db4cd12be5ee33304b099f5f3c7", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98a1fd8d7ecbc4daf325b88b0e74d5ce3a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a5efec1a16a10239484a0847ac476bc8", "guid": "bfdfe7dc352907fc980b868725387e98d1dbc26b03efe6e41b4ef1abdd749fe5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fdb2242d214f2d9c8134fb8800a179f", "guid": "bfdfe7dc352907fc980b868725387e981f833756a214581305b1232af1cdc036"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f973a2b7890379bf09ff0c3befda7702", "guid": "bfdfe7dc352907fc980b868725387e98608ab89302e512ca20aae1e41df9e6f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b67a31fe374f7e37856c8fc6122e2c8", "guid": "bfdfe7dc352907fc980b868725387e98aa9f00afd45676a2e2cc80e67a07ac02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856de9290f74225823476991160dc36cc", "guid": "bfdfe7dc352907fc980b868725387e98ffdfc2a3d17b9f52953f3e059e5accd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ce43b5937781aa6da3bb9e2c116fa26", "guid": "bfdfe7dc352907fc980b868725387e98255ca8bce631b076a8c60695fb50a8e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ca112982bfbc5e571604cd35c1029b9", "guid": "bfdfe7dc352907fc980b868725387e98440c7a4cf610c2d02c4f71d174cb6874"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f39ee8d40d2a5a3f21f9818874db0bb", "guid": "bfdfe7dc352907fc980b868725387e982753afc6f7719d304119e337b62c4cf5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a389f96078768f1164d93ce43f89a0ad", "guid": "bfdfe7dc352907fc980b868725387e988cc4b7fadebf5fbacecf66fe78d23031"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ca5c550498fb56971c24927a08c8cd8", "guid": "bfdfe7dc352907fc980b868725387e9846390be09f4acfd8062efa53c857721a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a8c2ecf4ee8871b415017d934f5c7aa", "guid": "bfdfe7dc352907fc980b868725387e9833b7e91eb8f16eff5327495bbffbac92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869edfc048c45d05319c60503addda4ab", "guid": "bfdfe7dc352907fc980b868725387e986a40af7a32ea526deaa72fe77aa0ad42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836df0c091278742d2ec29f5cab7cac8c", "guid": "bfdfe7dc352907fc980b868725387e986db5fe17e15efb3f34a9cd60f88d62d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985dd7972136e53eaeccad873eb137f2f8", "guid": "bfdfe7dc352907fc980b868725387e981674f09e346563b002c3c3167ec334d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f441e7bc615d850e6c684975540849a", "guid": "bfdfe7dc352907fc980b868725387e98afaa22af48ef3bee624b537a67ecfada"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983277aa66f236469d01e9694a3c5a735a", "guid": "bfdfe7dc352907fc980b868725387e98328547f3b14e9c85f056b352cb89aaf0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985694673eea399700ee780e49b5473892", "guid": "bfdfe7dc352907fc980b868725387e98b26138bd18cee56331a56d01a4820526"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896a99da7fc84e631d3f1d5008ced7d0a", "guid": "bfdfe7dc352907fc980b868725387e980c23eee6872df4a3d3ed150d90832606"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98489331e732d84eb4eb072e0173f30308", "guid": "bfdfe7dc352907fc980b868725387e98873abfb34e0c99be9c2b0f60f6e0666c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e876dcf6f0e4e336b288ee5784f2248a", "guid": "bfdfe7dc352907fc980b868725387e98f8b2f2c4b960d3b2cb25630a2c24899b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1d9ebfeaedbe69254bdca991dad5dc9", "guid": "bfdfe7dc352907fc980b868725387e985afb765b7fe6378007bc5601c95dd12d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837cf7108ea32cf3f0b5f6cd1130dc15c", "guid": "bfdfe7dc352907fc980b868725387e9871ea67dffee576f1b6d473c16f9a204d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef30b25c0bf838b2d0d82fa0edf97ca8", "guid": "bfdfe7dc352907fc980b868725387e986c1ab690375661d01f17bfb0645dc623"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f14d8e47ec768574239258b078c08e2", "guid": "bfdfe7dc352907fc980b868725387e98fa9416f92af009a2191221be46935ea6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7623bd7b932a95d3197d2eb45532cd2", "guid": "bfdfe7dc352907fc980b868725387e9894086f3c65c09bcae999a48c0e1b3675"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c33f9338ce41b49d38aa810ecf0808a5", "guid": "bfdfe7dc352907fc980b868725387e98ba639b977e62430e96a9cb0a5d02b82a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987daa9f8cf47cd853578ff1407eb7255c", "guid": "bfdfe7dc352907fc980b868725387e98895bca72273b03c4e43e455f48910a1d"}], "guid": "bfdfe7dc352907fc980b868725387e9877d215f854722a1bc546db1762e2c72a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983fda363095ae05f15b2920abb51d4948", "guid": "bfdfe7dc352907fc980b868725387e989476a25877fc7a583f48208919f97f63"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c049689114c35096fc93042e274d8d5b", "guid": "bfdfe7dc352907fc980b868725387e987f3ed6b87df901d4b2271a6e269a4682"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831e748fa35c434cc0f07d820e7512aa7", "guid": "bfdfe7dc352907fc980b868725387e98b6688de8a6322fe93c888c5c3fa96a0e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f944155b288dc2c8e55f5e7d7272221", "guid": "bfdfe7dc352907fc980b868725387e9846dd928dae9f6de13211f504024f17d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818d991a16c5cf18a6a1eafb10636cdd6", "guid": "bfdfe7dc352907fc980b868725387e98bb5b9bc54ed80cf00d8cd1bd2386e3cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a569d7e14bb644f96117f6a2074d804e", "guid": "bfdfe7dc352907fc980b868725387e98bb0f1999bc8e76069e1db67d941b71da"}], "guid": "bfdfe7dc352907fc980b868725387e9884ba99264bd4f42038f61d92e9f9d1c4", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9893a6aefda5143dd7f9bd0194a2b6c115", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e982d98be93881617cd378e87b1e9124bc7", "name": "StripeCore"}], "guid": "bfdfe7dc352907fc980b868725387e9864c30109ee71434e4e716d99f4166e22", "name": "StripeApplePay", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e48a873b6e297ebc2a87f23eb2fd0723", "name": "StripeApplePay.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}