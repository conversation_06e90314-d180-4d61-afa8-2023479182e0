{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988eb2ddacef72baa58a7cc41236cfaae3", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/flutter_secure_storage", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "flutter_secure_storage", "INFOPLIST_FILE": "Target Support Files/flutter_secure_storage/ResourceBundle-flutter_secure_storage-flutter_secure_storage-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "flutter_secure_storage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e980cbaaf4b093921c516a4d6e77c4c97e7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9849b2676d86b08a3350767f8cea3c9926", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/flutter_secure_storage", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "flutter_secure_storage", "INFOPLIST_FILE": "Target Support Files/flutter_secure_storage/ResourceBundle-flutter_secure_storage-flutter_secure_storage-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "flutter_secure_storage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9855c00dae1e86ec5f35ab7f6284fa3098", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9849b2676d86b08a3350767f8cea3c9926", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/flutter_secure_storage", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "flutter_secure_storage", "INFOPLIST_FILE": "Target Support Files/flutter_secure_storage/ResourceBundle-flutter_secure_storage-flutter_secure_storage-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "flutter_secure_storage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9863b4d4c153407bedf545806a2b00e1c2", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98f47ae3ba03fc6d407b82fb70639c0127", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98cd2d2acaa501839cc9fe1ac41da5368c", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ce45719f49c807508ce88c4a9af13aa4", "guid": "bfdfe7dc352907fc980b868725387e98edb5510b246f6481c3e33a2b115d3db5"}], "guid": "bfdfe7dc352907fc980b868725387e987a308011239d7e4cced80ec933f50add", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98a0220561f537715e864e45aed9ae8b8b", "name": "flutter_secure_storage-flutter_secure_storage", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e989548ba3fd96e73f640dce7442408204f", "name": "flutter_secure_storage.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}