{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d61e5e2f910089344d7d555189995c5a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite/sqflite-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/sqflite/sqflite-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite/sqflite.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite", "PRODUCT_NAME": "sqflite", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e19228be12252edf38dd6ca51b6df219", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9858e38b30233ad60af530c8881949ae62", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite/sqflite-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/sqflite/sqflite-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite/sqflite.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite", "PRODUCT_NAME": "sqflite", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98641391e496a4a37f3ef5b39b0f291421", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9858e38b30233ad60af530c8881949ae62", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite/sqflite-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/sqflite/sqflite-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite/sqflite.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite", "PRODUCT_NAME": "sqflite", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cab47b6636e5c33377ce749e179bf40d", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984477903ce6d92a0a59c19020f1dc5e3c", "guid": "bfdfe7dc352907fc980b868725387e98a3aecb2067b7ea581e8574efa04b567d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98160d32c845507287e968dfc0d3d345e0", "guid": "bfdfe7dc352907fc980b868725387e981241d0a2050140876cd7caaaba9d1998", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abd7e1c82aa40ea056575d26db2b753f", "guid": "bfdfe7dc352907fc980b868725387e98eccfc78ceaf7b8a4229553dcf85fb3a1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f5edc1ff31c42466441c793a061c7c9", "guid": "bfdfe7dc352907fc980b868725387e98902dd6cd6b926c073ff0655cff5874c9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e7b6275ba9174e6c43e5ecaa29a504c", "guid": "bfdfe7dc352907fc980b868725387e982f2cf96f4fe496c62ed6e61b78c447c3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d101b9a62819132a65d567aee9ffb47f", "guid": "bfdfe7dc352907fc980b868725387e98a2d892e9ba8ee8ddf71219572c15593b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982abb10bd4f0d4ebf6e2ca959eaf5891d", "guid": "bfdfe7dc352907fc980b868725387e9801fecbbc50ceaf5266cd4291381e574d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f45cb1809a034aa3727c5f2ebc8333d", "guid": "bfdfe7dc352907fc980b868725387e98eae375f2e69b9071095a6d6b712ecf87", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8812d9ca1f50c11ca48b304ec7f3344", "guid": "bfdfe7dc352907fc980b868725387e98694125d1ef8c543432a3fa457181990d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e846b8474a743bf433ea0609f606908", "guid": "bfdfe7dc352907fc980b868725387e98e444ae91aa8d0293aee6844bfff822a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e17773b292901894d28ce5e30d22c4d0", "guid": "bfdfe7dc352907fc980b868725387e98f5449576283c02c6c30500765fadd611", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c551e8ed668deb9b5812f95a098f1160", "guid": "bfdfe7dc352907fc980b868725387e984617d552d1a309fa78e5732b6fd0ead6", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98ad2dc82ab8ed911bd330f7f2eae217f7", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b6a4b87a7a4e9bac0c21a8215462968d", "guid": "bfdfe7dc352907fc980b868725387e98f725d4e8e04b01e297b4f883c6405f63"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814ce06a4abdc9d9665fd7820a6fbc48a", "guid": "bfdfe7dc352907fc980b868725387e9877cc4fd47e58b231dedf611a4570c2ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b7694f30096187049a2e323c49fc8ee", "guid": "bfdfe7dc352907fc980b868725387e980014fe2e5de017ddefafd4b3d370f8c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c7f4371c8ed836de91a2a0f5592c5ea", "guid": "bfdfe7dc352907fc980b868725387e9802c69bbb81d8d4f7c1a7acf7f55fbda8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f0e5f93bb8c8cd2d251442f231cf329", "guid": "bfdfe7dc352907fc980b868725387e980149839262cdea651bd496edc61a3a28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e48f5b939dab0803d7029ad4bbe5a49c", "guid": "bfdfe7dc352907fc980b868725387e98133069e8e6329fdbaa77a58206ad8daa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984210d527b80a11cef6e541d05faedfbf", "guid": "bfdfe7dc352907fc980b868725387e98d493fecd6e2a48d8e62594c793a2cb3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae8d984e2f65c8be5f87dbab62c2f590", "guid": "bfdfe7dc352907fc980b868725387e98ae03d7cb1fc02e7db9ca7eba050a3559"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c58868270be6c4dbcc5835e1f4c79c5", "guid": "bfdfe7dc352907fc980b868725387e98b7f2d5a5c9f396951fe6eefa33172664"}], "guid": "bfdfe7dc352907fc980b868725387e98da8caa0468a6494c539bcc9a3f56496f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9831e748fa35c434cc0f07d820e7512aa7", "guid": "bfdfe7dc352907fc980b868725387e980d562db8451c55dccc31ac9670df4bb5"}], "guid": "bfdfe7dc352907fc980b868725387e989c121c0173be5443faa5b2120d803293", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c44defa1c51e6619c9bc20d8804a8b84", "targetReference": "bfdfe7dc352907fc980b868725387e9894b6f514aa32ee4cfdd7fc11c1ff5321"}], "guid": "bfdfe7dc352907fc980b868725387e984c8ab30283e75361b6a65cb914f55e53", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9894b6f514aa32ee4cfdd7fc11c1ff5321", "name": "sqflite-sqflite_darwin_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e983786431ce548989b846bbf1a7384f58e", "name": "sqflite", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9892137925c03f59a4fb600ced1a959f92", "name": "sqflite.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}