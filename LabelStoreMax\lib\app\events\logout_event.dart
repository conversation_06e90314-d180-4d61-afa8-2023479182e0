
import '/app/models/cart.dart';
import '/app/services/auth_service.dart';
import 'package:nylo_framework/nylo_framework.dart';

class LogoutEvent implements NyEvent {
  @override
  final listeners = {DefaultListener: DefaultListener()};
}

class DefaultListener extends NyListener {
  @override
  handle(dynamic event) async {
    // Phase 2.2: Enhanced logout with AuthService
    await AuthService().logout();
    await Cart.getInstance.clear();
    await routeToInitial();
  }
}
