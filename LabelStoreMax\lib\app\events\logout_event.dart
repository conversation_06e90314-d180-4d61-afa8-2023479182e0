
import '/app/models/cart.dart';
import 'package:nylo_framework/nylo_framework.dart';
// TODO: Replace with WooCommerce authentication
// import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';

class LogoutEvent implements NyEvent {
  @override
  final listeners = {DefaultListener: DefaultListener()};
}

class DefaultListener extends NyListener {
  @override
  handle(dynamic event) async {
    // TODO: Implement WooCommerce logout
    // await WooCommerceService().logout();
    await Cart.getInstance.clear();
    // TODO: Clear user session state
    await routeToInitial();
  }
}
