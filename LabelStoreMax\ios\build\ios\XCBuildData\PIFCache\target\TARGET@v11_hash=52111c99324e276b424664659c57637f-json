{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cf5932e05ac491b48bb90c54a4468f32", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/stripe_ios/stripe_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/stripe_ios/stripe_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/stripe_ios/stripe_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "stripe_ios", "PRODUCT_NAME": "stripe_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9802dfba1040b9826bcc620b6e29f5452e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f78f11eb0829559266291550e60c31b5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/stripe_ios/stripe_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/stripe_ios/stripe_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/stripe_ios/stripe_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "stripe_ios", "PRODUCT_NAME": "stripe_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98353c3596a6874e21a82e5cebb22618a5", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f78f11eb0829559266291550e60c31b5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/stripe_ios/stripe_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/stripe_ios/stripe_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/stripe_ios/stripe_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "stripe_ios", "PRODUCT_NAME": "stripe_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a1caeb009dfd3c4f8a9eac8f99a0b6ec", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b5ad1bdad05527d0ca470461cbd6ad4a", "guid": "bfdfe7dc352907fc980b868725387e987a865173bbd33c964289c298110d0113", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986794bdf56c6aac4f402078fcc0b625f8", "guid": "bfdfe7dc352907fc980b868725387e9889018e9986950050023d79b3eb3b8d0d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98915908823810de571fd0166ddd6b5de8", "guid": "bfdfe7dc352907fc980b868725387e989d1e80df0caa541ffb9a2751ccc5763d", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98aa7647f5f17c2b31e264585bf9535b3b", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985f895cf77cab60ea0626c0af3af4e725", "guid": "bfdfe7dc352907fc980b868725387e988d5321c34a005bc3d3eafabdcbbd195b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98550f0a38bb41d4fc29dc211f52ab8ff2", "guid": "bfdfe7dc352907fc980b868725387e98134963bcfea375703bad8fa6db68aee4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1289cb95f1609c5657b380ed81b5460", "guid": "bfdfe7dc352907fc980b868725387e985d76fe350679af2392b09be1589a9301"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984753f7e9cf4af0b82a6c7ac8812ae3fc", "guid": "bfdfe7dc352907fc980b868725387e9887b417888cd18749bb988162ddf0a461"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982eb59760b6bc43901452489ccabcf0e9", "guid": "bfdfe7dc352907fc980b868725387e982e0c077d40950af114d2c3de10c2ad37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980251fb6e74f6e78433c1e290bdf3199e", "guid": "bfdfe7dc352907fc980b868725387e985def90ff43f5fb72613b2a2d20460005"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985eb2d9c37598130eec0de1b19069803d", "guid": "bfdfe7dc352907fc980b868725387e9814d82469694213f1a3ab1c5a70efcd2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f068892c4ae4b184995399ea0734ef9", "guid": "bfdfe7dc352907fc980b868725387e98d7a0f6526b06ee2a40fc18658a4e0013"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989144bef003cfbc87f3ac80951addd93c", "guid": "bfdfe7dc352907fc980b868725387e98cef0b492ef776659d5a68de17637f855"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b3554ad7c137aa7b5893e34f158adf7", "guid": "bfdfe7dc352907fc980b868725387e98b92befe7bbd0bd358f4442ffbeb8d012"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b02fedef709df78ec4e93e0e51c4aaf7", "guid": "bfdfe7dc352907fc980b868725387e98828ba2727dd134b1ea673095e6a76060"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb5b73567f759d63bf7f86b254d37e52", "guid": "bfdfe7dc352907fc980b868725387e9842e5fc93ab21f0d9a93b48c24abb560f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f34cc55c3dfaf64b0b37eab31546c7a", "guid": "bfdfe7dc352907fc980b868725387e98b38737da9d118a395269f3c2a4baafef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd47783e08467b96569474fd2680de9d", "guid": "bfdfe7dc352907fc980b868725387e98bf56a49c34d16918350bc655091e2b13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983954f038316f2deb5b5833a0da229152", "guid": "bfdfe7dc352907fc980b868725387e9867092df475d1ab7fe018ced320e5a9fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f358b1471b7bf44d7eed069a64c9ec4d", "guid": "bfdfe7dc352907fc980b868725387e98184e38255988c3f2d1cc446a43ab0fa8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98824f5fd7ed3838bf84a99c3d1176e420", "guid": "bfdfe7dc352907fc980b868725387e9822633ce38f935ce3e72b8bd4264e5169"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98619ab011ec524703b38641fe031d0aa8", "guid": "bfdfe7dc352907fc980b868725387e980b685fc8e8b79636980d7b575fe36c61"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863673768d02ce596163c398654714aa0", "guid": "bfdfe7dc352907fc980b868725387e9887892a066a9aa1272aa249d7397795f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1e19e34cef43d4dd828cb783e67e695", "guid": "bfdfe7dc352907fc980b868725387e98a863828ff5da9e7bd3caeb7ac9e3b577"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2ffe3eef95481aa126dc3c135a57bc0", "guid": "bfdfe7dc352907fc980b868725387e985823d453998c2396e1afdd0ca37b9aa7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987408c9e8ef2058486985b38704c749b9", "guid": "bfdfe7dc352907fc980b868725387e98e1a53772a033629d2b859755ecbe4e23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858b23ad12e8bf1804954df812253dab1", "guid": "bfdfe7dc352907fc980b868725387e9824756b63ed618aed6ff956fbef16cdad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98346a6cf8a4cdfc7c6b3ba04ffbfc8cf2", "guid": "bfdfe7dc352907fc980b868725387e98f1f554a100f412b07ee21dd2b9f9ef01"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f64113a3bd2911b3b868e34869878599", "guid": "bfdfe7dc352907fc980b868725387e9838c750581b1f0eaa24de351d1785ae2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3f4b9481f2dd486fb471af416bc6069", "guid": "bfdfe7dc352907fc980b868725387e98425761264ca94cd96945659b57bcfd28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887a7f5eaf57c47184e179b79fbfbb3fb", "guid": "bfdfe7dc352907fc980b868725387e98ccbec85d0247dd1c9cb5cf0424c13034"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf2e5c96ea73484c8bcac5f6fe327972", "guid": "bfdfe7dc352907fc980b868725387e980170396d9cbdda9b8efb15f452cea8b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986276bfb256cfe1815ada76106198bd41", "guid": "bfdfe7dc352907fc980b868725387e981399130cec9df8bb71d774e668c80ee9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c3302ee43e2a3fc88301ff4ffaa2291", "guid": "bfdfe7dc352907fc980b868725387e9859df423874e635c815a983e402654afd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e3abe283921b0d0b5616f2b5cae949b", "guid": "bfdfe7dc352907fc980b868725387e9868fca7656dffb3daddbac984d7a6994f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d78982f1a43c627605bac31a5c44ce31", "guid": "bfdfe7dc352907fc980b868725387e98ce26361b81fa44db55aee2e028cffdea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e45ebe67e269430f363cad4ef5e65942", "guid": "bfdfe7dc352907fc980b868725387e9892714b4ae2aa1ed07e621f7d10eb65bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980941756200299940d44f4cd073cd5488", "guid": "bfdfe7dc352907fc980b868725387e98520752f28d9bf57b0f679546f7bec74a"}], "guid": "bfdfe7dc352907fc980b868725387e986ab5a09b9a4ad65c720d2be023caf286", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9831e748fa35c434cc0f07d820e7512aa7", "guid": "bfdfe7dc352907fc980b868725387e98f63f763ddeca3aede9b5b5c6e2e3c953"}], "guid": "bfdfe7dc352907fc980b868725387e9868974109819156cae0001cab795a8d13", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98c69589cf5eaaadee22bdd0a46f74f90c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802b8a2060b8f4c4f36a50487027e7bca", "name": "Stripe"}, {"guid": "bfdfe7dc352907fc980b868725387e9864c30109ee71434e4e716d99f4166e22", "name": "StripeApplePay"}, {"guid": "bfdfe7dc352907fc980b868725387e98528ce098433a66c9a9c5c097217013f2", "name": "StripeFinancialConnections"}, {"guid": "bfdfe7dc352907fc980b868725387e989814132af5b72ab87e6f6046cac2a3cd", "name": "StripePaymentSheet"}, {"guid": "bfdfe7dc352907fc980b868725387e98caf0f30362a7eaf9b8b7f5ba71771d54", "name": "StripePayments"}, {"guid": "bfdfe7dc352907fc980b868725387e98bfacf038ceaf928d957d7e7abcab2e3b", "name": "StripePaymentsUI"}], "guid": "bfdfe7dc352907fc980b868725387e98f20376386b7fdaf72e36b18058deba48", "name": "stripe_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e987ca3631b039050a781963bb810e6746c", "name": "stripe_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}