//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';

class CategorySubcategoryScrollWidget extends StatefulWidget {
  const CategorySubcategoryScrollWidget({super.key});

  @override
  createState() => _CategorySubcategoryScrollWidgetState();
}

class _CategorySubcategoryScrollWidgetState
    extends State<CategorySubcategoryScrollWidget> {
  @override
  Widget build(BuildContext context) {
    return Container();
  }
}
