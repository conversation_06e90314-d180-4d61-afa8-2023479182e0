{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f87191038083c65b24d2940bab8ac2a2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_inappwebview_ios", "PRODUCT_NAME": "flutter_inappwebview_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b89882dc28a6d5c860523e82f554cc84", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980c3bea27177da9c9a82ddbea1d924c91", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_inappwebview_ios", "PRODUCT_NAME": "flutter_inappwebview_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e29cd749ce03643e9abbafc9d7ee4c45", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980c3bea27177da9c9a82ddbea1d924c91", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_inappwebview_ios", "PRODUCT_NAME": "flutter_inappwebview_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982fd3c6a7f4d84d77d3348746052d187a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98be023f577049ba2fc63bfe86ca2b0114", "guid": "bfdfe7dc352907fc980b868725387e98807bebc10b9b2347b3c38ab76dcd39c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ced648d2d843da6ee84e9847070e54f", "guid": "bfdfe7dc352907fc980b868725387e9855236db105a1790362e0f3f85da2820e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e2f978f0bcd85da9c3f3f0ea66389fce", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9801891ff41e70869c1bd965ff99a1e325", "guid": "bfdfe7dc352907fc980b868725387e988114bf115703b57c8ae21d5409dd19e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985de6cfdbcfb269be417652e68323943c", "guid": "bfdfe7dc352907fc980b868725387e98b588a7e8d8ddd4478d80982232420613"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2d09c4a8b6224e8cfb1172d14fb2837", "guid": "bfdfe7dc352907fc980b868725387e98c25bd75abc455f60626b1b6d2e12965f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b551bb80a75539cdf40e946ce05dec7", "guid": "bfdfe7dc352907fc980b868725387e9886bb1e038f2ec0e9b0706aa88efa09e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6c459529110f77329a02a975fed4560", "guid": "bfdfe7dc352907fc980b868725387e9867d7f020d92c0259475431094f026daa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836558f5bb72774f861b661d2526245b2", "guid": "bfdfe7dc352907fc980b868725387e983cbfbd91e9e8b5ed232e4a3e97ee6ea0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d37edf6d3324119ac4b747c4acbe14a", "guid": "bfdfe7dc352907fc980b868725387e985677e626286185176d39b4c7a3131d79"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5a4def427bb2143e0c0f1573c432a95", "guid": "bfdfe7dc352907fc980b868725387e98d470e112a689c44d32d123ec1e9a49dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c6525c08437b31676a50977d910ae05", "guid": "bfdfe7dc352907fc980b868725387e98ca6c1fe797300662ce54309c21e1f90d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2f885c585be01c343307634fab62a36", "guid": "bfdfe7dc352907fc980b868725387e98e2db67651016b0e5a242bf079bcd1b7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fceb02fe32952f7a51c90fa5b8d8791b", "guid": "bfdfe7dc352907fc980b868725387e9893f552456b99ece4b3b598b98c2d0951"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814ff4d3ecece09c99ff82dcafdaf2a6c", "guid": "bfdfe7dc352907fc980b868725387e98e64e2f7a87cc703185c2211316deb4fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c65b897414fe72438a5279c3fd812d2", "guid": "bfdfe7dc352907fc980b868725387e98831f9cc115b07d97e9aba4500b47d900"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98026845d46fbc20a979a2ccfed127dc1e", "guid": "bfdfe7dc352907fc980b868725387e9813af1cf6f1aa063237c27f62b8da4f4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f6e6876dd9954f82d686a74252f5a49", "guid": "bfdfe7dc352907fc980b868725387e986ed5364d2e170f445d04136ca20dd611"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981235a18d6a48f330cfc1fbecb453d4e8", "guid": "bfdfe7dc352907fc980b868725387e98e480306e9ebc93b19dbf732f19c8e307"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3a76feec6670d4998ef83666aa3bb5a", "guid": "bfdfe7dc352907fc980b868725387e98a0b529fc765425975daa2152012b3d31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca658a4bcd107fcbaa7411b8109d4c2f", "guid": "bfdfe7dc352907fc980b868725387e988ce54280e8249a8ef34a9f874fa2a3d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b75c1503548e5ba7fc7e7a6ae86bac8e", "guid": "bfdfe7dc352907fc980b868725387e98c4eb53ccf283dd9fadcbc4ab328696ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a7427286c7ef7bbf546e3cb74eb7ece", "guid": "bfdfe7dc352907fc980b868725387e988c7b5a9b08093316782f2062a6bb914d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828f02ce491c7137b6119cc75b05df6a6", "guid": "bfdfe7dc352907fc980b868725387e98023d74f18fb22dba3fb8cd08619adb5f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ae01388826cd3c1596c557438cbdd64", "guid": "bfdfe7dc352907fc980b868725387e9845ac40f0b799c7c7d7c8542c2caa56cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bbbf7155ca3576f639d9171aca26ab6", "guid": "bfdfe7dc352907fc980b868725387e98c518fd94f66a19215b72cded5acabcb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c639628f44533727028f72dd8da70ffc", "guid": "bfdfe7dc352907fc980b868725387e9865436ccd5e5292ab3eb9a2c726d4fd55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad9464a62ef5070bdaea30acd30fa5fe", "guid": "bfdfe7dc352907fc980b868725387e98c9c0f8cc39cd1f4c2791c1b6bcd95b24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a16b9aab886e9028ee77e20bb10c6773", "guid": "bfdfe7dc352907fc980b868725387e98222e67f8fb32506a7edcf8a0ac719b69"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8299d9b290e59c5093157ce9f3c3ef8", "guid": "bfdfe7dc352907fc980b868725387e985359bf3f299bad8a26efcc1796d7c1cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb0e06faac4dc087cd126ff2894cc86d", "guid": "bfdfe7dc352907fc980b868725387e9894b1e9a9c15f1d4d6dab730cad9a7356"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811ac6914deef4b74b2e4b1a57d2ce54c", "guid": "bfdfe7dc352907fc980b868725387e981eed0494494d3067db554c50722a6670"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833e28fde8a7b6443c48ba55065928547", "guid": "bfdfe7dc352907fc980b868725387e988b7cd12bea006b45c3dbb4e16996e9d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d53e0dbb7b7cf21f89fc49b6faa15163", "guid": "bfdfe7dc352907fc980b868725387e98d7495c6870d2ecefef04b9c19bbca9b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e247f41cbe8d834c96abaf953202995c", "guid": "bfdfe7dc352907fc980b868725387e983bd56d9a4345e817c88f1f030a933016"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e2d0b34adad469ed876a520802a1734", "guid": "bfdfe7dc352907fc980b868725387e98faeeab2baa047c58c10f40717f073aab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da73861d9846d249a33fdf245cc9fb6b", "guid": "bfdfe7dc352907fc980b868725387e98cea48adeb08a5939b6bef50534e25be1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ded98b00acdbb059fae8fb3cd219f71", "guid": "bfdfe7dc352907fc980b868725387e984c86b1becfedef31481dcbad2b17a5c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882354df8177af12d69f684bbd6572f3a", "guid": "bfdfe7dc352907fc980b868725387e98da3e64c4fb8555d91305261dc4ff94d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e499a2d5b58192728f02d1318a54f26f", "guid": "bfdfe7dc352907fc980b868725387e98bda20fd5eba9b97c8a836079351c8cd7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f878e32fbbf69209a8b1f0c747df70ba", "guid": "bfdfe7dc352907fc980b868725387e9806a749e93be9d437dd5f0ddf38711a74"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986126ab62c1bf7bf93a92462cf35758df", "guid": "bfdfe7dc352907fc980b868725387e98fdb2b1d48c92bc84d4a594cf3335c801"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c3cd1e3bfe1fa718d32a0e0721cbc58", "guid": "bfdfe7dc352907fc980b868725387e98e15c16685b281da13549a9fa2a0b8813"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b37a352538ae119cc82245a0b23b81d2", "guid": "bfdfe7dc352907fc980b868725387e98cc497b3c5b4a04b925785d634ff1e14c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a52648bb5607a13455f067e296c7d73b", "guid": "bfdfe7dc352907fc980b868725387e981ba2cec9e73183ea8decd900c0ab5b8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eea506172c7b2c87ecadffaf2d2d2e74", "guid": "bfdfe7dc352907fc980b868725387e9800260b0ff6bd074c8bdbb7a8c7ac0674"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867a1ef1165c979af12a9e3cecc7078a0", "guid": "bfdfe7dc352907fc980b868725387e98fe3de5bd3d36a17e0d9aa10a554a91b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847d9be25f8b32f52e6c5eaf44697cce8", "guid": "bfdfe7dc352907fc980b868725387e986d32a954ad28f8654b283264f8000ce4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836e695e4662e7b1aee7fc114761a977d", "guid": "bfdfe7dc352907fc980b868725387e98235040965da858be0295f7b555c2e118"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fbd5a7cee737448b5fa72852a1b7e69", "guid": "bfdfe7dc352907fc980b868725387e982fbf2a8321bfaa1dd406916c3062e75e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cecbe48aed1a9e5db3db3465a823c23f", "guid": "bfdfe7dc352907fc980b868725387e98d2cc5d5d922868210f4715e81b020063"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcebc4cbdfe37862c9647c22b20297b5", "guid": "bfdfe7dc352907fc980b868725387e9817d42fd3684b39a67bed167a984be3b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98493960d61fc8a2ea86131ffc7be92ac9", "guid": "bfdfe7dc352907fc980b868725387e982186516c38e2b047b8d85b9e3abec8f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801c4a6515b9735905153f26b648260ba", "guid": "bfdfe7dc352907fc980b868725387e983ba67aae03bec559656bb0fcb9a543ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9d77ba5007d35ff3a02e6deeaaf56e3", "guid": "bfdfe7dc352907fc980b868725387e981f15a3129063fc818f963bbd86131b10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cc214fb2d438fadd8fa5ff23f2f05f7", "guid": "bfdfe7dc352907fc980b868725387e980c04b925abe5a7dbcd2bce08777fa56a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6746e100576efaa76dbed259ed39e78", "guid": "bfdfe7dc352907fc980b868725387e988a8775245f5f0001bf1ff488c004e6c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872540d45ec5393f35f47f321e8152336", "guid": "bfdfe7dc352907fc980b868725387e98b980576b5b2e5f46ce92bf1270d8250c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820d182cc8ed8a52cd92183cea4386228", "guid": "bfdfe7dc352907fc980b868725387e982d2886a197ccb6c2dac6a56d578acaa8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878c08a0ebc88684d55d244f5fab4890b", "guid": "bfdfe7dc352907fc980b868725387e984e73d88ae412b41a6f15245d2d5c16ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a930e5b1c76cbb06c47a7f3f6f7005d", "guid": "bfdfe7dc352907fc980b868725387e984eeeaa430cd3c651fec8c5bd40d762e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3232c8623ac0f8d7c73cb8544f7dc28", "guid": "bfdfe7dc352907fc980b868725387e984921e40e36d4c16aea97616d7b65ec84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822be7882cdd307a8980af723b7a97c62", "guid": "bfdfe7dc352907fc980b868725387e9802fa17a9388f990ed77caff2a3799c8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890b6fe90273db038f865dca56d8d754f", "guid": "bfdfe7dc352907fc980b868725387e98c09a7ffa31156a82f74af06abc9ab20a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988329a1826c264b67fa941f5a58ea0eba", "guid": "bfdfe7dc352907fc980b868725387e986531517c4dbf0bee7d8fc09647df214f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845877cafeac087d6a4481439aa87a9aa", "guid": "bfdfe7dc352907fc980b868725387e98b56566c8310d63c0e874e3feda6f60cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864ac0b1c4eb7c075740758e208c3cccc", "guid": "bfdfe7dc352907fc980b868725387e98fea6e8fdefc722fe9995117b432c1180"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988648d8c0a85ff5323d3b5f88a2e215db", "guid": "bfdfe7dc352907fc980b868725387e9800eb72f48284f19e8aeb3435bd73353e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98098b88f3295a6e8e95541fcfe18491a3", "guid": "bfdfe7dc352907fc980b868725387e9879e53455071d94a93e77b6a6769ba4ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe4035049fa9386b503462f7e15cc54e", "guid": "bfdfe7dc352907fc980b868725387e982c0e37396d42e8820d4f94ae1b478e55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d62c111662f2051962df6a1337bb645a", "guid": "bfdfe7dc352907fc980b868725387e9819b5b42036bb6397bb0db36c68e51c06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822e8c7cfcf943832f9d64c4b2305eab3", "guid": "bfdfe7dc352907fc980b868725387e98e5ea4a3398e3e2c8541039f9b5b7ccf1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cae57f08e99bfadc0701b81eb302d2f7", "guid": "bfdfe7dc352907fc980b868725387e986a94a9d362f4b91d447246f6dd18200a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8c575d31998982619e746672c704a99", "guid": "bfdfe7dc352907fc980b868725387e98399c41c7701fbf6e0e4b68cf7346d6fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f65d7081cf5e33531215ca9fdeea9ed", "guid": "bfdfe7dc352907fc980b868725387e983550e714aa8401daab485d95b3476a24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849d7d2083a555544db763f4ae44b816b", "guid": "bfdfe7dc352907fc980b868725387e98c68be0c0d0ac92ea76aae4429e97ee89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bbb0e9544ad23da382e9e9a822ced09e", "guid": "bfdfe7dc352907fc980b868725387e98ffcb057fbbb7653d76250d4829f8398f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c5b8a09fb6c38642194a9bcb8dbffc2", "guid": "bfdfe7dc352907fc980b868725387e9870a2ed7e56708a76e685aa58afdf1ccd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba21d521cdac7c629c809698958b5e25", "guid": "bfdfe7dc352907fc980b868725387e989d8d8fa1f80f856f4d19e1d444f01fe0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed24f42d57426ffdc729e995aa6581d2", "guid": "bfdfe7dc352907fc980b868725387e988e3cdc0b82521f3db176f80d8a58cf55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a772805132c1bce93ccef7a8bac2ee30", "guid": "bfdfe7dc352907fc980b868725387e98a8cdf1d27bc6d8ac8204f41d35e953bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982326b41dba7d7d31c33b3c740cff67fb", "guid": "bfdfe7dc352907fc980b868725387e983fc914a8448b684bcd62fad4f10fc0e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828b3d41f137fc7cee9a16f966fc3f48c", "guid": "bfdfe7dc352907fc980b868725387e989238987c6d4dc35d3e5a322f03007bd0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da6c1ac4080597bdf813c95bfd44f07d", "guid": "bfdfe7dc352907fc980b868725387e982eab10a4ae07b13a5937761a89cfc75b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aeafddfab85ca0a3485bd730aa9a242a", "guid": "bfdfe7dc352907fc980b868725387e98f8571cdf6d6b84a85351970ad65fff0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bfeb1daca1463d000fc810baebdf630", "guid": "bfdfe7dc352907fc980b868725387e9881892e040425214db3fb2578de4ee2d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98165b7bc7cac48ebe42ad8bd3f99cf259", "guid": "bfdfe7dc352907fc980b868725387e98aacee0605a3cd4696ff801429ed87b22"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d26d4a272397ba29f72442093090ae81", "guid": "bfdfe7dc352907fc980b868725387e98d9b5dde53a97a88c61a30832bd559cff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885898592056855d3c9d1508de6590bcb", "guid": "bfdfe7dc352907fc980b868725387e98a1aa824512ffbe0aa8647091959b911c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd9407a4551874c0cb3361acba820282", "guid": "bfdfe7dc352907fc980b868725387e98b188cb742539656a82a35a3f16981bcd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988476f648edd2bc5e1fb22c57e7f8d0fe", "guid": "bfdfe7dc352907fc980b868725387e982c3450d903e9d96a5538e5a09aaa07b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2421ffdc723ee378e2388ac00c7a97e", "guid": "bfdfe7dc352907fc980b868725387e981f9782dc8dd741f99ab0e81bb8860697"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988127e385dfda68f0dd0b3e97be2c1ab3", "guid": "bfdfe7dc352907fc980b868725387e984025d19e45142061f02f60c129fc05fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896b64af3e5ad76bc05271b5008449696", "guid": "bfdfe7dc352907fc980b868725387e98a01b381e328902ff95f15bfd07e38f5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876bb01a21a44b1392994f542ab8e8124", "guid": "bfdfe7dc352907fc980b868725387e988a30d3d8acd4e6d20602b67da47193be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ee21409f4cd51607f03cef24ffc0651", "guid": "bfdfe7dc352907fc980b868725387e981a0cc038ae1b1fd25719adda27976cce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878657e57545fe14fe22ca2357245c874", "guid": "bfdfe7dc352907fc980b868725387e9874905207c3e9df630c4cc2cc4b8f326a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982747759ffc5bbe04ad35e6403ea35169", "guid": "bfdfe7dc352907fc980b868725387e98dbff2b3ddff5858342d98184e5628d72"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826245e96fe71784797ed8e1e9c9bbc90", "guid": "bfdfe7dc352907fc980b868725387e9815758cc3f525d96d70df78b67a43d851"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4b35118674fe2c3a8bf2bf8cb1f5d22", "guid": "bfdfe7dc352907fc980b868725387e9804e4f56dc3167681cf691e8596c71146"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bba84374ee0e67e1afd148f8f3115859", "guid": "bfdfe7dc352907fc980b868725387e9853ccc12ddfac15e8ffa7df5827562029"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895db3d58764a604293d8936d0c5e3161", "guid": "bfdfe7dc352907fc980b868725387e986f0a1052aa0ca0cf8f1905181b39665f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8f26a11fd947ace2f217613eaf6bf4d", "guid": "bfdfe7dc352907fc980b868725387e98f9ac30e6751ea202187da901159227a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98544e5b7e239b1d2f1128a44227afcadb", "guid": "bfdfe7dc352907fc980b868725387e98223c0539cd38711d7a444ccf4ac8bb58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982774ba1a19ef9a515d93e091e12e46b7", "guid": "bfdfe7dc352907fc980b868725387e98459f9b8d84f37936b5a3961f1212486a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988864c9a0969908a85fbdb3e36c2321e8", "guid": "bfdfe7dc352907fc980b868725387e98163c620f28a83f56ae09907b19a50459"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98277da8e5a16d76e74f94df8bfb2ea402", "guid": "bfdfe7dc352907fc980b868725387e98fac7be43458c86a240c9c227d1209259"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d03e3991cd57ee1a29f052557e2c255f", "guid": "bfdfe7dc352907fc980b868725387e98b8750ef6b70c38f5d3efed43794b1885"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d05ccf8cd2ecfd2a2a345cfadc77b6c", "guid": "bfdfe7dc352907fc980b868725387e98f656c306a73ee677f40cdfa9f9ca4557"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7115b25667e269f844f32c66140128a", "guid": "bfdfe7dc352907fc980b868725387e9821526f14d99f3d770dc12a04027008fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c65fadb2da1f2157b9ca70d232a40ddf", "guid": "bfdfe7dc352907fc980b868725387e987b6b74deff72959662529d87a81ecb8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb80c46b37ea667673f7d941a86390e4", "guid": "bfdfe7dc352907fc980b868725387e984ff2b8aa6691cf2cf57960c7512f48a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887b372a7ac0c0ea481caa66f5c27290f", "guid": "bfdfe7dc352907fc980b868725387e9837579c29f04e4c3539ec94bc445472f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c5569eb40eed04c5ee0ce44bd20ab6a", "guid": "bfdfe7dc352907fc980b868725387e986945e5c3e4e41047a8980cb599c88d0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a36df87e84d438ae54f24b747b15416", "guid": "bfdfe7dc352907fc980b868725387e983bd0acd644bb0ce8df00d1c587ead8ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98952808c7abd95c397efbd4f480fec3a9", "guid": "bfdfe7dc352907fc980b868725387e98d946d86b98f2497902130a0062ebb0a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a02353bb1a34841f05d3a4a987035436", "guid": "bfdfe7dc352907fc980b868725387e98d55d34d789a1f86ac8f3ad9acbb944b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c272ee650f382c4e0c9e76e779b3928", "guid": "bfdfe7dc352907fc980b868725387e98981c16ccd4d794cf02683c13599f7221"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879f3a1847c8d5f06cadbfdb8453c53a8", "guid": "bfdfe7dc352907fc980b868725387e98ddc8ba56e16a719422a7dc504cf71724"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ef692188267773a744abdbd8bd732f6", "guid": "bfdfe7dc352907fc980b868725387e98a25d3bfff5529b1dd5969eb1a9425c5a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804fda70c429f9883fc13be2d6fcb108a", "guid": "bfdfe7dc352907fc980b868725387e98456eb65b214512cac8c9321656ce3a72"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f2c46868d5da47b6781803019024955", "guid": "bfdfe7dc352907fc980b868725387e982fae223ba487719295cf409bf39b44e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ccd8f37ec01f8ebaf7ffc95ae42c206", "guid": "bfdfe7dc352907fc980b868725387e98b6fd83cd4dac6dd0f302fc9fddc5720b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98687565d696fd2a13e5e1ef386f38c053", "guid": "bfdfe7dc352907fc980b868725387e98c5276f2250204d3bcbac5138a0874abf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ee2867d3409075755d6b59a9c74915b", "guid": "bfdfe7dc352907fc980b868725387e9895aecedfcb719baaead44cbb1729c5e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b8f8a2c4973afd90cba8c4dbe84617b", "guid": "bfdfe7dc352907fc980b868725387e98a24fd2b423e05dd6d9186fd39827ab84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987930445bbaa051a52fc1af98641e3584", "guid": "bfdfe7dc352907fc980b868725387e98f02306045a58d555bff9be2e61d79d11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b277fb7932a7c37021e4cf22d3e40b56", "guid": "bfdfe7dc352907fc980b868725387e98467fa1467989c14c4013c3a50fa45e13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887016cb20ac9d7e9c5de315caf1c3ce3", "guid": "bfdfe7dc352907fc980b868725387e985db12a2072ed67e7cf46c4787669da4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987850e90582d166fd91e3f89bd2cea338", "guid": "bfdfe7dc352907fc980b868725387e9872c44737bd90b83b1c578d0a29f262cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ae8a24218ab8e5328876dfeb721840d", "guid": "bfdfe7dc352907fc980b868725387e9883ab9baced37a0d9c4ff931dc6447f3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c7c141908a5a2c8d92cf3bfc027af82", "guid": "bfdfe7dc352907fc980b868725387e986dec1fa323eb105bd353a2a879ca4d07"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987adc1988e7a36087fbc9c3ed8e063314", "guid": "bfdfe7dc352907fc980b868725387e98fd2bf27ca6ef5155fea67bbae44572cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ea7ee049cb9fe84b9d736adf1f6f5db", "guid": "bfdfe7dc352907fc980b868725387e9827160be7aaceae07b878cfad218603e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0bc28b9538b845c48613f0cd5473571", "guid": "bfdfe7dc352907fc980b868725387e98285146f588a09e3f0c8cc0afcd61543c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807cdb32a5a0b1e5b19904d8601dee3fd", "guid": "bfdfe7dc352907fc980b868725387e980e247b30b26c89bee42e7d6f81d0f3f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887b25d1e63774f7b250ce1793382cc02", "guid": "bfdfe7dc352907fc980b868725387e98ea3ee11b2da1ad3ceaaf3433256ca91a"}], "guid": "bfdfe7dc352907fc980b868725387e98cac5758aaab041b306b3055c248885f4", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9831e748fa35c434cc0f07d820e7512aa7", "guid": "bfdfe7dc352907fc980b868725387e98027d34dd1e1770c79dd54f5404987fd6"}], "guid": "bfdfe7dc352907fc980b868725387e98d8f70b5ff677ed02b77232961e037957", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b7d49ad1fe66522e6eab9248dd2331d6", "targetReference": "bfdfe7dc352907fc980b868725387e98feffba4bc77d9f3d84a98c192cefdc8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a10ce6383b88c2304784070f753ec42", "guid": "bfdfe7dc352907fc980b868725387e986d52c275603be68942df62606ca0d8e3"}], "guid": "bfdfe7dc352907fc980b868725387e98ba929dd1b11c60f9a4f8fd06e4eaa3e7", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e985f0ec3a68eeed5241cb87afb05bcc380", "name": "OrderedSet"}, {"guid": "bfdfe7dc352907fc980b868725387e98feffba4bc77d9f3d84a98c192cefdc8b", "name": "flutter_inappwebview_ios-flutter_inappwebview_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98a562549a031aeda8bf3440b79b3420bc", "name": "flutter_inappwebview_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9810acd6d3a97e7ef91b90dda5618dc5c0", "name": "flutter_inappwebview_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}