//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';
import 'package:nylo_framework/nylo_framework.dart'; // Ensure Nylo is imported for getEnv

class WooCommerceService {
  static final WooCommerceService _instance = WooCommerceService._internal();
  late WooCommerce _wooCommerce;

  factory WooCommerceService() {
    return _instance;
  }

  WooCommerceService._internal() {
    _wooCommerce = WooCommerce(
      baseUrl: getEnv('WOO_BASE_URL', defaultValue: 'https://velvete.ly'),
      username: getEnv('WOO_CONSUMER_KEY', defaultValue: 'ck_2734d46c1dc323c08f39f90ecb6eee2dfa9ddb86'),
      password: getEnv('WOO_CONSUMER_SECRET', defaultValue: 'cs_193dcd15c2a971357e267919f5a4b1dd7d0e7f53'),
      useFaker: false,
      isDebug: getEnv('APP_DEBUG', defaultValue: 'true') == 'true',
    );
  }

  WooCommerce get wooCommerce => _wooCommerce;

  // Convenience methods for common operations
  Future<List<WooProduct>> getProducts({
    int page = 1,
    int perPage = 10,
    String? search,
    int? category,
    List<int>? include,
    WooSortOrderBy orderBy = WooSortOrderBy.date,
    WooSortOrder order = WooSortOrder.desc,
    WooFilterStatus status = WooFilterStatus.publish,
    WooProductStockStatus? stockStatus,
  }) async {
    return await _wooCommerce.getProducts(
      page: page,
      perPage: perPage,
      search: search,
      category: category,
      include: include,
      orderBy: orderBy,
      order: order,
      status: status,
      stockStatus: stockStatus,
    );
  }

  Future<WooProduct> getProduct(int id) async {
    return await _wooCommerce.getProduct(id);
  }

  Future<List<WooProductCategory>> getProductCategories({
    int page = 1,
    int perPage = 10,
    int? parent,
    bool? hideEmpty,
    List<int>? include,
    WooSortOrder order = WooSortOrder.asc,
    WooCategoryOrderBy orderBy = WooCategoryOrderBy.name,
  }) async {
    return await _wooCommerce.getCategories(
      page: page,
      perPage: perPage,
      parent: parent,
      hideEmpty: hideEmpty,
      include: include,
      order: order,
      orderBy: orderBy,
    );
  }

  Future<List<WooProductVariation>> getProductVariations(int productId, {
    int page = 1,
    int perPage = 10,
  }) async {
    return await _wooCommerce.getProductVaritaions( // Using the misspelled method name from the library
      productId,
      page: page,
      perPage: perPage,
    );
  }

  Future<WooOrder> createOrder(WooOrder order) async {
    return await _wooCommerce.createOrder(order);
  }

  Future<List<WooOrder>> getOrders({
    int page = 1,
    int perPage = 10,
    int? customer,
    List<WooOrderStatus> status = const [WooOrderStatus.any],
  }) async {
    return await _wooCommerce.getOrders(
      page: page,
      perPage: perPage,
      customer: customer,
      status: status,
    );
  }

  Future<WooOrder> getOrder(int id) async {
    return await _wooCommerce.getOrder(id);
  }

  // WooCustomer methods
  Future<WooCustomer> createCustomer(WooCustomer customer) async {
    return await _wooCommerce.createCustomer(customer);
  }

  Future<WooCustomer> getCustomer(int id) async {
    return await _wooCommerce.getCustomer(id);
  }

  // WooCoupon methods
  Future<List<WooCoupon>> getCoupons({
    int page = 1,
    int perPage = 10,
  }) async {
    return await _wooCommerce.getCoupons(
      page: page,
      perPage: perPage,
    );
  }

  // Shipping methods
  Future<List<WooShippingZone>> getShippingZones() async {
    return await _wooCommerce.getShippingZones();
  }

  Future<WooShippingMethod> getShippingMethod(int id) async {
    return await _wooCommerce.getShippingMethod(id: id.toString());
  }

  Future<List<WooTaxRate>> getTaxRates() async {
    return await _wooCommerce.getTaxRates();
  }

  // Product reviews
  Future<List<WooProductReview>> getProductReviews({
    int page = 1,
    int perPage = 10,
    List<int>? product,
  }) async {
    return await _wooCommerce.getProductReviews(
      page: page,
      perPage: perPage,
      product: product,
    );
  }

  Future<WooProductReview> createProductReview(int productId, WooProductReview review) async {
    return await _wooCommerce.createProductReview(review);
  }
}