{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985432f83d9fa24ffaf86a4985005a391f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9843656f3be9807310e9e0cacd1be55d71", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f2f3a8d34adeea7e17aeb24b8daca5b1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989dd8e0d57a8a458afb4a995f1acb2945", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f2f3a8d34adeea7e17aeb24b8daca5b1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98881d2969bf182a33310030c0183e4b73", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9853aaa2f4d12178e7821bfaf66c68a20b", "guid": "bfdfe7dc352907fc980b868725387e98c86325a00b5ae8790befb4506617eff4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98505785fd3b8458e1c5ccd38c7b56a9cd", "guid": "bfdfe7dc352907fc980b868725387e9837daeb459b6c7c06cd1c65f5851559e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fae93760df730e68783edee75b5f3258", "guid": "bfdfe7dc352907fc980b868725387e9831826b8003c78c78abeb6a141140e1e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835e9f28063a3378710eee4981b641f7b", "guid": "bfdfe7dc352907fc980b868725387e98e41d4d2b7a390c8f3bcb77283a937d89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877ee2d929b3db0dd3041cfb06f091815", "guid": "bfdfe7dc352907fc980b868725387e98a108d819313662fc2b1721158991d184"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e33a0fd9ceee2492557d72bf785a3be", "guid": "bfdfe7dc352907fc980b868725387e9842eb0ea52775612a9b356f60dff29250"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9986fdaac1708b09945fdae8b298dad", "guid": "bfdfe7dc352907fc980b868725387e986cfec3b4e91ef08f453a863fc5481673", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98917e4a95e8795c37eafef05dede028ce", "guid": "bfdfe7dc352907fc980b868725387e98c6d9cb67bed0e2551f5bdc9a32475742", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5b3be320cc629d82ff2290203dff668", "guid": "bfdfe7dc352907fc980b868725387e98c3d0d49d8ecea8093ef0feab82adf69d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9df867fbece84cb436fcebbc8ba30d7", "guid": "bfdfe7dc352907fc980b868725387e9867a52596d4ef348bd8d7efd9faddb4ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830e52bcc86496c817ff5f4a519d4d374", "guid": "bfdfe7dc352907fc980b868725387e98494cf4c7d69fa5097878b68f9c25c8b3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a8bd8758a21c4207317f2a3c08f61e0", "guid": "bfdfe7dc352907fc980b868725387e9865192c737358afe840d468324e62d34a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b86d9aaf213a74688b02166b734c536", "guid": "bfdfe7dc352907fc980b868725387e98d25d59aa4f481927408b2583910d2991", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843f36b65b26231021ee923d76b2c661c", "guid": "bfdfe7dc352907fc980b868725387e989abaf13d89813dd2124c4e50d0f9e450"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980240980601a9c4f67a0a9a45763aed70", "guid": "bfdfe7dc352907fc980b868725387e989ac422f77141ecb0bf3af00775ef562a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6fd8722b3b3c3518e926c028c1d2c61", "guid": "bfdfe7dc352907fc980b868725387e98ecb69399c0ff447d118cdc27bc3d374d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2fa1778888e1976d40b9ed42590216d", "guid": "bfdfe7dc352907fc980b868725387e989f3a96f0a385077efdd61f9ed34933be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985edb65b65dc09787bc0ca09edc659b85", "guid": "bfdfe7dc352907fc980b868725387e981d8bdfda09fc77863e7b3d1f27586a95"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982651dbf73633448fe27026c604ef4dd2", "guid": "bfdfe7dc352907fc980b868725387e98647f83c5ee843763f385d8a1fed9af25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982852bad239a2302c4bf083678e8dc780", "guid": "bfdfe7dc352907fc980b868725387e98505ed4c5c92939da6740299bc680f4b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984150a875dc4001ce3228744e13e76a07", "guid": "bfdfe7dc352907fc980b868725387e98752c38738cb17f59e3ae423ef6851d51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828795fbdfb6a57aabae9888673f5017a", "guid": "bfdfe7dc352907fc980b868725387e98b93f4fe708f2f6b99f5a88959907797c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5d6fc55864ce2677ff0d0c575e331d5", "guid": "bfdfe7dc352907fc980b868725387e983fc853b634b8727f0dc44b89e88bdf65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98943451a93b2cf08692f5bb14b9bf8b9d", "guid": "bfdfe7dc352907fc980b868725387e987fb72fa22c7fb28d9b8755d08d9afddf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d788f60a173e4520570d3b01aefa209", "guid": "bfdfe7dc352907fc980b868725387e98729f9034d8d929a2dd83523af4fbe202"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819f34ff78fed38c80c1ecad7eb356df9", "guid": "bfdfe7dc352907fc980b868725387e98791e650d1a974559f13038911da99a99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983066a7262323b636072fff4bc6be5652", "guid": "bfdfe7dc352907fc980b868725387e983c15a8c01fa193dd4d2aa468ae632d7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ca803c459dab3650b9e5edc61956b96", "guid": "bfdfe7dc352907fc980b868725387e98f6a07bc0610dbb240f03991e3f86b331"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca9ebd49952c41562e1bcc0e126386e8", "guid": "bfdfe7dc352907fc980b868725387e985ba41a7e221507af7184f074c190b11a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ba057ae44f94e1db6e74c846f4c55dc", "guid": "bfdfe7dc352907fc980b868725387e9828e003b3dc4700835b725ef579eadc15"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985117c5c812853348f6b0593cbc6ebafa", "guid": "bfdfe7dc352907fc980b868725387e9890bde638b655a21c1c3795a4ba597649"}], "guid": "bfdfe7dc352907fc980b868725387e9846ae0e9ff66695563286080d967a2cfb", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985e0dae483b8a36da527e0c30a0c2859f", "guid": "bfdfe7dc352907fc980b868725387e984313ad0660a1e73d5c4c59bc29d72dc9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98638ee30a3121777eec7efd25e028d66a", "guid": "bfdfe7dc352907fc980b868725387e98f35a5266f0289d6e0db6fc208c36c693"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f061b6f01e9f01a10f32a1cf93b6d15", "guid": "bfdfe7dc352907fc980b868725387e984f37cbf61d8af6db4702bb0cc80a8034"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869d19145797a61905182807007464aa4", "guid": "bfdfe7dc352907fc980b868725387e98474e58393efd3de5067a8ae04b1ab116"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d17bb75ef614a1f0bdf7e905f90562b8", "guid": "bfdfe7dc352907fc980b868725387e981b1a3c6963588d84e152f87c6edf50fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d2ef21e597d90fa26ca8363ea04fe08", "guid": "bfdfe7dc352907fc980b868725387e98f3058f28b545599a6316e518b205e667"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858b2b52e5ddf76917cf2950b563b43c2", "guid": "bfdfe7dc352907fc980b868725387e98ccde2cec12f9201290d7b6416f35ecec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bc8e52c41309c0866722340b6a4db7a", "guid": "bfdfe7dc352907fc980b868725387e98c9f53b8f1047c10bcbfa4537dd50b51e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e52e79d128b9c7cca2cdd74198fecf0e", "guid": "bfdfe7dc352907fc980b868725387e98a542c72427107f602bc9c3a881552ab3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988764149c985a4dce2a41d4e7137244fa", "guid": "bfdfe7dc352907fc980b868725387e981e83556e5f564eece46cdf861c3e42e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bb75e82e2bc3eed6cf6873efb5baf14", "guid": "bfdfe7dc352907fc980b868725387e982eafed822a32bb078b8651560dd3120f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ed6e589c7fbe50e9502bd662f814b3c", "guid": "bfdfe7dc352907fc980b868725387e985b8bdd65a62b7d6ba12eed031b72707c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bf9e393a25d9584a22b3a8c374d6c89", "guid": "bfdfe7dc352907fc980b868725387e9831b054a9e3e80f620df4624c1e4af144"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a030588a96cd9cbe1688267d4291d7c4", "guid": "bfdfe7dc352907fc980b868725387e9808ed2703c7a1a21fb2d3580c72afec47"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e7b12ce3fc60aede57abb7544ae4664", "guid": "bfdfe7dc352907fc980b868725387e9856e29f7b1b71dcae71205c11e088cc1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843f567dbb2ac10b7c6eaa430da5fda60", "guid": "bfdfe7dc352907fc980b868725387e98157e176ec01aadc7d259a8a470fde8e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870d0ff08599e9ff234c29c63a0fc50b8", "guid": "bfdfe7dc352907fc980b868725387e98285347a4753584ea75257e85503577a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cc0d94d1f4aa065eeb3d65a2b1fcb60", "guid": "bfdfe7dc352907fc980b868725387e98dc7718b1a8348cf9530a5febeb6d6191"}], "guid": "bfdfe7dc352907fc980b868725387e988b730a934283972218f3f0d68de17303", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9831e748fa35c434cc0f07d820e7512aa7", "guid": "bfdfe7dc352907fc980b868725387e984300fa2bdfd00680be6cfac5bd280208"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818d991a16c5cf18a6a1eafb10636cdd6", "guid": "bfdfe7dc352907fc980b868725387e98f8d1055852a891c86ef835a3cbddb687"}], "guid": "bfdfe7dc352907fc980b868725387e98028497abdf5fb835842b9aa942af9de2", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e980fb2e30e75ee379638252da5c6454889", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e98dde62d8ab2ce8446f4b5294bc9955724", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}