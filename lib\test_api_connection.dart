import 'package:flutter/material.dart';
import 'package:nylo_framework/nylo_framework.dart';
import 'app/services/woocommerce_service.dart';
import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  print("=== WooCommerce API Connection Test ===");
  
  try {
    WooCommerceService wooService = WooCommerceService();
    print("DEBUG: WooCommerceService instance created successfully");
    
    print("DEBUG: Attempting to fetch 1 product...");
    List<WooProduct> products = await wooService.getProducts(perPage: 1);
    print("DEBUG: Product fetch attempt completed.");

    if (products.isNotEmpty) {
        print("DEBUG: ✅ SUCCESS! Fetched products successfully. Count: ${products.length}");
        print("DEBUG: First product name: ${products[0].name}");
        print("DEBUG: First product ID: ${products[0].id}");
        print("DEBUG: First product price: ${products[0].price}");
    } else {
        print("DEBUG: ⚠️ WARNING: No products fetched. Products list is empty.");
    }
  } catch (e) {
    print("DEBUG: ❌ ERROR Fetching products!");
    print("DEBUG: Error Type: ${e.runtimeType}");
    print("DEBUG: Error Message: $e");
    print("DEBUG: Full Error Details: ${e.toString()}");
  }
  
  print("=== Test Complete ===");
}
