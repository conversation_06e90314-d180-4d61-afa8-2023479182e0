{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cb0170d6becb52689a7c9877bdde669a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984fa9bece7b22391d0cef6c95fbdfe974", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987feb725628c73556051e90861bed7995", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989dd9b0b53a39cdb6c40f3ba9422e9ac1", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987feb725628c73556051e90861bed7995", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982a34c18d6ff1f9d15ece742e98421e82", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984322bfc26edc18472cfdd3d0a00305de", "guid": "bfdfe7dc352907fc980b868725387e98f3e7c720e896163c03f020fd22270213", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988717053e2dea4f4252a5937dfea1666b", "guid": "bfdfe7dc352907fc980b868725387e98ede6a05d4be6b0b0bb62d1414baef015", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8883cb6aa3cf3220452f4b49308e8bb", "guid": "bfdfe7dc352907fc980b868725387e983bffd94d57c6824547298e4ad80a8407", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa691ce3ee6da405a9a1ea608b1f2752", "guid": "bfdfe7dc352907fc980b868725387e988f7dd6fe0886401a9252c7a93ecab45b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844a3277789908d438c53742022e99895", "guid": "bfdfe7dc352907fc980b868725387e98874c5b0e189df99a188a56b49f27b008", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98496412f596b7f7c7a34a619d213ff777", "guid": "bfdfe7dc352907fc980b868725387e982bf4eee44ddf32e91145e01a966b7b7e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f03ea2611d6e7072c409c884a8987607", "guid": "bfdfe7dc352907fc980b868725387e983aa47119b838d2ec30c25d0a959e019a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800fed4087ab56e149cdba29efd8d854f", "guid": "bfdfe7dc352907fc980b868725387e98e1a5caf25f7f883dbecc213c79d2683f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da428399fe780ab4e326695241fb57c8", "guid": "bfdfe7dc352907fc980b868725387e9842e81fb78caa198a3aba2671e52eda1c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab62537203beb1017d0eade1d2f11f50", "guid": "bfdfe7dc352907fc980b868725387e98540524b64884d493579ea783e90cc3d9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859055b8d665ecb786072025e3ab2c78c", "guid": "bfdfe7dc352907fc980b868725387e9823e7e6a324a78e6045b5df13f8ef01c5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6b210cc030926fa7c7e4b9e97df5112", "guid": "bfdfe7dc352907fc980b868725387e9853993c6600f9ce479fd5e4391113fc49", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d0418bb039f66d3588b5ed71653300a", "guid": "bfdfe7dc352907fc980b868725387e984973f40ffc40298fe02f48834b44c1df", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98acd104dc7f45c6ebc9756fce98cde7ec", "guid": "bfdfe7dc352907fc980b868725387e98436ceee9624e8e1f3147c27fdc4f93d8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e71ac72f7bd8d14bc1716f6ac869051d", "guid": "bfdfe7dc352907fc980b868725387e9896a55e93cfb264c855f7778da5f011ef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897d510d3df4afc7cd1ce6a01ce73e1a4", "guid": "bfdfe7dc352907fc980b868725387e9871cf2129f8b6e9d17062bcae089ca250", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b57dc3c9cec4a81d281c560037c9395c", "guid": "bfdfe7dc352907fc980b868725387e98cf311a056a336d23b1e805c5df2a2521", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9c1c70dce3295e85967c0ede47cc55a", "guid": "bfdfe7dc352907fc980b868725387e98a0015c83c8ededc98b0a9b6b165d7e82", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98566d946b73feb5bd044dbead21170a17", "guid": "bfdfe7dc352907fc980b868725387e98d8d08af860aa3b7913d99b6995e44edf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897d4a7f8c091a217e8c37d63859a1161", "guid": "bfdfe7dc352907fc980b868725387e980f715b1ee82a90496858876c4a93c4fe", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980364adf744d22ebc79bc0d130ccf1c32", "guid": "bfdfe7dc352907fc980b868725387e98acb3590c52ed9d7e8907b5647275b07f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b697c9d04f7cfc73a19b24fff2e0beb", "guid": "bfdfe7dc352907fc980b868725387e98a3016a518ff46589998d23b5aa5585b1", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98ec5788cee044eb358e3d0b6fcd466b65", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987644da3b40e7e061542d5c8640215c7d", "guid": "bfdfe7dc352907fc980b868725387e989298db91b7dba13b825045e6465ac8b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866fe04f1f511dc13a4a3d3a818c42ef8", "guid": "bfdfe7dc352907fc980b868725387e9863d15f1f3fabc3d60d5b6915451fd009"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987eb3eb21460071b16b925cab21f1c0c8", "guid": "bfdfe7dc352907fc980b868725387e98e1921a18a639c4067da4750e7a5961d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858ce0b89028f31c70360f35d3217e9dd", "guid": "bfdfe7dc352907fc980b868725387e9844f043b4ee94b12b97ba9111b6462799"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98611f8456425693068bce8474e7eb1b40", "guid": "bfdfe7dc352907fc980b868725387e985604a049c9bbba446407cf54c9591ac6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4e95569822d681548a1694a9d6d612d", "guid": "bfdfe7dc352907fc980b868725387e9817369e74b0b71735178761c441085619"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f9ce0cc732268f7ee8426aa70dc9b3d", "guid": "bfdfe7dc352907fc980b868725387e98b201590d3ce03d0f2c0e69ae70436b0e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98757bae11314c4653b74cdf59b0e1273f", "guid": "bfdfe7dc352907fc980b868725387e9806c54338e73efda57e3b3236fc58210d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841be68ef8909b7d7a0eb61fa2c1928ab", "guid": "bfdfe7dc352907fc980b868725387e9831d0b66a5d7c0f47d4baaf23f6458b10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8ceeef107acfef254b6883156d4dd42", "guid": "bfdfe7dc352907fc980b868725387e985397de4f4907aa0d125716b7e98acd5e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879bed32d90b779f1078bd5514b7635dc", "guid": "bfdfe7dc352907fc980b868725387e9835b08d38022a8b83089fc7cdc79ee54e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cd48eb7274fbef41ccb235f9f675015", "guid": "bfdfe7dc352907fc980b868725387e98998b3b23adb8d23c67b1e3fe932628de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a6609c561c5481ceeb31a8caf76ab6f", "guid": "bfdfe7dc352907fc980b868725387e981de57293e508dced0cc189c9f50f479f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d981d4b4ce42c2db5a5e11a20eac0bb0", "guid": "bfdfe7dc352907fc980b868725387e98bb680802f564a3c189e3175907bde2d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856c6908f8d63411a39af0df7e67ad47a", "guid": "bfdfe7dc352907fc980b868725387e9805158b8ce73b04c6e57de2fa4f25113b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828a5dfa7308d6358a322e5b47b7c00ed", "guid": "bfdfe7dc352907fc980b868725387e98bb0255ec9077f9048069d4f5ff31376f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc589b4837210bfca9d80b02aa3cc071", "guid": "bfdfe7dc352907fc980b868725387e987574b9c4019e0350adf39498644d3405"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98389bf479d1710575ce15b0e6c9e760bf", "guid": "bfdfe7dc352907fc980b868725387e98a96db06f82a457995f6ccbc05e1ba66f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f60861b4146e441dac2556a6fec953cc", "guid": "bfdfe7dc352907fc980b868725387e989817e920a89572f280e38496d22f6b59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d24b97321dbaa3dd08bc51c8fd101450", "guid": "bfdfe7dc352907fc980b868725387e98563647d443abd2d99970e460c0bc8511"}], "guid": "bfdfe7dc352907fc980b868725387e98d05d2c9da8f22e36cb2e3f1f7784d273", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9831e748fa35c434cc0f07d820e7512aa7", "guid": "bfdfe7dc352907fc980b868725387e98e7f9383b681399163f6c1af57722e714"}], "guid": "bfdfe7dc352907fc980b868725387e98463d6adbe3f492d1567f6e25c181f215", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9800e612e11445dc8113ae79c485126f2b", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e983fee11f864b832c25135f5cc2b6fd64a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}