//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/bootstrap/helpers.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';

class ProductReviewItemContainerWidget extends StatelessWidget {
  const ProductReviewItemContainerWidget(
      {super.key, required this.productReview});

  final WooProductReview productReview;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        RatingBarIndicator(
          rating: productReview.rating!.toDouble(),
          itemBuilder: (context, index) => Icon(
            Icons.star,
            color: Colors.amber,
          ),
          itemCount: 5,
          itemSize: 20.0,
          direction: Axis.horizontal,
        ),
        Padding(
          padding: const EdgeInsets.only(top: 8),
          child: Text(parseHtmlString(productReview.review)),
        ),
        Row(
          children: [
            Text(productReview.reviewer!),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 8),
              child: Icon(
                Icons.circle_rounded,
                size: 3,
              ),
            ),
            Text(
              formatDateTime("MMM d, yyyy").format(
                productReview.dateCreated!,
              ),
            ),
          ],
        )
      ],
    );
  }
}
