  message android.R.id  Activity android.app  
runOnUiThread android.app.Activity  Context android.content  LAYOUT_INFLATER_SERVICE android.content.Context  assets android.content.Context  getDrawable android.content.Context  getSystemService android.content.Context  AssetManager android.content.res  
PorterDuff android.graphics  Typeface android.graphics  Mode android.graphics.PorterDuff  SRC_IN  android.graphics.PorterDuff.Mode  createFromAsset android.graphics.Typeface  Drawable android.graphics.drawable  setColorFilter "android.graphics.drawable.Drawable  Build 
android.os  SDK_INT android.os.Build.VERSION  LOLLIPOP android.os.Build.VERSION_CODES  R android.os.Build.VERSION_CODES  Log android.util  d android.util.Log  Gravity android.view  LayoutInflater android.view  View android.view  BOTTOM android.view.Gravity  CENTER android.view.Gravity  TOP android.view.Gravity  inflate android.view.LayoutInflater  
background android.view.View  findViewById android.view.View  TextView android.widget  Toast android.widget  setTextColor android.widget.TextView  text android.widget.TextView  textSize android.widget.TextView  typeface android.widget.TextView  Callback android.widget.Toast  LENGTH_LONG android.widget.Toast  LENGTH_SHORT android.widget.Toast  addCallback android.widget.Toast  cancel android.widget.Toast  duration android.widget.Toast  makeText android.widget.Toast  
setGravity android.widget.Toast  show android.widget.Toast  view android.widget.Toast  mToast android.widget.Toast.Callback  
onToastHidden android.widget.Toast.Callback  
ContextCompat androidx.core.content  getDrawable #androidx.core.content.ContextCompat  FlutterInjector 
io.flutter  
flutterLoader io.flutter.FlutterInjector  instance io.flutter.FlutterInjector  
FlutterLoader "io.flutter.embedding.engine.loader  getLookupKeyForAsset 0io.flutter.embedding.engine.loader.FlutterLoader  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  applicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  BinaryMessenger io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  argument #io.flutter.plugin.common.MethodCall  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  Activity *io.github.ponnamkarthik.toast.fluttertoast  Any *io.github.ponnamkarthik.toast.fluttertoast  AssetManager *io.github.ponnamkarthik.toast.fluttertoast  BinaryMessenger *io.github.ponnamkarthik.toast.fluttertoast  Build *io.github.ponnamkarthik.toast.fluttertoast  Context *io.github.ponnamkarthik.toast.fluttertoast  
ContextCompat *io.github.ponnamkarthik.toast.fluttertoast  Drawable *io.github.ponnamkarthik.toast.fluttertoast  	Exception *io.github.ponnamkarthik.toast.fluttertoast  FlutterInjector *io.github.ponnamkarthik.toast.fluttertoast  
FlutterPlugin *io.github.ponnamkarthik.toast.fluttertoast  FlutterToastPlugin *io.github.ponnamkarthik.toast.fluttertoast  Gravity *io.github.ponnamkarthik.toast.fluttertoast  Int *io.github.ponnamkarthik.toast.fluttertoast  LayoutInflater *io.github.ponnamkarthik.toast.fluttertoast  Log *io.github.ponnamkarthik.toast.fluttertoast  
MethodCall *io.github.ponnamkarthik.toast.fluttertoast  MethodCallHandler *io.github.ponnamkarthik.toast.fluttertoast  MethodCallHandlerImpl *io.github.ponnamkarthik.toast.fluttertoast  
MethodChannel *io.github.ponnamkarthik.toast.fluttertoast  Number *io.github.ponnamkarthik.toast.fluttertoast  
PorterDuff *io.github.ponnamkarthik.toast.fluttertoast  R *io.github.ponnamkarthik.toast.fluttertoast  String *io.github.ponnamkarthik.toast.fluttertoast  TextView *io.github.ponnamkarthik.toast.fluttertoast  Toast *io.github.ponnamkarthik.toast.fluttertoast  Typeface *io.github.ponnamkarthik.toast.fluttertoast  android *io.github.ponnamkarthik.toast.fluttertoast  mToast *io.github.ponnamkarthik.toast.fluttertoast  toString *io.github.ponnamkarthik.toast.fluttertoast  FlutterPluginBinding 8io.github.ponnamkarthik.toast.fluttertoast.FlutterPlugin  MethodCallHandlerImpl =io.github.ponnamkarthik.toast.fluttertoast.FlutterToastPlugin  
MethodChannel =io.github.ponnamkarthik.toast.fluttertoast.FlutterToastPlugin  channel =io.github.ponnamkarthik.toast.fluttertoast.FlutterToastPlugin  setupChannel =io.github.ponnamkarthik.toast.fluttertoast.FlutterToastPlugin  teardownChannel =io.github.ponnamkarthik.toast.fluttertoast.FlutterToastPlugin  Build @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  Context @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  
ContextCompat @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  FlutterInjector @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  Gravity @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  Log @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  
PorterDuff @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  R @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  Toast @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  Typeface @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  android @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  context @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  mToast @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  toString @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  Result 8io.github.ponnamkarthik.toast.fluttertoast.MethodChannel  corner 5io.github.ponnamkarthik.toast.fluttertoast.R.drawable  text /io.github.ponnamkarthik.toast.fluttertoast.R.id  toast_custom 3io.github.ponnamkarthik.toast.fluttertoast.R.layout  Callback 0io.github.ponnamkarthik.toast.fluttertoast.Toast  	Exception 	java.lang  Runnable 	java.lang  <SAM-CONSTRUCTOR> java.lang.Runnable  	Function0 kotlin  Number kotlin  toString kotlin  toString 
kotlin.Any  	compareTo 
kotlin.Int  toFloat 
kotlin.Number  toInt 
kotlin.Number  toString kotlin.collections  toString kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         