  SuppressLint android.annotation  Notification android.app  	javaClass android.app.Notification  AsyncQueryHandler android.content  
ComponentName android.content  
ContentValues android.content  Context android.content  Intent android.content  startInsert !android.content.AsyncQueryHandler  	className android.content.ComponentName  flattenToShortString android.content.ComponentName  flattenToString android.content.ComponentName  packageName android.content.ComponentName  call android.content.ContentResolver  insert android.content.ContentResolver  put android.content.ContentValues  applicationContext android.content.Context  contentResolver android.content.Context  let android.content.Context  packageManager android.content.Context  packageName android.content.Context  
sendBroadcast android.content.Context  ACTION_MAIN android.content.Intent  
CATEGORY_HOME android.content.Intent  action android.content.Intent  addCategory android.content.Intent  	component android.content.Intent  putExtra android.content.Intent  
setPackage android.content.Intent  ActivityInfo android.content.pm  PackageManager android.content.pm  ProviderInfo android.content.pm  ResolveInfo android.content.pm  packageName android.content.pm.ActivityInfo  packageName "android.content.pm.PackageItemInfo  MATCH_DEFAULT_ONLY !android.content.pm.PackageManager  getLaunchIntentForPackage !android.content.pm.PackageManager  queryBroadcastReceivers !android.content.pm.PackageManager  queryIntentActivities !android.content.pm.PackageManager  resolveActivity !android.content.pm.PackageManager  resolveContentProvider !android.content.pm.PackageManager  activityInfo android.content.pm.ResolveInfo  resolvePackageName android.content.pm.ResolveInfo  Uri android.net  parse android.net.Uri  Build 
android.os  Bundle 
android.os  Looper 
android.os  putInt android.os.BaseBundle  	putString android.os.BaseBundle  SDK_INT android.os.Build.VERSION  O android.os.Build.VERSION_CODES  putInt android.os.Bundle  	putString android.os.Bundle  
getMainLooper android.os.Looper  myLooper android.os.Looper  Log android.util  e android.util.Log  i android.util.Log  Keep androidx.annotation  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  applicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  BinaryMessenger io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  	arguments #io.flutter.plugin.common.MethodCall  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  Class 	java.lang  	Exception 	java.lang  getDeclaredConstructor java.lang.Class  getDeclaredField java.lang.Class  getDeclaredMethod java.lang.Class  valueOf java.lang.String  newInstance java.lang.reflect.Constructor  get java.lang.reflect.Field  invoke java.lang.reflect.Method  
BigDecimal 	java.math  
BigInteger 	java.math  Collections 	java.util  swap java.util.Collections  Array kotlin  BooleanArray kotlin  	ByteArray kotlin  	CharArray kotlin  DoubleArray kotlin  	Exception kotlin  
FloatArray kotlin  	Function0 kotlin  	Function1 kotlin  Int kotlin  IntArray kotlin  	LongArray kotlin  Nothing kotlin  
ShortArray kotlin  	Throwable kotlin  
UByteArray kotlin  	UIntArray kotlin  
ULongArray kotlin  UShortArray kotlin  let kotlin  plus kotlin  synchronized kotlin  not kotlin.Boolean  	Companion 
kotlin.Int  	compareTo 
kotlin.Int  plus 
kotlin.Int  toString 
kotlin.Int  plus 
kotlin.String  IntIterator kotlin.collections  Iterator kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableIterator kotlin.collections  MutableList kotlin.collections  Set kotlin.collections  contains kotlin.collections  	emptyList kotlin.collections  get kotlin.collections  indices kotlin.collections  listOf kotlin.collections  
mutableListOf kotlin.collections  plus kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  contains kotlin.collections.List  get kotlin.collections.List  indices kotlin.collections.List  isEmpty kotlin.collections.List  iterator kotlin.collections.List  get kotlin.collections.Map  hasNext "kotlin.collections.MutableIterator  next "kotlin.collections.MutableIterator  add kotlin.collections.MutableList  iterator kotlin.collections.MutableList  Throws 
kotlin.jvm  Volatile 
kotlin.jvm  java 
kotlin.jvm  	javaClass 
kotlin.jvm  javaPrimitiveType 
kotlin.jvm  	CharRange 
kotlin.ranges  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  	UIntRange 
kotlin.ranges  
ULongRange 
kotlin.ranges  contains 
kotlin.ranges  until 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  KClass kotlin.reflect  java kotlin.reflect.KClass  javaPrimitiveType kotlin.reflect.KClass  Sequence kotlin.sequences  contains kotlin.sequences  plus kotlin.sequences  
MatchGroup kotlin.text  contains kotlin.text  get kotlin.text  indices kotlin.text  plus kotlin.text  AppBadgePlusPlugin me.liolin.app_badge_plus  Badge me.liolin.app_badge_plus  Context me.liolin.app_badge_plus  
FlutterPlugin me.liolin.app_badge_plus  Int me.liolin.app_badge_plus  Keep me.liolin.app_badge_plus  Map me.liolin.app_badge_plus  
MethodCall me.liolin.app_badge_plus  MethodCallHandler me.liolin.app_badge_plus  
MethodChannel me.liolin.app_badge_plus  Result me.liolin.app_badge_plus  get me.liolin.app_badge_plus  isBadgeSupported me.liolin.app_badge_plus  let me.liolin.app_badge_plus  updateBadge me.liolin.app_badge_plus  Badge +me.liolin.app_badge_plus.AppBadgePlusPlugin  
MethodChannel +me.liolin.app_badge_plus.AppBadgePlusPlugin  channel +me.liolin.app_badge_plus.AppBadgePlusPlugin  context +me.liolin.app_badge_plus.AppBadgePlusPlugin  get +me.liolin.app_badge_plus.AppBadgePlusPlugin  isBadgeSupported +me.liolin.app_badge_plus.AppBadgePlusPlugin  let +me.liolin.app_badge_plus.AppBadgePlusPlugin  updateBadge +me.liolin.app_badge_plus.AppBadgePlusPlugin  FlutterPluginBinding &me.liolin.app_badge_plus.FlutterPlugin  Any me.liolin.app_badge_plus.badge  ApexLauncherBadge me.liolin.app_badge_plus.badge  AsusLauncherBadge me.liolin.app_badge_plus.badge  Badge me.liolin.app_badge_plus.badge  BadgeException me.liolin.app_badge_plus.badge  Boolean me.liolin.app_badge_plus.badge  Class me.liolin.app_badge_plus.badge  Context me.liolin.app_badge_plus.badge  DefaultBadge me.liolin.app_badge_plus.badge  	Exception me.liolin.app_badge_plus.badge  HtcLauncherBadge me.liolin.app_badge_plus.badge  HuaweiLauncherBadge me.liolin.app_badge_plus.badge  IBadge me.liolin.app_badge_plus.badge  Int me.liolin.app_badge_plus.badge  Intent me.liolin.app_badge_plus.badge  Keep me.liolin.app_badge_plus.badge  LGLauncherBadge me.liolin.app_badge_plus.badge  LauncherTool me.liolin.app_badge_plus.badge  List me.liolin.app_badge_plus.badge  Log me.liolin.app_badge_plus.badge  	MiUIBadge me.liolin.app_badge_plus.badge  MutableList me.liolin.app_badge_plus.badge  Notification me.liolin.app_badge_plus.badge  NowaLauncherBadge me.liolin.app_badge_plus.badge  OPPOLauncherBadge me.liolin.app_badge_plus.badge  ResolveInfo me.liolin.app_badge_plus.badge  SamsungLauncherBadge me.liolin.app_badge_plus.badge  SonyLauncherBadge me.liolin.app_badge_plus.badge  String me.liolin.app_badge_plus.badge  	Throwable me.liolin.app_badge_plus.badge  Throws me.liolin.app_badge_plus.badge  VivoLauncherBadge me.liolin.app_badge_plus.badge  Volatile me.liolin.app_badge_plus.badge  YandexLauncherBadge me.liolin.app_badge_plus.badge  ZTELauncherBadge me.liolin.app_badge_plus.badge  contains me.liolin.app_badge_plus.badge  getLauncherList me.liolin.app_badge_plus.badge  java me.liolin.app_badge_plus.badge  
mutableListOf me.liolin.app_badge_plus.badge  synchronized me.liolin.app_badge_plus.badge  until me.liolin.app_badge_plus.badge  Any $me.liolin.app_badge_plus.badge.Badge  ApexLauncherBadge $me.liolin.app_badge_plus.badge.Badge  AsusLauncherBadge $me.liolin.app_badge_plus.badge.Badge  BADGES $me.liolin.app_badge_plus.badge.Badge  BadgeException $me.liolin.app_badge_plus.badge.Badge  DefaultBadge $me.liolin.app_badge_plus.badge.Badge  HtcLauncherBadge $me.liolin.app_badge_plus.badge.Badge  HuaweiLauncherBadge $me.liolin.app_badge_plus.badge.Badge  LGLauncherBadge $me.liolin.app_badge_plus.badge.Badge  LauncherTool $me.liolin.app_badge_plus.badge.Badge  Log $me.liolin.app_badge_plus.badge.Badge  	MiUIBadge $me.liolin.app_badge_plus.badge.Badge  NowaLauncherBadge $me.liolin.app_badge_plus.badge.Badge  OPPOLauncherBadge $me.liolin.app_badge_plus.badge.Badge  SamsungLauncherBadge $me.liolin.app_badge_plus.badge.Badge  SonyLauncherBadge $me.liolin.app_badge_plus.badge.Badge  TAG $me.liolin.app_badge_plus.badge.Badge  VivoLauncherBadge $me.liolin.app_badge_plus.badge.Badge  YandexLauncherBadge $me.liolin.app_badge_plus.badge.Badge  ZTELauncherBadge $me.liolin.app_badge_plus.badge.Badge  badgeSupportedLock $me.liolin.app_badge_plus.badge.Badge  contains $me.liolin.app_badge_plus.badge.Badge  getLauncherList $me.liolin.app_badge_plus.badge.Badge  iBadge $me.liolin.app_badge_plus.badge.Badge  	initBadge $me.liolin.app_badge_plus.badge.Badge  isBadgeSupported $me.liolin.app_badge_plus.badge.Badge  java $me.liolin.app_badge_plus.badge.Badge  
mutableListOf $me.liolin.app_badge_plus.badge.Badge  notification $me.liolin.app_badge_plus.badge.Badge  synchronized $me.liolin.app_badge_plus.badge.Badge  until $me.liolin.app_badge_plus.badge.Badge  updateBadge $me.liolin.app_badge_plus.badge.Badge  updateBadgeOrThrow $me.liolin.app_badge_plus.badge.Badge  getSupportLaunchers %me.liolin.app_badge_plus.badge.IBadge  updateBadge %me.liolin.app_badge_plus.badge.IBadge  ApexLauncherBadge me.liolin.app_badge_plus.impl  AsusLauncherBadge me.liolin.app_badge_plus.impl  AsyncQueryHandler me.liolin.app_badge_plus.impl  BADGE_CONTENT_URI me.liolin.app_badge_plus.impl  Badge me.liolin.app_badge_plus.impl  Boolean me.liolin.app_badge_plus.impl  
BroadcastTool me.liolin.app_badge_plus.impl  Bundle me.liolin.app_badge_plus.impl  
CLASS_NAME me.liolin.app_badge_plus.impl  COLUMN_BADGES_COUNT me.liolin.app_badge_plus.impl  COLUMN_CLASS me.liolin.app_badge_plus.impl  COLUMN_PACKAGE me.liolin.app_badge_plus.impl  CONTENT_URI me.liolin.app_badge_plus.impl  COUNT me.liolin.app_badge_plus.impl  
ComponentName me.liolin.app_badge_plus.impl  
ContentValues me.liolin.app_badge_plus.impl  Context me.liolin.app_badge_plus.impl  DefaultBadge me.liolin.app_badge_plus.impl  EXTRA_BADGE_COMPONENT me.liolin.app_badge_plus.impl  EXTRA_BADGE_COUNT me.liolin.app_badge_plus.impl  EXTRA_COMPONENT me.liolin.app_badge_plus.impl  EXTRA_COUNT me.liolin.app_badge_plus.impl  	Exception me.liolin.app_badge_plus.impl  HtcLauncherBadge me.liolin.app_badge_plus.impl  HuaweiLauncherBadge me.liolin.app_badge_plus.impl  IBadge me.liolin.app_badge_plus.impl  
INTENT_ACTION me.liolin.app_badge_plus.impl  INTENT_EXTRA_ACTIVITY_NAME me.liolin.app_badge_plus.impl  INTENT_EXTRA_BADGE_COUNT me.liolin.app_badge_plus.impl   INTENT_EXTRA_BADGE_UPGRADE_COUNT me.liolin.app_badge_plus.impl  INTENT_EXTRA_CLASS_NAME me.liolin.app_badge_plus.impl  INTENT_EXTRA_MESSAGE me.liolin.app_badge_plus.impl  INTENT_EXTRA_PACKAGE_NAME me.liolin.app_badge_plus.impl  INTENT_EXTRA_SHOW_MESSAGE me.liolin.app_badge_plus.impl  INTENT_SET_NOTIFICATION me.liolin.app_badge_plus.impl  INTENT_UPDATE_COUNTER me.liolin.app_badge_plus.impl  INTENT_UPDATE_SHORTCUT me.liolin.app_badge_plus.impl  Int me.liolin.app_badge_plus.impl  Intent me.liolin.app_badge_plus.impl  Keep me.liolin.app_badge_plus.impl  LGLauncherBadge me.liolin.app_badge_plus.impl  LauncherTool me.liolin.app_badge_plus.impl  List me.liolin.app_badge_plus.impl  Log me.liolin.app_badge_plus.impl  Looper me.liolin.app_badge_plus.impl  METHOD_TO_CALL me.liolin.app_badge_plus.impl  	MiUIBadge me.liolin.app_badge_plus.impl  Notification me.liolin.app_badge_plus.impl  NowaLauncherBadge me.liolin.app_badge_plus.impl  OPPOLauncherBadge me.liolin.app_badge_plus.impl  PACKAGE_NAME me.liolin.app_badge_plus.impl  PROVIDER_COLUMNS_ACTIVITY_NAME me.liolin.app_badge_plus.impl  PROVIDER_COLUMNS_BADGE_COUNT me.liolin.app_badge_plus.impl  PROVIDER_COLUMNS_PACKAGE_NAME me.liolin.app_badge_plus.impl  PROVIDER_CONTENT_URI me.liolin.app_badge_plus.impl  SONY_HOME_PROVIDER_NAME me.liolin.app_badge_plus.impl  SamsungLauncherBadge me.liolin.app_badge_plus.impl  SonyLauncherBadge me.liolin.app_badge_plus.impl  String me.liolin.app_badge_plus.impl  SuppressLint me.liolin.app_badge_plus.impl  	Throwable me.liolin.app_badge_plus.impl  Throws me.liolin.app_badge_plus.impl  Uri me.liolin.app_badge_plus.impl  VivoLauncherBadge me.liolin.app_badge_plus.impl  YandexLauncherBadge me.liolin.app_badge_plus.impl  ZTELauncherBadge me.liolin.app_badge_plus.impl  	emptyList me.liolin.app_badge_plus.impl  getClassName me.liolin.app_badge_plus.impl  getComponentName me.liolin.app_badge_plus.impl  java me.liolin.app_badge_plus.impl  	javaClass me.liolin.app_badge_plus.impl  javaPrimitiveType me.liolin.app_badge_plus.impl  listOf me.liolin.app_badge_plus.impl  plus me.liolin.app_badge_plus.impl  
sendBroadcast me.liolin.app_badge_plus.impl  sendDefaultBroadcast me.liolin.app_badge_plus.impl  
BroadcastTool /me.liolin.app_badge_plus.impl.ApexLauncherBadge  
CLASS_NAME /me.liolin.app_badge_plus.impl.ApexLauncherBadge  COUNT /me.liolin.app_badge_plus.impl.ApexLauncherBadge  	Companion /me.liolin.app_badge_plus.impl.ApexLauncherBadge  Context /me.liolin.app_badge_plus.impl.ApexLauncherBadge  INTENT_UPDATE_COUNTER /me.liolin.app_badge_plus.impl.ApexLauncherBadge  Int /me.liolin.app_badge_plus.impl.ApexLauncherBadge  Intent /me.liolin.app_badge_plus.impl.ApexLauncherBadge  LauncherTool /me.liolin.app_badge_plus.impl.ApexLauncherBadge  List /me.liolin.app_badge_plus.impl.ApexLauncherBadge  PACKAGE_NAME /me.liolin.app_badge_plus.impl.ApexLauncherBadge  String /me.liolin.app_badge_plus.impl.ApexLauncherBadge  getComponentName /me.liolin.app_badge_plus.impl.ApexLauncherBadge  listOf /me.liolin.app_badge_plus.impl.ApexLauncherBadge  
sendBroadcast /me.liolin.app_badge_plus.impl.ApexLauncherBadge  
BroadcastTool 9me.liolin.app_badge_plus.impl.ApexLauncherBadge.Companion  
CLASS_NAME 9me.liolin.app_badge_plus.impl.ApexLauncherBadge.Companion  COUNT 9me.liolin.app_badge_plus.impl.ApexLauncherBadge.Companion  INTENT_UPDATE_COUNTER 9me.liolin.app_badge_plus.impl.ApexLauncherBadge.Companion  Intent 9me.liolin.app_badge_plus.impl.ApexLauncherBadge.Companion  LauncherTool 9me.liolin.app_badge_plus.impl.ApexLauncherBadge.Companion  PACKAGE_NAME 9me.liolin.app_badge_plus.impl.ApexLauncherBadge.Companion  getComponentName 9me.liolin.app_badge_plus.impl.ApexLauncherBadge.Companion  listOf 9me.liolin.app_badge_plus.impl.ApexLauncherBadge.Companion  
sendBroadcast 9me.liolin.app_badge_plus.impl.ApexLauncherBadge.Companion  	Companion /me.liolin.app_badge_plus.impl.AsusLauncherBadge  Context /me.liolin.app_badge_plus.impl.AsusLauncherBadge  
INTENT_ACTION /me.liolin.app_badge_plus.impl.AsusLauncherBadge  INTENT_EXTRA_ACTIVITY_NAME /me.liolin.app_badge_plus.impl.AsusLauncherBadge  INTENT_EXTRA_BADGE_COUNT /me.liolin.app_badge_plus.impl.AsusLauncherBadge  INTENT_EXTRA_PACKAGE_NAME /me.liolin.app_badge_plus.impl.AsusLauncherBadge  Int /me.liolin.app_badge_plus.impl.AsusLauncherBadge  Intent /me.liolin.app_badge_plus.impl.AsusLauncherBadge  LauncherTool /me.liolin.app_badge_plus.impl.AsusLauncherBadge  List /me.liolin.app_badge_plus.impl.AsusLauncherBadge  String /me.liolin.app_badge_plus.impl.AsusLauncherBadge  getComponentName /me.liolin.app_badge_plus.impl.AsusLauncherBadge  listOf /me.liolin.app_badge_plus.impl.AsusLauncherBadge  
INTENT_ACTION 9me.liolin.app_badge_plus.impl.AsusLauncherBadge.Companion  INTENT_EXTRA_ACTIVITY_NAME 9me.liolin.app_badge_plus.impl.AsusLauncherBadge.Companion  INTENT_EXTRA_BADGE_COUNT 9me.liolin.app_badge_plus.impl.AsusLauncherBadge.Companion  INTENT_EXTRA_PACKAGE_NAME 9me.liolin.app_badge_plus.impl.AsusLauncherBadge.Companion  Intent 9me.liolin.app_badge_plus.impl.AsusLauncherBadge.Companion  LauncherTool 9me.liolin.app_badge_plus.impl.AsusLauncherBadge.Companion  getComponentName 9me.liolin.app_badge_plus.impl.AsusLauncherBadge.Companion  listOf 9me.liolin.app_badge_plus.impl.AsusLauncherBadge.Companion  	emptyList *me.liolin.app_badge_plus.impl.DefaultBadge  
BroadcastTool .me.liolin.app_badge_plus.impl.HtcLauncherBadge  COUNT .me.liolin.app_badge_plus.impl.HtcLauncherBadge  	Companion .me.liolin.app_badge_plus.impl.HtcLauncherBadge  Context .me.liolin.app_badge_plus.impl.HtcLauncherBadge  EXTRA_COMPONENT .me.liolin.app_badge_plus.impl.HtcLauncherBadge  EXTRA_COUNT .me.liolin.app_badge_plus.impl.HtcLauncherBadge  	Exception .me.liolin.app_badge_plus.impl.HtcLauncherBadge  INTENT_SET_NOTIFICATION .me.liolin.app_badge_plus.impl.HtcLauncherBadge  INTENT_UPDATE_SHORTCUT .me.liolin.app_badge_plus.impl.HtcLauncherBadge  Int .me.liolin.app_badge_plus.impl.HtcLauncherBadge  Intent .me.liolin.app_badge_plus.impl.HtcLauncherBadge  LauncherTool .me.liolin.app_badge_plus.impl.HtcLauncherBadge  List .me.liolin.app_badge_plus.impl.HtcLauncherBadge  Log .me.liolin.app_badge_plus.impl.HtcLauncherBadge  PACKAGE_NAME .me.liolin.app_badge_plus.impl.HtcLauncherBadge  String .me.liolin.app_badge_plus.impl.HtcLauncherBadge  getComponentName .me.liolin.app_badge_plus.impl.HtcLauncherBadge  listOf .me.liolin.app_badge_plus.impl.HtcLauncherBadge  
sendBroadcast .me.liolin.app_badge_plus.impl.HtcLauncherBadge  
BroadcastTool 8me.liolin.app_badge_plus.impl.HtcLauncherBadge.Companion  COUNT 8me.liolin.app_badge_plus.impl.HtcLauncherBadge.Companion  EXTRA_COMPONENT 8me.liolin.app_badge_plus.impl.HtcLauncherBadge.Companion  EXTRA_COUNT 8me.liolin.app_badge_plus.impl.HtcLauncherBadge.Companion  INTENT_SET_NOTIFICATION 8me.liolin.app_badge_plus.impl.HtcLauncherBadge.Companion  INTENT_UPDATE_SHORTCUT 8me.liolin.app_badge_plus.impl.HtcLauncherBadge.Companion  Intent 8me.liolin.app_badge_plus.impl.HtcLauncherBadge.Companion  LauncherTool 8me.liolin.app_badge_plus.impl.HtcLauncherBadge.Companion  Log 8me.liolin.app_badge_plus.impl.HtcLauncherBadge.Companion  PACKAGE_NAME 8me.liolin.app_badge_plus.impl.HtcLauncherBadge.Companion  getComponentName 8me.liolin.app_badge_plus.impl.HtcLauncherBadge.Companion  listOf 8me.liolin.app_badge_plus.impl.HtcLauncherBadge.Companion  
sendBroadcast 8me.liolin.app_badge_plus.impl.HtcLauncherBadge.Companion  Bundle 1me.liolin.app_badge_plus.impl.HuaweiLauncherBadge  LauncherTool 1me.liolin.app_badge_plus.impl.HuaweiLauncherBadge  Uri 1me.liolin.app_badge_plus.impl.HuaweiLauncherBadge  getClassName 1me.liolin.app_badge_plus.impl.HuaweiLauncherBadge  listOf 1me.liolin.app_badge_plus.impl.HuaweiLauncherBadge  
BroadcastTool -me.liolin.app_badge_plus.impl.LGLauncherBadge  	Companion -me.liolin.app_badge_plus.impl.LGLauncherBadge  Context -me.liolin.app_badge_plus.impl.LGLauncherBadge  
INTENT_ACTION -me.liolin.app_badge_plus.impl.LGLauncherBadge  INTENT_EXTRA_BADGE_COUNT -me.liolin.app_badge_plus.impl.LGLauncherBadge  INTENT_EXTRA_CLASS_NAME -me.liolin.app_badge_plus.impl.LGLauncherBadge  INTENT_EXTRA_PACKAGE_NAME -me.liolin.app_badge_plus.impl.LGLauncherBadge  Int -me.liolin.app_badge_plus.impl.LGLauncherBadge  Intent -me.liolin.app_badge_plus.impl.LGLauncherBadge  LauncherTool -me.liolin.app_badge_plus.impl.LGLauncherBadge  List -me.liolin.app_badge_plus.impl.LGLauncherBadge  String -me.liolin.app_badge_plus.impl.LGLauncherBadge  getComponentName -me.liolin.app_badge_plus.impl.LGLauncherBadge  listOf -me.liolin.app_badge_plus.impl.LGLauncherBadge  sendDefaultBroadcast -me.liolin.app_badge_plus.impl.LGLauncherBadge  
BroadcastTool 7me.liolin.app_badge_plus.impl.LGLauncherBadge.Companion  
INTENT_ACTION 7me.liolin.app_badge_plus.impl.LGLauncherBadge.Companion  INTENT_EXTRA_BADGE_COUNT 7me.liolin.app_badge_plus.impl.LGLauncherBadge.Companion  INTENT_EXTRA_CLASS_NAME 7me.liolin.app_badge_plus.impl.LGLauncherBadge.Companion  INTENT_EXTRA_PACKAGE_NAME 7me.liolin.app_badge_plus.impl.LGLauncherBadge.Companion  Intent 7me.liolin.app_badge_plus.impl.LGLauncherBadge.Companion  LauncherTool 7me.liolin.app_badge_plus.impl.LGLauncherBadge.Companion  getComponentName 7me.liolin.app_badge_plus.impl.LGLauncherBadge.Companion  listOf 7me.liolin.app_badge_plus.impl.LGLauncherBadge.Companion  sendDefaultBroadcast 7me.liolin.app_badge_plus.impl.LGLauncherBadge.Companion  Badge 'me.liolin.app_badge_plus.impl.MiUIBadge  Int 'me.liolin.app_badge_plus.impl.MiUIBadge  Log 'me.liolin.app_badge_plus.impl.MiUIBadge  	javaClass 'me.liolin.app_badge_plus.impl.MiUIBadge  javaPrimitiveType 'me.liolin.app_badge_plus.impl.MiUIBadge  listOf 'me.liolin.app_badge_plus.impl.MiUIBadge  notificationBadge 'me.liolin.app_badge_plus.impl.MiUIBadge  CONTENT_URI /me.liolin.app_badge_plus.impl.NowaLauncherBadge  COUNT /me.liolin.app_badge_plus.impl.NowaLauncherBadge  	Companion /me.liolin.app_badge_plus.impl.NowaLauncherBadge  
ContentValues /me.liolin.app_badge_plus.impl.NowaLauncherBadge  Context /me.liolin.app_badge_plus.impl.NowaLauncherBadge  Int /me.liolin.app_badge_plus.impl.NowaLauncherBadge  LauncherTool /me.liolin.app_badge_plus.impl.NowaLauncherBadge  List /me.liolin.app_badge_plus.impl.NowaLauncherBadge  String /me.liolin.app_badge_plus.impl.NowaLauncherBadge  Uri /me.liolin.app_badge_plus.impl.NowaLauncherBadge  getComponentName /me.liolin.app_badge_plus.impl.NowaLauncherBadge  listOf /me.liolin.app_badge_plus.impl.NowaLauncherBadge  plus /me.liolin.app_badge_plus.impl.NowaLauncherBadge  CONTENT_URI 9me.liolin.app_badge_plus.impl.NowaLauncherBadge.Companion  COUNT 9me.liolin.app_badge_plus.impl.NowaLauncherBadge.Companion  
ContentValues 9me.liolin.app_badge_plus.impl.NowaLauncherBadge.Companion  LauncherTool 9me.liolin.app_badge_plus.impl.NowaLauncherBadge.Companion  Uri 9me.liolin.app_badge_plus.impl.NowaLauncherBadge.Companion  getComponentName 9me.liolin.app_badge_plus.impl.NowaLauncherBadge.Companion  listOf 9me.liolin.app_badge_plus.impl.NowaLauncherBadge.Companion  plus 9me.liolin.app_badge_plus.impl.NowaLauncherBadge.Companion  Bundle /me.liolin.app_badge_plus.impl.OPPOLauncherBadge  	Companion /me.liolin.app_badge_plus.impl.OPPOLauncherBadge  Context /me.liolin.app_badge_plus.impl.OPPOLauncherBadge  	Exception /me.liolin.app_badge_plus.impl.OPPOLauncherBadge   INTENT_EXTRA_BADGE_UPGRADE_COUNT /me.liolin.app_badge_plus.impl.OPPOLauncherBadge  Int /me.liolin.app_badge_plus.impl.OPPOLauncherBadge  List /me.liolin.app_badge_plus.impl.OPPOLauncherBadge  Log /me.liolin.app_badge_plus.impl.OPPOLauncherBadge  PROVIDER_CONTENT_URI /me.liolin.app_badge_plus.impl.OPPOLauncherBadge  String /me.liolin.app_badge_plus.impl.OPPOLauncherBadge  	Throwable /me.liolin.app_badge_plus.impl.OPPOLauncherBadge  Throws /me.liolin.app_badge_plus.impl.OPPOLauncherBadge  Uri /me.liolin.app_badge_plus.impl.OPPOLauncherBadge  executeBadgeByContentProvider /me.liolin.app_badge_plus.impl.OPPOLauncherBadge  listOf /me.liolin.app_badge_plus.impl.OPPOLauncherBadge  Bundle 9me.liolin.app_badge_plus.impl.OPPOLauncherBadge.Companion  	Exception 9me.liolin.app_badge_plus.impl.OPPOLauncherBadge.Companion   INTENT_EXTRA_BADGE_UPGRADE_COUNT 9me.liolin.app_badge_plus.impl.OPPOLauncherBadge.Companion  Log 9me.liolin.app_badge_plus.impl.OPPOLauncherBadge.Companion  PROVIDER_CONTENT_URI 9me.liolin.app_badge_plus.impl.OPPOLauncherBadge.Companion  Uri 9me.liolin.app_badge_plus.impl.OPPOLauncherBadge.Companion  listOf 9me.liolin.app_badge_plus.impl.OPPOLauncherBadge.Companion  	Companion 2me.liolin.app_badge_plus.impl.SamsungLauncherBadge  Context 2me.liolin.app_badge_plus.impl.SamsungLauncherBadge  
INTENT_ACTION 2me.liolin.app_badge_plus.impl.SamsungLauncherBadge  INTENT_EXTRA_BADGE_COUNT 2me.liolin.app_badge_plus.impl.SamsungLauncherBadge  INTENT_EXTRA_CLASS_NAME 2me.liolin.app_badge_plus.impl.SamsungLauncherBadge  INTENT_EXTRA_PACKAGE_NAME 2me.liolin.app_badge_plus.impl.SamsungLauncherBadge  Int 2me.liolin.app_badge_plus.impl.SamsungLauncherBadge  Intent 2me.liolin.app_badge_plus.impl.SamsungLauncherBadge  LauncherTool 2me.liolin.app_badge_plus.impl.SamsungLauncherBadge  List 2me.liolin.app_badge_plus.impl.SamsungLauncherBadge  String 2me.liolin.app_badge_plus.impl.SamsungLauncherBadge  getClassName 2me.liolin.app_badge_plus.impl.SamsungLauncherBadge  listOf 2me.liolin.app_badge_plus.impl.SamsungLauncherBadge  
INTENT_ACTION <me.liolin.app_badge_plus.impl.SamsungLauncherBadge.Companion  INTENT_EXTRA_BADGE_COUNT <me.liolin.app_badge_plus.impl.SamsungLauncherBadge.Companion  INTENT_EXTRA_CLASS_NAME <me.liolin.app_badge_plus.impl.SamsungLauncherBadge.Companion  INTENT_EXTRA_PACKAGE_NAME <me.liolin.app_badge_plus.impl.SamsungLauncherBadge.Companion  Intent <me.liolin.app_badge_plus.impl.SamsungLauncherBadge.Companion  LauncherTool <me.liolin.app_badge_plus.impl.SamsungLauncherBadge.Companion  getClassName <me.liolin.app_badge_plus.impl.SamsungLauncherBadge.Companion  listOf <me.liolin.app_badge_plus.impl.SamsungLauncherBadge.Companion  AsyncQueryHandler /me.liolin.app_badge_plus.impl.SonyLauncherBadge  BADGE_CONTENT_URI /me.liolin.app_badge_plus.impl.SonyLauncherBadge  Boolean /me.liolin.app_badge_plus.impl.SonyLauncherBadge  	Companion /me.liolin.app_badge_plus.impl.SonyLauncherBadge  
ComponentName /me.liolin.app_badge_plus.impl.SonyLauncherBadge  
ContentValues /me.liolin.app_badge_plus.impl.SonyLauncherBadge  Context /me.liolin.app_badge_plus.impl.SonyLauncherBadge  
INTENT_ACTION /me.liolin.app_badge_plus.impl.SonyLauncherBadge  INTENT_EXTRA_ACTIVITY_NAME /me.liolin.app_badge_plus.impl.SonyLauncherBadge  INTENT_EXTRA_MESSAGE /me.liolin.app_badge_plus.impl.SonyLauncherBadge  INTENT_EXTRA_PACKAGE_NAME /me.liolin.app_badge_plus.impl.SonyLauncherBadge  INTENT_EXTRA_SHOW_MESSAGE /me.liolin.app_badge_plus.impl.SonyLauncherBadge  Int /me.liolin.app_badge_plus.impl.SonyLauncherBadge  Intent /me.liolin.app_badge_plus.impl.SonyLauncherBadge  LauncherTool /me.liolin.app_badge_plus.impl.SonyLauncherBadge  List /me.liolin.app_badge_plus.impl.SonyLauncherBadge  Looper /me.liolin.app_badge_plus.impl.SonyLauncherBadge  PROVIDER_COLUMNS_ACTIVITY_NAME /me.liolin.app_badge_plus.impl.SonyLauncherBadge  PROVIDER_COLUMNS_BADGE_COUNT /me.liolin.app_badge_plus.impl.SonyLauncherBadge  PROVIDER_COLUMNS_PACKAGE_NAME /me.liolin.app_badge_plus.impl.SonyLauncherBadge  PROVIDER_CONTENT_URI /me.liolin.app_badge_plus.impl.SonyLauncherBadge  SONY_HOME_PROVIDER_NAME /me.liolin.app_badge_plus.impl.SonyLauncherBadge  String /me.liolin.app_badge_plus.impl.SonyLauncherBadge  SuppressLint /me.liolin.app_badge_plus.impl.SonyLauncherBadge  Uri /me.liolin.app_badge_plus.impl.SonyLauncherBadge  createContentValues /me.liolin.app_badge_plus.impl.SonyLauncherBadge  executeBadgeByBroadcast /me.liolin.app_badge_plus.impl.SonyLauncherBadge  executeBadgeByContentProvider /me.liolin.app_badge_plus.impl.SonyLauncherBadge  getComponentName /me.liolin.app_badge_plus.impl.SonyLauncherBadge  insertBadgeAsync /me.liolin.app_badge_plus.impl.SonyLauncherBadge  insertBadgeSync /me.liolin.app_badge_plus.impl.SonyLauncherBadge  listOf /me.liolin.app_badge_plus.impl.SonyLauncherBadge  
mQueryHandler /me.liolin.app_badge_plus.impl.SonyLauncherBadge  sonyBadgeContentProviderExists /me.liolin.app_badge_plus.impl.SonyLauncherBadge  BADGE_CONTENT_URI 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  
ContentValues 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  
INTENT_ACTION 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  INTENT_EXTRA_ACTIVITY_NAME 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  INTENT_EXTRA_MESSAGE 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  INTENT_EXTRA_PACKAGE_NAME 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  INTENT_EXTRA_SHOW_MESSAGE 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  Intent 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  LauncherTool 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  Looper 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  PROVIDER_COLUMNS_ACTIVITY_NAME 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  PROVIDER_COLUMNS_BADGE_COUNT 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  PROVIDER_COLUMNS_PACKAGE_NAME 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  PROVIDER_CONTENT_URI 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  SONY_HOME_PROVIDER_NAME 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  Uri 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  getComponentName 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  listOf 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  	Companion /me.liolin.app_badge_plus.impl.VivoLauncherBadge  Context /me.liolin.app_badge_plus.impl.VivoLauncherBadge  
INTENT_ACTION /me.liolin.app_badge_plus.impl.VivoLauncherBadge  INTENT_EXTRA_BADGE_COUNT /me.liolin.app_badge_plus.impl.VivoLauncherBadge  INTENT_EXTRA_CLASS_NAME /me.liolin.app_badge_plus.impl.VivoLauncherBadge  INTENT_EXTRA_PACKAGE_NAME /me.liolin.app_badge_plus.impl.VivoLauncherBadge  Int /me.liolin.app_badge_plus.impl.VivoLauncherBadge  Intent /me.liolin.app_badge_plus.impl.VivoLauncherBadge  LauncherTool /me.liolin.app_badge_plus.impl.VivoLauncherBadge  List /me.liolin.app_badge_plus.impl.VivoLauncherBadge  String /me.liolin.app_badge_plus.impl.VivoLauncherBadge  getClassName /me.liolin.app_badge_plus.impl.VivoLauncherBadge  listOf /me.liolin.app_badge_plus.impl.VivoLauncherBadge  
INTENT_ACTION 9me.liolin.app_badge_plus.impl.VivoLauncherBadge.Companion  INTENT_EXTRA_BADGE_COUNT 9me.liolin.app_badge_plus.impl.VivoLauncherBadge.Companion  INTENT_EXTRA_CLASS_NAME 9me.liolin.app_badge_plus.impl.VivoLauncherBadge.Companion  INTENT_EXTRA_PACKAGE_NAME 9me.liolin.app_badge_plus.impl.VivoLauncherBadge.Companion  Intent 9me.liolin.app_badge_plus.impl.VivoLauncherBadge.Companion  LauncherTool 9me.liolin.app_badge_plus.impl.VivoLauncherBadge.Companion  getClassName 9me.liolin.app_badge_plus.impl.VivoLauncherBadge.Companion  listOf 9me.liolin.app_badge_plus.impl.VivoLauncherBadge.Companion  	AUTHORITY 1me.liolin.app_badge_plus.impl.YandexLauncherBadge  Bundle 1me.liolin.app_badge_plus.impl.YandexLauncherBadge  COLUMN_BADGES_COUNT 1me.liolin.app_badge_plus.impl.YandexLauncherBadge  COLUMN_CLASS 1me.liolin.app_badge_plus.impl.YandexLauncherBadge  COLUMN_PACKAGE 1me.liolin.app_badge_plus.impl.YandexLauncherBadge  CONTENT_URI 1me.liolin.app_badge_plus.impl.YandexLauncherBadge  	Companion 1me.liolin.app_badge_plus.impl.YandexLauncherBadge  Context 1me.liolin.app_badge_plus.impl.YandexLauncherBadge  Int 1me.liolin.app_badge_plus.impl.YandexLauncherBadge  LauncherTool 1me.liolin.app_badge_plus.impl.YandexLauncherBadge  List 1me.liolin.app_badge_plus.impl.YandexLauncherBadge  METHOD_TO_CALL 1me.liolin.app_badge_plus.impl.YandexLauncherBadge  String 1me.liolin.app_badge_plus.impl.YandexLauncherBadge  Uri 1me.liolin.app_badge_plus.impl.YandexLauncherBadge  getClassName 1me.liolin.app_badge_plus.impl.YandexLauncherBadge  getComponentName 1me.liolin.app_badge_plus.impl.YandexLauncherBadge  java 1me.liolin.app_badge_plus.impl.YandexLauncherBadge  listOf 1me.liolin.app_badge_plus.impl.YandexLauncherBadge  	AUTHORITY ;me.liolin.app_badge_plus.impl.YandexLauncherBadge.Companion  Bundle ;me.liolin.app_badge_plus.impl.YandexLauncherBadge.Companion  COLUMN_BADGES_COUNT ;me.liolin.app_badge_plus.impl.YandexLauncherBadge.Companion  COLUMN_CLASS ;me.liolin.app_badge_plus.impl.YandexLauncherBadge.Companion  COLUMN_PACKAGE ;me.liolin.app_badge_plus.impl.YandexLauncherBadge.Companion  CONTENT_URI ;me.liolin.app_badge_plus.impl.YandexLauncherBadge.Companion  LauncherTool ;me.liolin.app_badge_plus.impl.YandexLauncherBadge.Companion  METHOD_TO_CALL ;me.liolin.app_badge_plus.impl.YandexLauncherBadge.Companion  Uri ;me.liolin.app_badge_plus.impl.YandexLauncherBadge.Companion  getClassName ;me.liolin.app_badge_plus.impl.YandexLauncherBadge.Companion  getComponentName ;me.liolin.app_badge_plus.impl.YandexLauncherBadge.Companion  java ;me.liolin.app_badge_plus.impl.YandexLauncherBadge.Companion  listOf ;me.liolin.app_badge_plus.impl.YandexLauncherBadge.Companion  Bundle .me.liolin.app_badge_plus.impl.ZTELauncherBadge  CONTENT_URI .me.liolin.app_badge_plus.impl.ZTELauncherBadge  	Companion .me.liolin.app_badge_plus.impl.ZTELauncherBadge  Context .me.liolin.app_badge_plus.impl.ZTELauncherBadge  EXTRA_BADGE_COMPONENT .me.liolin.app_badge_plus.impl.ZTELauncherBadge  EXTRA_BADGE_COUNT .me.liolin.app_badge_plus.impl.ZTELauncherBadge  Int .me.liolin.app_badge_plus.impl.ZTELauncherBadge  LauncherTool .me.liolin.app_badge_plus.impl.ZTELauncherBadge  List .me.liolin.app_badge_plus.impl.ZTELauncherBadge  String .me.liolin.app_badge_plus.impl.ZTELauncherBadge  Uri .me.liolin.app_badge_plus.impl.ZTELauncherBadge  	emptyList .me.liolin.app_badge_plus.impl.ZTELauncherBadge  getComponentName .me.liolin.app_badge_plus.impl.ZTELauncherBadge  Bundle 8me.liolin.app_badge_plus.impl.ZTELauncherBadge.Companion  CONTENT_URI 8me.liolin.app_badge_plus.impl.ZTELauncherBadge.Companion  EXTRA_BADGE_COMPONENT 8me.liolin.app_badge_plus.impl.ZTELauncherBadge.Companion  EXTRA_BADGE_COUNT 8me.liolin.app_badge_plus.impl.ZTELauncherBadge.Companion  LauncherTool 8me.liolin.app_badge_plus.impl.ZTELauncherBadge.Companion  Uri 8me.liolin.app_badge_plus.impl.ZTELauncherBadge.Companion  	emptyList 8me.liolin.app_badge_plus.impl.ZTELauncherBadge.Companion  getComponentName 8me.liolin.app_badge_plus.impl.ZTELauncherBadge.Companion  BadgeException me.liolin.app_badge_plus.util  
BroadcastTool me.liolin.app_badge_plus.util  Build me.liolin.app_badge_plus.util  Collections me.liolin.app_badge_plus.util  
ComponentName me.liolin.app_badge_plus.util  Context me.liolin.app_badge_plus.util  	Exception me.liolin.app_badge_plus.util  Intent me.liolin.app_badge_plus.util  Keep me.liolin.app_badge_plus.util  LauncherTool me.liolin.app_badge_plus.util  List me.liolin.app_badge_plus.util  Log me.liolin.app_badge_plus.util  PackageManager me.liolin.app_badge_plus.util  ResolveInfo me.liolin.app_badge_plus.util  String me.liolin.app_badge_plus.util  Throws me.liolin.app_badge_plus.util  indices me.liolin.app_badge_plus.util  BadgeException +me.liolin.app_badge_plus.util.BroadcastTool  Build +me.liolin.app_badge_plus.util.BroadcastTool  Intent +me.liolin.app_badge_plus.util.BroadcastTool  Log +me.liolin.app_badge_plus.util.BroadcastTool  TAG +me.liolin.app_badge_plus.util.BroadcastTool  resolveBroadcast +me.liolin.app_badge_plus.util.BroadcastTool  
sendBroadcast +me.liolin.app_badge_plus.util.BroadcastTool  sendDefaultBroadcast +me.liolin.app_badge_plus.util.BroadcastTool  sendIntentExplicitly +me.liolin.app_badge_plus.util.BroadcastTool  Collections *me.liolin.app_badge_plus.util.LauncherTool  Intent *me.liolin.app_badge_plus.util.LauncherTool  Log *me.liolin.app_badge_plus.util.LauncherTool  PackageManager *me.liolin.app_badge_plus.util.LauncherTool  TAG *me.liolin.app_badge_plus.util.LauncherTool  getClassName *me.liolin.app_badge_plus.util.LauncherTool  getComponentName *me.liolin.app_badge_plus.util.LauncherTool  getLauncherList *me.liolin.app_badge_plus.util.LauncherTool  indices *me.liolin.app_badge_plus.util.LauncherTool  makeSureLauncherOnTop *me.liolin.app_badge_plus.util.LauncherTool                                                                                                                                                                                                                                               