import 'dart:convert';
import 'dart:io';

void main() async {
  print("=== Direct WooCommerce API Test ===");
  
  // API credentials
  const String baseUrl = 'https://velvete.ly';
  const String consumerKey = 'ck_18128d84570a5c30870239c83e9cfcae0be0b4f9';
  const String consumerSecret = 'cs_e079d293dbffc0b44387b7eba6d8704f83638cb2';
  
  // Create the URL with authentication
  const String endpoint = '/wp-json/wc/v3/products';
  final String url = '$baseUrl$endpoint?consumer_key=$consumerKey&consumer_secret=$consumerSecret&per_page=1';
  
  print("DEBUG: Testing URL: $url");
  
  try {
    // Create HTTP client
    final HttpClient client = HttpClient();
    
    // Make the request
    final HttpClientRequest request = await client.getUrl(Uri.parse(url));
    request.headers.set('Content-Type', 'application/json');
    
    print("DEBUG: Sending HTTP request...");
    final HttpClientResponse response = await request.close();
    
    print("DEBUG: Response status code: ${response.statusCode}");
    print("DEBUG: Response headers: ${response.headers}");
    
    // Read response body
    final String responseBody = await response.transform(utf8.decoder).join();
    print("DEBUG: Response body length: ${responseBody.length}");
    print("DEBUG: Response body (first 500 chars): ${responseBody.length > 500 ? responseBody.substring(0, 500) + '...' : responseBody}");
    
    if (response.statusCode == 200) {
      // Try to parse JSON
      try {
        final dynamic jsonData = json.decode(responseBody);
        if (jsonData is List) {
          print("DEBUG: ✅ SUCCESS! Received ${jsonData.length} products");
          if (jsonData.isNotEmpty) {
            final product = jsonData[0];
            print("DEBUG: First product ID: ${product['id']}");
            print("DEBUG: First product name: ${product['name']}");
            print("DEBUG: First product price: ${product['price']}");
          }
        } else {
          print("DEBUG: ⚠️ Unexpected response format: ${jsonData.runtimeType}");
        }
      } catch (e) {
        print("DEBUG: ❌ JSON parsing error: $e");
      }
    } else {
      print("DEBUG: ❌ HTTP Error ${response.statusCode}");
      print("DEBUG: Error response: $responseBody");
    }
    
    client.close();
    
  } catch (e) {
    print("DEBUG: ❌ Network error: $e");
  }
  
  print("=== Test Complete ===");
}
