//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/app/events/login_event.dart';
import '/bootstrap/app_helper.dart';
import '/bootstrap/helpers.dart';
import '/resources/widgets/buttons.dart';
import '/resources/widgets/safearea_widget.dart';
import '/resources/widgets/velvete_ui.dart';
import 'package:nylo_framework/nylo_framework.dart';

import '/app/services/woocommerce_service.dart';
import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';
import '/app/models/libyan_city.dart';

class AccountRegistrationPage extends NyStatefulWidget {
  static RouteView path =
      ("/account-register", (_) => AccountRegistrationPage());

  AccountRegistrationPage({super.key})
      : super(child: () => _AccountRegistrationPageState());
}

class _AccountRegistrationPageState extends NyPage<AccountRegistrationPage> {
  final TextEditingController _tfEmailAddressController =
          TextEditingController(),
      _tfPasswordController = TextEditingController(),
      _tfFirstNameController = TextEditingController(),
      _tfLastNameController = TextEditingController(),
      _tfPhoneNumberController = TextEditingController();

  LibyanCity? _selectedCity;
  List<LibyanCity> _cities = [];

  @override
  get init => () {
    _cities = LibyanCitiesData.getAllCities();
  };



  @override
  Widget view(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(Icons.close),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(trans("Register")),
        centerTitle: true,
      ),
      resizeToAvoidBottomInset: false,
      body: SafeAreaWidget(
        child: Column(
          children: <Widget>[
            Container(
                margin: EdgeInsets.only(top: 10),
                child: Row(
                  children: <Widget>[
                    Flexible(
                      child: TextEditingRow(
                        heading: "الاسم", // Arabic for "First Name"
                        controller: _tfFirstNameController,
                        shouldAutoFocus: true,
                        keyboardType: TextInputType.text,
                      ),
                    ),
                    Flexible(
                      child: TextEditingRow(
                        heading: "اللقب", // Arabic for "Last Name"
                        controller: _tfLastNameController,
                        shouldAutoFocus: false,
                        keyboardType: TextInputType.text,
                      ),
                    ),
                  ],
                )),
            TextEditingRow(
              heading: trans("Email address"),
              controller: _tfEmailAddressController,
              shouldAutoFocus: false,
              keyboardType: TextInputType.emailAddress,
              hintText: "<EMAIL>",
            ),
            Container(
              margin: EdgeInsets.symmetric(vertical: 8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    trans("Email Notice"),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                  Text(
                    "For email communication. Gmail account preferred due to local usage patterns.",
                    style: TextStyle(
                      fontSize: 11,
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ),
            ),
            TextEditingRow(
              heading: trans("Phone Number"),
              controller: _tfPhoneNumberController,
              shouldAutoFocus: false,
              keyboardType: TextInputType.phone,
              hintText: "+21894xxxxxxx",
            ),
            Container(
              margin: EdgeInsets.symmetric(vertical: 8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    trans("WhatsApp Notice"),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.orange[700],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    "Must be used in WhatsApp to receive notifications and order status updates.",
                    style: TextStyle(
                      fontSize: 11,
                      color: Colors.orange[600],
                    ),
                  ),
                ],
              ),
            ),
            _buildCityDropdown(),
            TextEditingRow(
              heading: trans("Password"),
              controller: _tfPasswordController,
              shouldAutoFocus: false,
              obscureText: true,
            ),
            Padding(
              padding: EdgeInsets.only(top: 10),
              child: PrimaryButton(
                title: trans("Sign up"),
                isLoading: isLocked('register_user'),
                action: _signUpTapped,
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(vertical: 16),
              child: InkWell(
                onTap: _viewTOSModal,
                child: RichText(
                  text: TextSpan(
                    text:
                        '${trans("By tapping \"Register\" you agree to ")} ${AppHelper.instance.appConfig!.appName!}\'s ',
                    children: <TextSpan>[
                      TextSpan(
                          text: trans("terms and conditions"),
                          style: TextStyle(fontWeight: FontWeight.bold)),
                      TextSpan(text: '  ${trans("and")}  '),
                      TextSpan(
                          text: trans("privacy policy"),
                          style: TextStyle(fontWeight: FontWeight.bold)),
                    ],
                    style: TextStyle(
                        color:
                            (Theme.of(context).brightness == Brightness.light)
                                ? Colors.black45
                                : Colors.white70),
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCityDropdown() {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "المدينة", // Arabic for "City"
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8),
          Container(
            height: 50,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[400]!),
              borderRadius: BorderRadius.circular(8),
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<LibyanCity>(
                value: _selectedCity,
                hint: Text("اختر المدينة"), // Arabic for "Choose City"
                isExpanded: true,
                padding: EdgeInsets.symmetric(horizontal: 12),
                items: _cities.map((LibyanCity city) {
                  return DropdownMenuItem<LibyanCity>(
                    value: city,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(city.getDisplayName()),
                        Text(
                          city.getFormattedDeliveryCost(),
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
                onChanged: (LibyanCity? newValue) {
                  setState(() {
                    _selectedCity = newValue;
                  });
                },
              ),
            ),
          ),
          if (_selectedCity != null)
            Container(
              margin: EdgeInsets.only(top: 4),
              child: Text(
                "تكلفة التوصيل: ${_selectedCity!.getFormattedDeliveryCost()}", // Arabic for "Delivery cost"
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.green[700],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
        ],
      ),
    );
  }

  _signUpTapped() async {
    String email = _tfEmailAddressController.text,
        password = _tfPasswordController.text,
        firstName = _tfFirstNameController.text,
        lastName = _tfLastNameController.text,
        phoneNumber = _tfPhoneNumberController.text;

    if (email.isNotEmpty) {
      email = email.trim();
    }
    if (phoneNumber.isNotEmpty) {
      phoneNumber = phoneNumber.trim();
    }

    // Validation
    if (!isEmail(email)) {
      showToast(
          title: trans("Oops"),
          description: trans("That email address is not valid"),
          style: ToastNotificationStyleType.danger);
      return;
    }

    if (password.length <= 5) {
      showToast(
          title: trans("Oops"),
          description: trans("Password must be a min 6 characters"),
          style: ToastNotificationStyleType.danger);
      return;
    }

    if (firstName.isEmpty || lastName.isEmpty) {
      showToast(
          title: trans("Oops"),
          description: "الرجاء إدخال الاسم واللقب", // Arabic for "Please enter first and last name"
          style: ToastNotificationStyleType.danger);
      return;
    }

    if (phoneNumber.isEmpty) {
      showToast(
          title: trans("Oops"),
          description: "الرجاء إدخال رقم الهاتف", // Arabic for "Please enter phone number"
          style: ToastNotificationStyleType.danger);
      return;
    }

    if (!phoneNumber.startsWith('+218')) {
      showToast(
          title: trans("Oops"),
          description: "رقم الهاتف يجب أن يبدأ بـ +218", // Arabic for "Phone number must start with +218"
          style: ToastNotificationStyleType.danger);
      return;
    }

    if (_selectedCity == null) {
      showToast(
          title: trans("Oops"),
          description: "الرجاء اختيار المدينة", // Arabic for "Please select city"
          style: ToastNotificationStyleType.danger);
      return;
    }

    await lockRelease('register_user', perform: () async {
      try {
        // Create customer using WooCommerce API with enhanced data
        WooCustomer newCustomer = WooCustomer(
          email: email.toLowerCase(),
          firstName: firstName,
          lastName: lastName,
          // Note: WooCommerce REST API doesn't handle passwords directly
          // Password management should be handled through WordPress authentication
        );

        try {
          // Attempt to create customer via WooCommerce API
          WooCustomer? createdCustomer = await WooCommerceService().wooCommerce.createCustomer(newCustomer);

          if (createdCustomer.id != null) {
            // Store user session with enhanced data
            await NyStorage.save('user_email', email);
            await NyStorage.save('user_logged_in', true);
            await NyStorage.save('user_id', createdCustomer.id.toString());
            await NyStorage.save('user_first_name', firstName);
            await NyStorage.save('user_last_name', lastName);
            await NyStorage.save('user_phone_number', phoneNumber);
            await NyStorage.save('user_city', _selectedCity!.name);
            await NyStorage.save('user_city_arabic', _selectedCity!.nameArabic);
            await NyStorage.save('user_delivery_cost', _selectedCity!.deliveryCost.toString());

            // Show success message and navigate
            showToast(
                title: "مرحباً", // Arabic for "Hello"
                description: "تم إنشاء الحساب بنجاح! مرحباً بك في متجر فيلفيت", // Arabic for "Account created successfully! Welcome to Velvete Store"
                style: ToastNotificationStyleType.success);

            event<LoginEvent>(data: {"email": email});
            Navigator.pushNamedAndRemoveUntil(
                context, "/home", (Route<dynamic> route) => false);

          } else {
            showToast(
                title: trans("Oops!"),
                description: trans("Failed to create account"),
                style: ToastNotificationStyleType.danger);
            return;
          }
        } catch (e) {
          // Handle specific WooCommerce API errors
          String errorMessage = e.toString();
          if (errorMessage.contains('email') && errorMessage.contains('exists')) {
            showToast(
                title: trans("Oops!"),
                description: trans("That email is taken, try another"),
                style: ToastNotificationStyleType.danger);
          } else {
            showToast(
                title: trans("Oops!"),
                description: trans("Something went wrong, please try again"),
                style: ToastNotificationStyleType.danger);
          }
          NyLogger.error("Registration error: $e");
          return;
        }
      } on Exception catch (e) {
        printError(e.toString());
        showToast(
            title: trans("Oops!"),
            description: trans("Something went wrong"),
            style: ToastNotificationStyleType.danger);
        return;
      }



      event<LoginEvent>();

      showToast(
          title: "${trans("Hello")} $firstName",
          description: trans("you're now logged in"),
          style: ToastNotificationStyleType.success,
          icon: Icons.account_circle);
      if (!mounted) return;
      navigatorPush(context,
          routeName: UserAuth.instance.redirect, forgetLast: 2);
    });
  }

  _viewTOSModal() async {
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(trans("Actions")),
        content: Text(trans("View Terms and Conditions or Privacy policy")),
        actions: <Widget>[
          MaterialButton(
            onPressed: _viewTermsConditions,
            child: Text(trans("Terms and Conditions")),
          ),
          MaterialButton(
            onPressed: _viewPrivacyPolicy,
            child: Text(trans("Privacy Policy")),
          ),
          Divider(),
          TextButton(
            onPressed: pop,
            child: Text('Close'),
          ),
        ],
      ),
    );
  }

  void _viewTermsConditions() {
    Navigator.pop(context);
    openBrowserTab(url: "https://velvete.ly/terms-conditions/");
  }

  void _viewPrivacyPolicy() {
    Navigator.pop(context);
    openBrowserTab(url: "https://velvete.ly/privicy-policy/");
  }
}
