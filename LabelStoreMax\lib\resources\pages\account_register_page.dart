//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/app/events/login_event.dart';
import '/bootstrap/app_helper.dart';
import '/bootstrap/helpers.dart';
import '/resources/widgets/buttons.dart';
import '/resources/widgets/safearea_widget.dart';
import '/resources/widgets/velvete_ui.dart';
import 'package:nylo_framework/nylo_framework.dart';

import '/app/services/woocommerce_service.dart';
import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';

class AccountRegistrationPage extends NyStatefulWidget {
  static RouteView path =
      ("/account-register", (_) => AccountRegistrationPage());

  AccountRegistrationPage({super.key})
      : super(child: () => _AccountRegistrationPageState());
}

class _AccountRegistrationPageState extends NyPage<AccountRegistrationPage> {
  final TextEditingController _tfEmailAddressController =
          TextEditingController(),
      _tfPasswordController = TextEditingController(),
      _tfFirstNameController = TextEditingController(),
      _tfLastNameController = TextEditingController();



  @override
  Widget view(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(Icons.close),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(trans("Register")),
        centerTitle: true,
      ),
      resizeToAvoidBottomInset: false,
      body: SafeAreaWidget(
        child: Column(
          children: <Widget>[
            Container(
                margin: EdgeInsets.only(top: 10),
                child: Row(
                  children: <Widget>[
                    Flexible(
                      child: TextEditingRow(
                        heading: trans("First Name"),
                        controller: _tfFirstNameController,
                        shouldAutoFocus: true,
                        keyboardType: TextInputType.text,
                      ),
                    ),
                    Flexible(
                      child: TextEditingRow(
                        heading: trans("Last Name"),
                        controller: _tfLastNameController,
                        shouldAutoFocus: false,
                        keyboardType: TextInputType.text,
                      ),
                    ),
                  ],
                )),
            TextEditingRow(
              heading: trans("Email address"),
              controller: _tfEmailAddressController,
              shouldAutoFocus: false,
              keyboardType: TextInputType.emailAddress,
            ),
            TextEditingRow(
              heading: trans("Password"),
              controller: _tfPasswordController,
              shouldAutoFocus: true,
              obscureText: true,
            ),
            Padding(
              padding: EdgeInsets.only(top: 10),
              child: PrimaryButton(
                title: trans("Sign up"),
                isLoading: isLocked('register_user'),
                action: _signUpTapped,
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(vertical: 16),
              child: InkWell(
                onTap: _viewTOSModal,
                child: RichText(
                  text: TextSpan(
                    text:
                        '${trans("By tapping \"Register\" you agree to ")} ${AppHelper.instance.appConfig!.appName!}\'s ',
                    children: <TextSpan>[
                      TextSpan(
                          text: trans("terms and conditions"),
                          style: TextStyle(fontWeight: FontWeight.bold)),
                      TextSpan(text: '  ${trans("and")}  '),
                      TextSpan(
                          text: trans("privacy policy"),
                          style: TextStyle(fontWeight: FontWeight.bold)),
                    ],
                    style: TextStyle(
                        color:
                            (Theme.of(context).brightness == Brightness.light)
                                ? Colors.black45
                                : Colors.white70),
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  _signUpTapped() async {
    String email = _tfEmailAddressController.text,
        password = _tfPasswordController.text,
        firstName = _tfFirstNameController.text,
        lastName = _tfLastNameController.text;

    if (email.isNotEmpty) {
      email = email.trim();
    }

    if (!isEmail(email)) {
      showToast(
          title: trans("Oops"),
          description: trans("That email address is not valid"),
          style: ToastNotificationStyleType.danger);
      return;
    }

    if (password.length <= 5) {
      showToast(
          title: trans("Oops"),
          description: trans("Password must be a min 6 characters"),
          style: ToastNotificationStyleType.danger);
      return;
    }

    await lockRelease('register_user', perform: () async {
      try {
        // TODO: Implement proper WooCommerce customer registration
        // For now, we'll use a placeholder registration system
        // This should be replaced with WooCommerce customer creation API

        // Create customer using WooCommerce API
        WooCustomer newCustomer = WooCustomer(
          email: email.toLowerCase(),
          firstName: firstName,
          lastName: lastName,
          // Note: WooCommerce REST API doesn't handle passwords directly
          // Password management should be handled through WordPress authentication
        );

        try {
          // Attempt to create customer via WooCommerce API
          WooCustomer? createdCustomer = await WooCommerceService().wooCommerce.createCustomer(newCustomer);

          if (createdCustomer.id != null) {
            // Store user session (placeholder)
            await NyStorage.save('user_email', email);
            await NyStorage.save('user_logged_in', true);
            await NyStorage.save('user_id', createdCustomer.id.toString());
            await NyStorage.save('user_first_name', firstName);
            await NyStorage.save('user_last_name', lastName);


          } else {
            showToast(
                title: trans("Oops!"),
                description: trans("Failed to create account"),
                style: ToastNotificationStyleType.danger);
            return;
          }
        } catch (e) {
          // Handle specific WooCommerce API errors
          String errorMessage = e.toString();
          if (errorMessage.contains('email') && errorMessage.contains('exists')) {
            showToast(
                title: trans("Oops!"),
                description: trans("That email is taken, try another"),
                style: ToastNotificationStyleType.danger);
          } else {
            showToast(
                title: trans("Oops!"),
                description: trans("Something went wrong, please try again"),
                style: ToastNotificationStyleType.danger);
          }
          NyLogger.error("Registration error: $e");
          return;
        }
      } on Exception catch (e) {
        printError(e.toString());
        showToast(
            title: trans("Oops!"),
            description: trans("Something went wrong"),
            style: ToastNotificationStyleType.danger);
        return;
      }



      event<LoginEvent>();

      showToast(
          title: "${trans("Hello")} $firstName",
          description: trans("you're now logged in"),
          style: ToastNotificationStyleType.success,
          icon: Icons.account_circle);
      if (!mounted) return;
      navigatorPush(context,
          routeName: UserAuth.instance.redirect, forgetLast: 2);
    });
  }

  _viewTOSModal() async {
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(trans("Actions")),
        content: Text(trans("View Terms and Conditions or Privacy policy")),
        actions: <Widget>[
          MaterialButton(
            onPressed: _viewTermsConditions,
            child: Text(trans("Terms and Conditions")),
          ),
          MaterialButton(
            onPressed: _viewPrivacyPolicy,
            child: Text(trans("Privacy Policy")),
          ),
          Divider(),
          TextButton(
            onPressed: pop,
            child: Text('Close'),
          ),
        ],
      ),
    );
  }

  void _viewTermsConditions() {
    Navigator.pop(context);
    openBrowserTab(url: "https://velvete.ly/terms-conditions/");
  }

  void _viewPrivacyPolicy() {
    Navigator.pop(context);
    openBrowserTab(url: "https://velvete.ly/privicy-policy/");
  }
}
