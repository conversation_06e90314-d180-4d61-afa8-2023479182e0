_fe_analyzer_shared
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/_fe_analyzer_shared-82.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/_fe_analyzer_shared-82.0.0/lib/
_flutterfire_internals
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/_flutterfire_internals-1.3.55/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/_flutterfire_internals-1.3.55/lib/
analyzer
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/analyzer-7.4.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/analyzer-7.4.5/lib/
animate_do
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/animate_do-4.2.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/animate_do-4.2.0/lib/
app_badge_plus
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/app_badge_plus-1.2.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/app_badge_plus-1.2.3/lib/
archive
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/archive-4.0.7/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/archive-4.0.7/lib/
args
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/args-2.7.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/args-2.7.0/lib/
async
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/async-2.13.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/async-2.13.0/lib/
auto_size_text
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/auto_size_text-3.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/auto_size_text-3.0.0/lib/
base58check
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/base58check-2.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/base58check-2.0.0/lib/
bech32
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/bech32-0.2.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/bech32-0.2.2/lib/
boolean_selector
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/boolean_selector-2.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/boolean_selector-2.1.2/lib/
bubble_tab_indicator
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/bubble_tab_indicator-0.1.6/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/bubble_tab_indicator-0.1.6/lib/
cached_network_image
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cached_network_image-3.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cached_network_image-3.4.1/lib/
cached_network_image_platform_interface
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/lib/
cached_network_image_web
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cached_network_image_web-1.3.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cached_network_image_web-1.3.1/lib/
characters
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/characters-1.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/characters-1.4.0/lib/
checked_yaml
2.19
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/checked_yaml-2.0.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/checked_yaml-2.0.3/lib/
cli_util
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cli_util-0.4.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cli_util-0.4.2/lib/
clock
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/clock-1.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/clock-1.1.2/lib/
collection
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/collection-1.19.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/collection-1.19.1/lib/
convert
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/convert-3.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/convert-3.1.2/lib/
crypto
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/crypto-3.0.6/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/crypto-3.0.6/lib/
csslib
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/csslib-1.0.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/csslib-1.0.2/lib/
cupertino_icons
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cupertino_icons-1.0.8/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cupertino_icons-1.0.8/lib/
date_field
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/date_field-6.0.3+1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/date_field-6.0.3+1/lib/
dbus
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/dbus-0.7.11/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/dbus-0.7.11/lib/
dio
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/dio-5.8.0+1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/dio-5.8.0+1/lib/
dio_web_adapter
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/dio_web_adapter-2.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/dio_web_adapter-2.1.1/lib/
equatable
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/equatable-2.0.7/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/equatable-2.0.7/lib/
error_stack
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/error_stack-1.10.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/error_stack-1.10.3/lib/
eventify
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/eventify-1.0.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/eventify-1.0.1/lib/
fake_async
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fake_async-1.3.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fake_async-1.3.3/lib/
faker
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/faker-2.2.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/faker-2.2.0/lib/
ffi
3.7
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/ffi-2.1.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/ffi-2.1.4/lib/
file
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file-7.0.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file-7.0.1/lib/
firebase_core
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core-3.13.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core-3.13.1/lib/
firebase_core_platform_interface
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/lib/
firebase_core_web
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core_web-2.23.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core_web-2.23.0/lib/
firebase_messaging
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_messaging-15.2.6/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_messaging-15.2.6/lib/
firebase_messaging_platform_interface
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_messaging_platform_interface-4.6.6/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_messaging_platform_interface-4.6.6/lib/
firebase_messaging_web
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_messaging_web-3.10.6/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_messaging_web-3.10.6/lib/
fixnum
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fixnum-1.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fixnum-1.1.1/lib/
flutter_cache_manager
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_cache_manager-3.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/
flutter_dotenv
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_dotenv-5.2.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_dotenv-5.2.1/lib/
flutter_inappwebview
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_inappwebview-6.1.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/
flutter_inappwebview_android
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/lib/
flutter_inappwebview_internal_annotations
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_inappwebview_internal_annotations-1.2.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_inappwebview_internal_annotations-1.2.0/lib/
flutter_inappwebview_ios
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/lib/
flutter_inappwebview_macos
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_inappwebview_macos-1.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_inappwebview_macos-1.1.2/lib/
flutter_inappwebview_platform_interface
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/
flutter_inappwebview_web
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_inappwebview_web-1.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_inappwebview_web-1.1.2/lib/
flutter_inappwebview_windows
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_inappwebview_windows-0.6.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_inappwebview_windows-0.6.0/lib/
flutter_launcher_icons
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_launcher_icons-0.14.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_launcher_icons-0.14.3/lib/
flutter_lints
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_lints-5.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_lints-5.0.0/lib/
flutter_local_notifications
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_local_notifications-19.2.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_local_notifications-19.2.1/lib/
flutter_local_notifications_linux
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_local_notifications_linux-6.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_local_notifications_linux-6.0.0/lib/
flutter_local_notifications_platform_interface
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_local_notifications_platform_interface-9.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_local_notifications_platform_interface-9.0.0/lib/
flutter_local_notifications_windows
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_local_notifications_windows-1.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_local_notifications_windows-1.0.0/lib/
flutter_multi_formatter
2.19
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_multi_formatter-2.13.7/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_multi_formatter-2.13.7/lib/
flutter_plugin_android_lifecycle
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/lib/
flutter_rating_bar
2.14
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_rating_bar-4.0.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_rating_bar-4.0.1/lib/
flutter_secure_storage
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage-9.2.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/
flutter_secure_storage_linux
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/lib/
flutter_secure_storage_macos
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/lib/
flutter_secure_storage_platform_interface
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/lib/
flutter_secure_storage_web
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/lib/
flutter_secure_storage_windows
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/lib/
flutter_spinkit
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_spinkit-5.2.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_spinkit-5.2.1/lib/
flutter_staggered_grid_view
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_staggered_grid_view-0.7.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_staggered_grid_view-0.7.0/lib/
flutter_stripe
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_stripe-11.5.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_stripe-11.5.0/lib/
flutter_styled_toast
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_styled_toast-2.2.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_styled_toast-2.2.1/lib/
flutter_swiper_view
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_swiper_view-1.1.8/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_swiper_view-1.1.8/lib/
flutter_timezone
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_timezone-4.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_timezone-4.1.1/lib/
flutter_widget_from_html_core
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_widget_from_html_core-0.16.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_widget_from_html_core-0.16.0/lib/
fluttertoast
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fluttertoast-8.2.12/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fluttertoast-8.2.12/lib/
freezed_annotation
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/freezed_annotation-2.4.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/freezed_annotation-2.4.4/lib/
geocoding
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geocoding-3.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geocoding-3.0.0/lib/
geocoding_android
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geocoding_android-3.3.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geocoding_android-3.3.1/lib/
geocoding_ios
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geocoding_ios-3.0.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geocoding_ios-3.0.2/lib/
geocoding_platform_interface
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geocoding_platform_interface-3.2.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geocoding_platform_interface-3.2.0/lib/
geolocator
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator-13.0.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator-13.0.4/lib/
geolocator_android
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator_android-4.6.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator_android-4.6.2/lib/
geolocator_apple
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator_apple-2.3.13/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator_apple-2.3.13/lib/
geolocator_platform_interface
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/
geolocator_web
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator_web-4.1.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator_web-4.1.3/lib/
geolocator_windows
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator_windows-0.2.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator_windows-0.2.5/lib/
get_time_ago
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/get_time_ago-2.3.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/get_time_ago-2.3.1/lib/
glob
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/glob-2.1.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/glob-2.1.3/lib/
google_fonts
2.14
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_fonts-6.2.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_fonts-6.2.1/lib/
google_maps
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_maps-8.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_maps-8.1.1/lib/
google_maps_flutter
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_maps_flutter-2.12.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_maps_flutter-2.12.3/lib/
google_maps_flutter_android
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_maps_flutter_android-2.16.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_maps_flutter_android-2.16.2/lib/
google_maps_flutter_ios
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/lib/
google_maps_flutter_platform_interface
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/
google_maps_flutter_web
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_maps_flutter_web-0.5.12+2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_maps_flutter_web-0.5.12+2/lib/
html
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/html-0.15.6/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/html-0.15.6/lib/
http
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http-1.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http-1.4.0/lib/
http_parser
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http_parser-4.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http_parser-4.1.2/lib/
image
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image-4.5.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image-4.5.4/lib/
intl
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/intl-0.20.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/intl-0.20.2/lib/
js
2.19
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/js-0.6.7/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/js-0.6.7/lib/
json_annotation
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/json_annotation-4.9.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/json_annotation-4.9.0/lib/
leak_tracker
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker-10.0.9/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker-10.0.9/lib/
leak_tracker_flutter_testing
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/lib/
leak_tracker_testing
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_testing-3.0.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_testing-3.0.1/lib/
lints
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/lints-5.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/lints-5.1.1/lib/
logging
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/logging-1.3.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/logging-1.3.0/lib/
mask_text_input_formatter
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/mask_text_input_formatter-2.9.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/mask_text_input_formatter-2.9.0/lib/
matcher
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/matcher-0.12.17/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/matcher-0.12.17/lib/
material_color_utilities
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/material_color_utilities-0.11.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/
math_expressions
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/math_expressions-2.7.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/math_expressions-2.7.0/lib/
meta
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/meta-1.16.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/meta-1.16.0/lib/
nylo_framework
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/nylo_framework-6.8.7/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/nylo_framework-6.8.7/lib/
nylo_support
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/nylo_support-6.28.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/nylo_support-6.28.5/lib/
octo_image
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/octo_image-2.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/octo_image-2.1.0/lib/
package_config
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/package_config-2.2.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/package_config-2.2.0/lib/
package_info_plus
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/package_info_plus-8.3.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/package_info_plus-8.3.0/lib/
package_info_plus_platform_interface
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0/lib/
path
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path-1.9.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path-1.9.1/lib/
path_provider
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider-2.1.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider-2.1.5/lib/
path_provider_android
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_android-2.2.17/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_android-2.2.17/lib/
path_provider_foundation
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_foundation-2.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/
path_provider_linux
2.19
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_linux-2.2.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/
path_provider_platform_interface
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/
path_provider_windows
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_windows-2.3.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/
petitparser
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/petitparser-6.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/petitparser-6.1.0/lib/
platform
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/platform-3.1.6/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/platform-3.1.6/lib/
plugin_platform_interface
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/plugin_platform_interface-2.1.8/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib/
posix
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/posix-6.0.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/posix-6.0.2/lib/
pretty_dio_logger
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pretty_dio_logger-1.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pretty_dio_logger-1.4.0/lib/
pub_semver
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pub_semver-2.2.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pub_semver-2.2.0/lib/
pull_to_refresh_flutter3
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pull_to_refresh_flutter3-2.0.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pull_to_refresh_flutter3-2.0.2/lib/
razorpay_flutter
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/razorpay_flutter-1.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/razorpay_flutter-1.4.0/lib/
recase
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/recase-4.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/recase-4.1.0/lib/
rxdart
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/rxdart-0.28.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/rxdart-0.28.0/lib/
sanitize_html
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sanitize_html-2.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sanitize_html-2.1.0/lib/
shared_preferences
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences-2.5.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences-2.5.3/lib/
shared_preferences_android
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_android-2.4.10/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/
shared_preferences_foundation
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/
shared_preferences_linux
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_linux-2.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib/
shared_preferences_platform_interface
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/
shared_preferences_web
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_web-2.4.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_web-2.4.3/lib/
shared_preferences_windows
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_windows-2.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib/
skeletonizer
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/skeletonizer-2.0.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/skeletonizer-2.0.1/lib/
source_span
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/source_span-1.10.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/source_span-1.10.1/lib/
sprintf
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sprintf-7.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sprintf-7.0.0/lib/
sqflite
3.7
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite-2.4.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite-2.4.2/lib/
sqflite_android
3.7
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_android-2.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_android-2.4.1/lib/
sqflite_common
3.7
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_common-2.5.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_common-2.5.5/lib/
sqflite_darwin
3.7
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_darwin-2.4.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_darwin-2.4.2/lib/
sqflite_platform_interface
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/
stack_trace
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stack_trace-1.12.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stack_trace-1.12.1/lib/
stream_channel
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stream_channel-2.1.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stream_channel-2.1.4/lib/
stream_transform
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stream_transform-2.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stream_transform-2.1.1/lib/
string_scanner
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/string_scanner-1.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/string_scanner-1.4.1/lib/
stripe_android
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stripe_android-11.5.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stripe_android-11.5.0/lib/
stripe_ios
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stripe_ios-11.5.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stripe_ios-11.5.0/lib/
stripe_platform_interface
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stripe_platform_interface-11.5.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stripe_platform_interface-11.5.0/lib/
synchronized
3.7
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/synchronized-3.3.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/synchronized-3.3.1/lib/
term_glyph
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/term_glyph-1.2.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/term_glyph-1.2.2/lib/
test_api
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/test_api-0.7.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/test_api-0.7.4/lib/
theme_provider
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/theme_provider-0.6.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/theme_provider-0.6.0/lib/
timezone
2.19
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/timezone-0.10.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/timezone-0.10.1/lib/
typed_data
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/typed_data-1.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/typed_data-1.4.0/lib/
url_launcher
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher-6.3.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher-6.3.1/lib/
url_launcher_android
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_android-6.3.16/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_android-6.3.16/lib/
url_launcher_ios
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_ios-6.3.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_ios-6.3.3/lib/
url_launcher_linux
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_linux-3.2.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib/
url_launcher_macos
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_macos-3.2.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_macos-3.2.2/lib/
url_launcher_platform_interface
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/
url_launcher_web
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_web-2.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_web-2.4.1/lib/
url_launcher_windows
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_windows-3.1.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_windows-3.1.4/lib/
uuid
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/uuid-4.5.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/uuid-4.5.1/lib/
validated
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/validated-2.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/validated-2.0.0/lib/
vector_math
2.14
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_math-2.1.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_math-2.1.4/lib/
vm_service
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vm_service-15.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vm_service-15.0.0/lib/
watcher
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/watcher-1.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/watcher-1.1.1/lib/
web
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web-1.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web-1.1.1/lib/
win32
3.7
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/win32-5.13.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/win32-5.13.0/lib/
woocommerce_flutter_api
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/woocommerce_flutter_api-1.3.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/woocommerce_flutter_api-1.3.0/lib/
wp_json_api
2.19
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/wp_json_api-4.3.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/wp_json_api-4.3.3/lib/
xdg_directories
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/xdg_directories-1.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/xdg_directories-1.1.0/lib/
xml
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/xml-6.5.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/xml-6.5.0/lib/
yaml
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/yaml-3.1.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/yaml-3.1.3/lib/
flutter_app
3.4
file:///C:/Users/<USER>/Downloads/Velvete/APP/Project%2001/flutter_velvete_1/LabelStoreMax/
file:///C:/Users/<USER>/Downloads/Velvete/APP/Project%2001/flutter_velvete_1/LabelStoreMax/lib/
sky_engine
3.7
file:///C:/flutter/bin/cache/pkg/sky_engine/
file:///C:/flutter/bin/cache/pkg/sky_engine/lib/
flutter
3.7
file:///C:/flutter/packages/flutter/
file:///C:/flutter/packages/flutter/lib/
flutter_localizations
3.7
file:///C:/flutter/packages/flutter_localizations/
file:///C:/flutter/packages/flutter_localizations/lib/
flutter_test
3.7
file:///C:/flutter/packages/flutter_test/
file:///C:/flutter/packages/flutter_test/lib/
flutter_web_plugins
3.7
file:///C:/flutter/packages/flutter_web_plugins/
file:///C:/flutter/packages/flutter_web_plugins/lib/
2
