{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986a24ef576cc955de4007c337d27b8adb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984da35711a0dc9b7fa0072b2398e1d023", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985f17a4332bf1d4bc957ee97483f8481d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982a384bc127a4c4cfbff984204c34e64b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985f17a4332bf1d4bc957ee97483f8481d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982614535334b10d5374392c912942f061", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f7e1265da3be3170c89bfb6bf8bf1bd5", "guid": "bfdfe7dc352907fc980b868725387e988459b7be903a872cbb12fb25848e024d", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98ec72c9edf584475f1221baa047633a7f", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e1b275481f608655771816fc6f6b5f86", "guid": "bfdfe7dc352907fc980b868725387e98b802a760fd4f4e805273c7f154a8ed48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f348da7a3e617e4c0bb23661dfb5298c", "guid": "bfdfe7dc352907fc980b868725387e985278333db3f67abab844415650f69ae7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f03af4542d859f157b75ad133a9e6886", "guid": "bfdfe7dc352907fc980b868725387e982af2839ada8bae8d3e2fc10227f06240"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987be1e9af166b13cbcbf0d9d16c000b79", "guid": "bfdfe7dc352907fc980b868725387e984a9c80c12210e182070bf31f4c36e9b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df915500269976414caf8132c493d4a5", "guid": "bfdfe7dc352907fc980b868725387e9800429b19c1af390eaa07e44557bf8224"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9e855f693086bd6e0ac7f08001bdaff", "guid": "bfdfe7dc352907fc980b868725387e98720a7fb14d317a70c468902906cf1260"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f87a3050c5d8300c41121361d8b066ed", "guid": "bfdfe7dc352907fc980b868725387e982b5da231df97625f221dfd4502725f78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861f69e80cc9a6086c7d337526e95a7f5", "guid": "bfdfe7dc352907fc980b868725387e98585b0a1900280e7c2076a95eaa7b06e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843d0f2dbfb2528edeafdb7580ee6b76f", "guid": "bfdfe7dc352907fc980b868725387e98f4bfdbb27d6c4f2dc2a17174f4a54c91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e8c8039532000c5fc74a9324eb2c059", "guid": "bfdfe7dc352907fc980b868725387e98a223b9f51a95587b2213e08dc31fa306"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870d5cb147ac2cd5a0c58a871c7a71b42", "guid": "bfdfe7dc352907fc980b868725387e981e61610c4fbe1bee10334274e8855f2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0fa0ba4b23e8e06f4a120f8034b1f1a", "guid": "bfdfe7dc352907fc980b868725387e98fa5154fff3dadc2b11e1192611647236"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b4edb5d2a0115f813931c618f5da8f9", "guid": "bfdfe7dc352907fc980b868725387e98233d67064530f39cec2608c26509014a"}], "guid": "bfdfe7dc352907fc980b868725387e98d01c6a68020ecde2d441536fab0fa8d3", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9831e748fa35c434cc0f07d820e7512aa7", "guid": "bfdfe7dc352907fc980b868725387e9806f2ea858e3a7c1bd7ecd27ecdac48f5"}], "guid": "bfdfe7dc352907fc980b868725387e98937e3f43c6d1caf0da0ef7af568bed95", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c864ce31e8d7322597ed12c3b204f865", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e98db121442a0a92aa29f7ae90f7d9b68ee", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}