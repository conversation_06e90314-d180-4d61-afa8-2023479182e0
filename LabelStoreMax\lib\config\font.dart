import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/* Font
|--------------------------------------------------------------------------
| Uses Google Fonts - https://pub.dev/packages/google_fonts
|
| e.g. updating the font from "montserrat" to "lato"
| before: final TextStyle appThemeFont = GoogleFonts.montserrat();
| after:  final TextStyle appThemeFont = GoogleFonts.lato();
|-------------------------------------------------------------------------- */

TextStyle appFont = GoogleFonts.poppins();

// e.g. custom font in pubspec.yaml - https://flutter.dev/docs/cookbook/design/fonts
// final TextStyle appThemeFont = TextStyle(fontFamily: "ZenTokyoZoo");
