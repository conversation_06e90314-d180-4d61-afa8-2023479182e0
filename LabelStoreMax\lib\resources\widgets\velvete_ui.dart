//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/app/models/cart.dart';
import '/app/models/checkout_session.dart';
import '/bootstrap/helpers.dart';
import 'package:nylo_framework/nylo_framework.dart';

class CheckoutRowLine extends StatelessWidget {
  const CheckoutRowLine(
      {super.key,
      required this.heading,
      required this.leadImage,
      required this.leadTitle,
      required this.action,
      this.showBorderBottom = true});

  final String heading;
  final String? leadTitle;
  final Widget leadImage;
  final Function() action;
  final bool showBorderBottom;

  @override
  Widget build(BuildContext context) => Container(
        child: Column(
          children: <Widget>[
            Container(
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Text(
                    heading,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  Row(
                    children: <Widget>[
                      leadImage,
                      Padding(
                        padding: EdgeInsets.only(left: 8),
                        child: Text(
                          leadTitle ?? "",
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(left: 8),
                        child: Icon(Icons.arrow_forward_ios, size: 16),
                      )
                    ],
                  )
                ],
              ),
            ),
            if (showBorderBottom == true)
              Container(
                height: 1,
                color: Colors.black12,
              )
          ],
        ),
      );
}

class CheckoutMetaLine extends StatelessWidget {
  const CheckoutMetaLine({super.key, required this.title, required this.amount});

  final String title;
  final String amount;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Flexible(
            child: Container(
              child: Text(
                title,
                style: Theme.of(context).textTheme.bodyMedium,
                overflow: TextOverflow.ellipsis,
                maxLines: 3,
              ),
            ),
          ),
          Container(
            child: Text(
              amount,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }
}

class CheckoutSubtotal extends StatelessWidget {
  const CheckoutSubtotal({super.key, this.title});

  final String? title;

  @override
  Widget build(BuildContext context) => NyFutureBuilder<String>(
        future: Cart.getInstance.getSubtotal(withFormat: true),
        child: (BuildContext context, data) => Padding(
          padding: EdgeInsets.only(bottom: 0, top: 0),
          child: CheckoutMetaLine(
            title: title ?? trans("Subtotal"),
            amount: data ?? "",
          ),
        ),
        loadingStyle: LoadingStyle.none(),
      );
}

class CheckoutTotal extends StatelessWidget {
  const CheckoutTotal({super.key, required this.title, required this.amount});

  final String title;
  final String amount;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: Colors.black12, width: 1),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Flexible(
            child: Container(
              child: Text(
                title,
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                overflow: TextOverflow.ellipsis,
                maxLines: 3,
              ),
            ),
          ),
          Container(
            child: Text(
              amount,
              style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
            ),
          ),
        ],
      ),
    );
  }
}

List<BoxShadow> wsBoxShadow({double? blurRadius}) => [
      BoxShadow(
        color: Color(0xFFE8E8E8),
        blurRadius: blurRadius ?? 15.0,
        spreadRadius: 0,
        offset: Offset(
          0.0,
          0.0,
        ),
      )
    ];

wsModalBottom(
    BuildContext context, Widget Function(BuildContext) builder) async {
  return await showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: builder);
}

// StoreLogo class moved to store_logo_widget.dart to avoid conflicts

class TextEditingRow extends StatelessWidget {
  const TextEditingRow({
    super.key,
    required this.heading,
    required this.controller,
    this.shouldAutoFocus = false,
    this.keyboardType,
    this.obscureText = false,
    this.validator,
    this.onChanged,
  });

  final String heading;
  final TextEditingController? controller;
  final bool shouldAutoFocus;
  final TextInputType? keyboardType;
  final bool obscureText;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;

  @override
  Widget build(BuildContext context) => Container(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Flexible(
              child: Container(
                height: 50,
                child: TextFormField(
                  controller: controller,
                  readOnly: false,
                  autofocus: shouldAutoFocus,
                  autocorrect: false,
                  keyboardType: keyboardType,
                  obscureText: obscureText,
                  validator: validator,
                  onChanged: onChanged,
                  style: Theme.of(context).textTheme.titleMedium,
                  decoration: InputDecoration(
                    hintText: heading,
                    labelText: heading,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: Theme.of(context).primaryColor,
                        width: 2,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      );
}

class CheckoutTaxTotal extends StatelessWidget {
  const CheckoutTaxTotal({super.key, required this.checkoutSession});

  final CheckoutSession checkoutSession;

  @override
  Widget build(BuildContext context) {
    return NyFutureBuilder<String>(
      future: Cart.getInstance.taxAmount(null), // TODO: Pass correct tax rate
      child: (BuildContext context, data) {
        if (data == "0.00") {
          return SizedBox.shrink();
        }
        return CheckoutMetaLine(
          title: trans("Tax"),
          amount: formatStringCurrency(total: data),
        );
      },
    );
  }
}

class CheckoutShippingTotal extends StatelessWidget {
  const CheckoutShippingTotal({super.key, required this.checkoutSession});

  final CheckoutSession checkoutSession;

  @override
  Widget build(BuildContext context) {
    if (checkoutSession.shippingType == null) {
      return SizedBox.shrink();
    }
    return CheckoutMetaLine(
      title: trans("Shipping"),
      amount: formatStringCurrency(total: checkoutSession.shippingType!.cost),
    );
  }
}

class CheckoutCartTotal extends StatelessWidget {
  const CheckoutCartTotal({super.key});

  @override
  Widget build(BuildContext context) {
    return NyFutureBuilder<String>(
      future: Cart.getInstance.getTotal(),
      child: (BuildContext context, data) => CheckoutSubtotal(
        title: trans("Subtotal"),
      ),
    );
  }
}

class CheckoutGrandTotal extends StatelessWidget {
  const CheckoutGrandTotal({super.key, required this.checkoutSession});

  final CheckoutSession checkoutSession;

  @override
  Widget build(BuildContext context) {
    return NyFutureBuilder<String>(
      future: checkoutSession.total(),
      child: (BuildContext context, data) => CheckoutTotal(
        title: trans("Total"),
        amount: formatStringCurrency(total: data),
      ),
    );
  }
}
