// lib/resources/pages/simple_product_detail_page.dart
// Velvete Store - Simple Product Detail Page
//
// Created by we008.
// 2025, Velvete Store. All rights reserved.

import 'package:flutter/material.dart';
import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart' as wc_api;
import 'package:nylo_framework/nylo_framework.dart';


class SimpleProductDetailPage extends NyStatefulWidget {
  static RouteView path = ("/simple_product_detail", (_) => SimpleProductDetailPage());

  SimpleProductDetailPage({super.key}) : super(child: () => _SimpleProductDetailPageState());
}

class _SimpleProductDetailPageState extends NyPage<SimpleProductDetailPage>
    with SingleTickerProviderStateMixin {
  wc_api.WooProduct? _product;
  TabController? _tabController;
  bool _isLoading = true;

  @override
  get init => () async {
    _tabController = TabController(length: 3, vsync: this);
    await _loadProduct();
  };

  Future<void> _loadProduct() async {
    try {
      // Get the product data using Nylo's data method
      final productData = widget.data();

      print("SimpleProductDetailPage received data type: ${productData.runtimeType}");
      print("SimpleProductDetailPage received data: $productData");

      if (productData is wc_api.WooProduct) {
        _product = productData;
      } else if (productData != null) {
        // Try to access the data property using reflection
        try {
          // Check if it's an ArgumentsWrapper and try to get the data
          if (productData.toString().contains('ArgumentsWrapper')) {
            // Try to access the data field using reflection
            final dynamic wrapper = productData;
            print("Wrapper toString: ${wrapper.toString()}");
            print("Wrapper runtimeType: ${wrapper.runtimeType}");

            // Try different ways to access the data
            try {
              final data = wrapper.data;
              if (data is wc_api.WooProduct) {
                _product = data;
              }
            } catch (e1) {
              print("Failed to access .data: $e1");
              try {
                final data = wrapper['data'];
                if (data is wc_api.WooProduct) {
                  _product = data;
                }
              } catch (e2) {
                print("Failed to access ['data']: $e2");
              }
            }
          } else {
            _product = productData as wc_api.WooProduct;
          }
        } catch (e) {
          print("Error casting product: $e");
        }
      }

      if (_product == null) {
        print("Product not found - creating a dummy product for testing");
        // For now, let's create a dummy product to test the UI
        _product = wc_api.WooProduct(
          id: 0,
          name: "Test Product",
          price: 100.0,
          regularPrice: 100.0,
          description: "This is a test product",
          shortDescription: "Test product",
          images: [],
        );
      }

      setState(() {
        _isLoading = false;
      });

      // BLUEPRINT IMPLEMENTATION: Branch based on product type
      if (_product != null) {
        if (_product!.type == wc_api.WooProductType.variable) {
          print("🔄 VARIABLE PRODUCT DETECTED - Implementing full variation logic");
          // TODO: Implement variation loading and selection
        } else if (_product!.type == wc_api.WooProductType.simple) {
          print("✅ SIMPLE PRODUCT DETECTED - Standard display logic");
          // For simple products, no variation logic needed
        } else {
          print("⚠️ UNKNOWN PRODUCT TYPE: ${_product!.type}");
        }
      }

    } catch (e) {
      print("Error loading product: $e");
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        Navigator.of(context).pop();
      }
    }
  }



  @override
  void dispose() {
    _tabController?.dispose();
    super.dispose();
  }

  @override
  Widget view(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: Text("Product Details"),
          centerTitle: true,
        ),
        body: Center(child: CircularProgressIndicator()),
      );
    }

    final product = _product;
    if (product == null) {
      return Scaffold(
        appBar: AppBar(
          title: Text("Product Details"),
          centerTitle: true,
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text("Product not found"),
              SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text("Go Back"),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Text("Product Detail Page - ${product.name}"),
      ),
    );
  }


}