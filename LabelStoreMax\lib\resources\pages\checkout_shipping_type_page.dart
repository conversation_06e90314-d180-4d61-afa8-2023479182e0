//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:collection/collection.dart' show IterableExtension;
import 'package:flutter/material.dart';
import '/app/models/cart.dart';
import '/app/models/cart_line_item.dart';
import '/app/models/checkout_session.dart';
import '/app/models/shipping_type.dart';
import '/bootstrap/helpers.dart';
import '/resources/widgets/app_loader_widget.dart';
import '/resources/widgets/buttons.dart';
import '/resources/widgets/safearea_widget.dart';
import '/resources/widgets/velvete_ui.dart';
import 'package:nylo_framework/nylo_framework.dart';
import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';

class CheckoutShippingTypePage extends NyStatefulWidget {
  static RouteView path =
      ("/checkout-shipping-type", (_) => CheckoutShippingTypePage());

  CheckoutShippingTypePage({super.key})
      : super(child: () => _CheckoutShippingTypePageState());
}

class _CheckoutShippingTypePageState extends NyPage<CheckoutShippingTypePage> {
  bool _isShippingSupported = true, _isLoading = true;
  final List<Map<String, dynamic>> _wsShippingOptions = [];
  WooShippingZone? _shippingZone;

  @override
  get init => () {
        _getShippingMethods();
      };

  _getShippingMethods() async {
    try {
      // TODO: Implement proper WooCommerce shipping zones and methods
      // For now, create simple shipping options

      Map<String, dynamic> flatRateOption = {
        "id": "flat_rate",
        "method_id": "flat_rate",
        "title": "Flat Rate Shipping",
        "cost": "10.00", // Default shipping cost
        "object": null
      };

      _wsShippingOptions.add(flatRateOption);

      // Add free shipping option
      Map<String, dynamic> freeShippingOption = {
        "id": "free_shipping",
        "method_id": "free_shipping",
        "title": "Free Shipping",
        "cost": "0.00",
        "object": null
      };

      _wsShippingOptions.add(freeShippingOption);

    } catch (e) {
      print("Error fetching shipping methods: $e");
      _isShippingSupported = false;
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<String> _getShippingPrice(int index) async {
    double total = 0;
    List<CartLineItem> cartLineItem = await Cart.getInstance.getCart();

    total += (await (workoutShippingCostWC(
            sum: _wsShippingOptions[index]['cost']))) ??
        0;

    switch (_wsShippingOptions[index]['method_id']) {
      case "flat_rate":
        FlatRate? flatRate = (_wsShippingOptions[index]['object'] as FlatRate?);

        if (cartLineItem.firstWhereOrNull(
                (t) => t.shippingClassId == null || t.shippingClassId == "0") !=
            null) {
          total += await (workoutShippingClassCostWC(
                  sum: flatRate!.classCost,
                  cartLineItem: cartLineItem
                      .where((t) =>
                          t.shippingClassId == null || t.shippingClassId == "0")
                      .toList())) ??
              0;
        }

        List<CartLineItem> cItemsWithShippingClasses = cartLineItem
            .where((t) => t.shippingClassId != null && t.shippingClassId != "0")
            .toList();
        for (int i = 0; i < cItemsWithShippingClasses.length; i++) {
          ShippingClasses? shippingClasses = flatRate!.shippingClasses!
              .firstWhereOrNull(
                  (d) => d.id == cItemsWithShippingClasses[i].shippingClassId);
          if (shippingClasses != null) {
            double classTotal = await (workoutShippingClassCostWC(
                    sum: shippingClasses.cost,
                    cartLineItem: cartLineItem
                        .where((g) => g.shippingClassId == shippingClasses.id)
                        .toList())) ??
                0;
            total += classTotal;
          }
        }
        break;
      default:
        break;
    }
    return (total).toString();
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        title: Text(trans("Shipping Methods")),
        automaticallyImplyLeading: false,
        centerTitle: true,
      ),
      body: SafeAreaWidget(
        child: GestureDetector(
          onTap: () => FocusScope.of(context).requestFocus(FocusNode()),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Padding(
                padding: EdgeInsets.only(top: 20),
                child: Center(
                  child: Image.asset(
                    getImageAsset('shipping_icon.png'),
                    height: 100,
                    color: (Theme.of(context).brightness == Brightness.light)
                        ? null
                        : Colors.white,
                    fit: BoxFit.fitHeight,
                  ),
                ),
              ),
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: ThemeColor.get(context).backgroundContainer,
                    borderRadius: BorderRadius.circular(10),
                    boxShadow:
                        (Theme.of(context).brightness == Brightness.light)
                            ? wsBoxShadow()
                            : null,
                  ),
                  padding: EdgeInsets.all(8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: <Widget>[
                      (_isLoading
                          ? Expanded(child: AppLoaderWidget())
                          : (_isShippingSupported
                              ? Expanded(
                                  child: ListView.separated(
                                    itemCount: _wsShippingOptions.length,
                                    separatorBuilder: (context, index) =>
                                        Divider(
                                      color: Colors.black12,
                                    ),
                                    itemBuilder:
                                        (BuildContext context, int index) {
                                      return ListTile(
                                        contentPadding: EdgeInsets.only(
                                          left: 16,
                                          right: 16,
                                        ),
                                        title: Text(
                                          _wsShippingOptions[index]['title'],
                                          style: Theme.of(context)
                                              .textTheme
                                              .titleMedium!
                                              .copyWith(
                                                fontWeight: FontWeight.bold,
                                              ),
                                        ),
                                        selected: true,
                                        subtitle: NyFutureBuilder<String>(
                                          future: _getShippingPrice(index),
                                          child: (BuildContext context, data) {
                                            Map<String, dynamic>
                                                shippingOption =
                                                _wsShippingOptions[index];
                                            return RichText(
                                              text: TextSpan(
                                                text: '',
                                                style: Theme.of(context)
                                                    .textTheme
                                                    .bodyMedium,
                                                children: <TextSpan>[
                                                  (shippingOption["object"]
                                                          is FreeShipping
                                                      ? TextSpan(
                                                          text: trans(
                                                              "Free postage"),
                                                        )
                                                      : TextSpan(
                                                          text:
                                                              "${trans("Price")}: ${formatStringCurrency(total: data)}",
                                                        )),
                                                  if (shippingOption[
                                                          "min_amount"] !=
                                                      null)
                                                    TextSpan(
                                                        text:
                                                            "\n${trans("Spend a minimum of")} ${formatStringCurrency(total: shippingOption["min_amount"])}",
                                                        style: Theme.of(context)
                                                            .textTheme
                                                            .bodyMedium!
                                                            .copyWith(
                                                                fontSize: 14))
                                                ],
                                              ),
                                            );
                                          },
                                        ),
                                        trailing: (CheckoutSession.getInstance
                                                        .shippingType !=
                                                    null &&
                                                CheckoutSession.getInstance
                                                        .shippingType!.object ==
                                                    _wsShippingOptions[index]
                                                        ["object"]
                                            ? Icon(Icons.check)
                                            : null),
                                        onTap: () =>
                                            _handleCheckoutTapped(index),
                                      );
                                    },
                                  ),
                                )
                              : Text(
                                  trans(
                                      "Shipping is not supported for your location, sorry"),
                                  style: Theme.of(context).textTheme.titleLarge,
                                  textAlign: TextAlign.center,
                                ))),
                      LinkButton(
                        title: trans("CANCEL"),
                        action: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  _handleCheckoutTapped(int index) async {
    Map<String, dynamic> shippingOptions = _wsShippingOptions[index];
    ShippingType shippingType = ShippingType(
        methodId: shippingOptions['method_id'],
        object: shippingOptions['object'],
        cost: (await _getShippingPrice(index)),
        minimumValue: null);

    if (_wsShippingOptions[index]['min_amount'] != null) {
      shippingType.minimumValue = _wsShippingOptions[index]['min_amount'];
    }

    CheckoutSession.getInstance.shippingType = shippingType;

    pop();
  }
}
