import 'package:nylo_framework/nylo_framework.dart';
import '/app/services/auth_service.dart';

class LoginEvent implements NyEvent {
  @override
  final listeners = {
    DefaultListener: DefaultListener(),
  };
}

class DefaultListener extends NyListener {
  @override
  handle(dynamic event) async {
    // Phase 2.2: Enhanced login event handling with AuthService
    UserSession? currentUser = await AuthService().getCurrentUser();
    if (currentUser != null) {
      // User is authenticated, store user data in app state if needed
      print('User logged in: ${currentUser.email} (ID: ${currentUser.id})');

      // You can add additional login event handling here
      // For example: sync cart data, fetch user preferences, etc.
    }
  }
}
