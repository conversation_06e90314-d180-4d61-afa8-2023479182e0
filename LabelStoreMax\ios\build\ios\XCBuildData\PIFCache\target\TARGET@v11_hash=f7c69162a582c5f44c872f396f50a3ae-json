{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e049c0643656c5dc9c79c746912536fd", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/flutter_timezone", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "flutter_timezone", "INFOPLIST_FILE": "Target Support Files/flutter_timezone/ResourceBundle-flutter_timezone_privacy-flutter_timezone-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "flutter_timezone_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e981ad604846d7f7359113db4da8592dfe7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a71a56978f0106e35b7447ac0cf6067f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/flutter_timezone", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "flutter_timezone", "INFOPLIST_FILE": "Target Support Files/flutter_timezone/ResourceBundle-flutter_timezone_privacy-flutter_timezone-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "PRODUCT_NAME": "flutter_timezone_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e989f11724b0f26500e6d26efc490890e9a", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a71a56978f0106e35b7447ac0cf6067f", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/flutter_timezone", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "flutter_timezone", "INFOPLIST_FILE": "Target Support Files/flutter_timezone/ResourceBundle-flutter_timezone_privacy-flutter_timezone-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "PRODUCT_NAME": "flutter_timezone_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98c7f8eb779a8ced25f83254df5aaa36ae", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98b35f9517f65688362eaa50ea1a1f214a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98905e48860fb6b801222a8ce94fd820ee", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b6b2065b8b772dc528a0f363e8ebe454", "guid": "bfdfe7dc352907fc980b868725387e98ad7f8bc081138307b0453decefd5dd59"}], "guid": "bfdfe7dc352907fc980b868725387e9892ff262252a8008b1fcc4f7f86a42be6", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e981e40b3b736efc8c91830a70eb0de9350", "name": "flutter_timezone-flutter_timezone_privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e987da207c5ea856363cb85710443672df9", "name": "flutter_timezone_privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}