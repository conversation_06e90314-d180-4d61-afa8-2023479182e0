{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b3f389e5c9197b38cf59307d74d4d919", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9813120398bd9a5715e7712ebd1f088eee", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f6e20d18a707b10b7380c9eeba798297", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9821be5dee9bcbc43f6bcb64f33ccfd85f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f6e20d18a707b10b7380c9eeba798297", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98200c03127db44400fb2c5f62a915a9fb", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98dec4524fdffc2406679df8f45467cc9f", "guid": "bfdfe7dc352907fc980b868725387e9824d90089878bb82cc48b8a8cef25872d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1a436f7c78ad8c412b794839647245c", "guid": "bfdfe7dc352907fc980b868725387e98f9e97c1a1393b4ad0cb266d5096de462", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98266342f1e8fc539c8a74891ca9e69238", "guid": "bfdfe7dc352907fc980b868725387e98bbefdec555cf638792ba33874e97b9c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875dfaeae5d9647587e55b97c2a3bec2d", "guid": "bfdfe7dc352907fc980b868725387e988aa1d98db03c4f22f7d8be262825b1c7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98794a1ba584e87cdda2f728d794d10287", "guid": "bfdfe7dc352907fc980b868725387e98eec3d1f0f816afef24faf1ebed8394a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803e1155c888d6c32f05bdbffee043bb5", "guid": "bfdfe7dc352907fc980b868725387e9830993f78eb028c18c3efd440a3ea188c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98413e4d37ba73d375bb133666ac99d446", "guid": "bfdfe7dc352907fc980b868725387e987437b957627e52a8e57f2c742175b35e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899f7e6af1ab54fe2cc01f1a6efd2b773", "guid": "bfdfe7dc352907fc980b868725387e98840d48e1dc8cba3adfe66f7b82725e1a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4c9879be87992a087bd3036b4197af6", "guid": "bfdfe7dc352907fc980b868725387e9822886e6c5f3314e94dc717505e4c7fb9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c433393f724215cd04fef2660681739", "guid": "bfdfe7dc352907fc980b868725387e9884607f81e48e8c259930fcf24666d17b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857ee0e181e10bb06cdd3aac53fb1c385", "guid": "bfdfe7dc352907fc980b868725387e98d5fad93727cdb32c4ccfc202804b6336", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818f75937a108635bae6d24c32b331fe2", "guid": "bfdfe7dc352907fc980b868725387e983da3d43a450824fff033ec89a612738e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98633b59d29a087134d625a861471e8ff2", "guid": "bfdfe7dc352907fc980b868725387e985686fe24c36ed909fc15947737057a1f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988161012420c93411d657bb43479ed2e6", "guid": "bfdfe7dc352907fc980b868725387e988bcc7bcca5a17cc8c4d61ab9cf329f37", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98febccdc5c468dbfccc8aaa6cd426c35e", "guid": "bfdfe7dc352907fc980b868725387e98a25b0012b8e727948dc8f96ccd60d2a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2fcc565d8c46a99c4dc2d3a0595d6ef", "guid": "bfdfe7dc352907fc980b868725387e983a6b1522258f94e4f51743214c8e8093", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a86c47434206c00117ae4ee399d0577e", "guid": "bfdfe7dc352907fc980b868725387e98ced7b7b9a3eb9c87bd5dd38d092a8bef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987737e76a58ab08906f4872a0db3a3948", "guid": "bfdfe7dc352907fc980b868725387e984e1935f6abf4b78f92bd156598133598", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989edf4297489d7962d33032c0f208c371", "guid": "bfdfe7dc352907fc980b868725387e9829e2dd30a27c74e591c4395e52e13e08", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3d296a518abc470e01b117c9a05acd6", "guid": "bfdfe7dc352907fc980b868725387e98bb643272c048d77a46b00c1190cafb3a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980157cae439a537c990bf5379ad29de3d", "guid": "bfdfe7dc352907fc980b868725387e98b6d66618b743767df649a03540ba85dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f00b06279c363c70e7716b6806f68bdd", "guid": "bfdfe7dc352907fc980b868725387e98b67a0c868cd8657c96e13f271ccda002"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822e0130b2baf9a0205e002cd9af8330b", "guid": "bfdfe7dc352907fc980b868725387e98c0f683c9b4c77b270297c0035cf9c18e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98270278c1e03e5bcc067444b703db9cee", "guid": "bfdfe7dc352907fc980b868725387e984ffc6453e92fb73936c5d7866520f1b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f90492668848765744b29be8146cd244", "guid": "bfdfe7dc352907fc980b868725387e983ea2ca5410f8e84aa197b6b560cdcf77", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f820768eb0da41c008e42ade18f4734", "guid": "bfdfe7dc352907fc980b868725387e9896e445bb140b6aaf21e0096f9eea1f7f"}], "guid": "bfdfe7dc352907fc980b868725387e98861a3d334816d14fbf036fddb5c0a9f5", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9831d6bde4e7854240edb58cd1138c6446", "guid": "bfdfe7dc352907fc980b868725387e98459e8895a6e16fdd3ec84577797d6b1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bdd53919ec1619a2131f0cea3442427", "guid": "bfdfe7dc352907fc980b868725387e9803ed8f8173a07f7c767500ae87943012"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d7ce4f8afb7390bb5d9206f46b3a4b9", "guid": "bfdfe7dc352907fc980b868725387e98393792cab18ea27be580fc5384001d90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810ab36eabffdb9d0786506b4cd58c0e9", "guid": "bfdfe7dc352907fc980b868725387e98dafa530929f7150eea9dc2d6428075ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b12ce92c42d96a261e0fa77f40149f7", "guid": "bfdfe7dc352907fc980b868725387e98c6d4142fc885f77121627ccb23807b0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbb247e6aadc8a239c88dc493c3a50c0", "guid": "bfdfe7dc352907fc980b868725387e98e7a5c6bc2ac22de9524644cf8ae1d466"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983eaf286baa968d2bfccac584772a0b9f", "guid": "bfdfe7dc352907fc980b868725387e988dbcc7b94e9f1683b6a17274e0cd1dab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4be95a2fac93973d82ecb16f3bc968d", "guid": "bfdfe7dc352907fc980b868725387e98d2bad8c33988edaf38aed6893dd0a59c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98038d098a6c29d3f586a95870e429ab37", "guid": "bfdfe7dc352907fc980b868725387e9870aba967536af6bea5da9f3c8795029b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98413339e67bcc0e9e8ec71bbb4d128477", "guid": "bfdfe7dc352907fc980b868725387e98068d46fdf39221ab095b914a78eb37df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cea8714939832acfd32783c56667200e", "guid": "bfdfe7dc352907fc980b868725387e984d520524f0c9f5f00df681c1e28de581"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a42ac4afbaee76021a54ee94fe15de3", "guid": "bfdfe7dc352907fc980b868725387e980755e81ad901f0898e9b14cda13026f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854bc575cc5d77b47c2e76ed1121fe3ee", "guid": "bfdfe7dc352907fc980b868725387e987b2b3d3624b6be839c7752483dc22da9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886158598b03a09912ca6256f2162453f", "guid": "bfdfe7dc352907fc980b868725387e989322aea38cf30be5f4e5de0aee195dad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee67e703fee10f1053e1f9b9df02cf36", "guid": "bfdfe7dc352907fc980b868725387e98c1a88d6f8bc4cdb1ef05a7bed37025b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a55b0df01d14e3aadcceebd09115861", "guid": "bfdfe7dc352907fc980b868725387e9860604891c8db49528452f6ea94703b4f"}], "guid": "bfdfe7dc352907fc980b868725387e9802e2588ec0e7b9ff3cfdd547981ee6f7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9831e748fa35c434cc0f07d820e7512aa7", "guid": "bfdfe7dc352907fc980b868725387e983f1b46ffe6cde8c92d3fc81d4a97684b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818d991a16c5cf18a6a1eafb10636cdd6", "guid": "bfdfe7dc352907fc980b868725387e9848fa8fa5b90f215f813783222e842278"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c4020fa913b688e32db3ca52c1a4086", "guid": "bfdfe7dc352907fc980b868725387e98cf3fc79b25e75194928c00bb50519b81"}], "guid": "bfdfe7dc352907fc980b868725387e9886fbdd03d69453d80b20f630841f5be7", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98f0838834dc11dcc203084128bfab1d28", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e98640b980c297ec330b691b49cc9b7ae87", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}