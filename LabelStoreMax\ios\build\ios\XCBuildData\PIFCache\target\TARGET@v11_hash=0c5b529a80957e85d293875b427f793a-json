{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989142862a7b9c53273831d68c3cbd1754", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9807e7fc10d89a7f80c7d0520af4ea1984", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d78eb5f823757eff0f720ca39b6a3e1f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986383e0e14fb898b1bf5ad81c79bbb52c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d78eb5f823757eff0f720ca39b6a3e1f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f3cf550767b0bc9db33d4e28f472e343", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98aa1c0edc5614ab7c7854e04fa1a4c11f", "guid": "bfdfe7dc352907fc980b868725387e9877412f316b78f0404969719ecafd2708"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ed1bd53dc22b9236a9843b34a998aa1", "guid": "bfdfe7dc352907fc980b868725387e989e6f18e475fd42b86035c49f35175044", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866e6e9f86a231d1f8c913da13739e995", "guid": "bfdfe7dc352907fc980b868725387e98126dce96536cc774642b912cf48a8892"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98028d8474f73d57bf9dc811aea86d201c", "guid": "bfdfe7dc352907fc980b868725387e9855734dbdf08d01dad9a898a727a1d249"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c20b3e8f323d07dc67250064f6103365", "guid": "bfdfe7dc352907fc980b868725387e986e2b941e819fd07555da8fbfa4a0b39e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eeb1f2a37871445464c433279f5c0604", "guid": "bfdfe7dc352907fc980b868725387e98c920eb481b2b43e3033018aaf3c12cc2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866f335a0ea78a07f256fb3821efb5a56", "guid": "bfdfe7dc352907fc980b868725387e9821e9c1439a74782a506a5dcf3f72e2f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b8c06140e392a59c149dee4f8a2ce8a", "guid": "bfdfe7dc352907fc980b868725387e98594f92c16f4cd2050759dd4c0789cc12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872d48e3f127daf7a4ef6e9b15e79f6e7", "guid": "bfdfe7dc352907fc980b868725387e98285f36eee7b6734ec5e3dcba12397418", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834c12ccd8a5530912cb492e1ca1b5179", "guid": "bfdfe7dc352907fc980b868725387e98f061e9a8aa7dfb14dd85b1de8543183d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a70f45ea31ec38d2a55c431b802de8dc", "guid": "bfdfe7dc352907fc980b868725387e98b77dc4e5e7f10c4e7fd07aa99acfc1a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98039796da81b17a88113c525aeb64572e", "guid": "bfdfe7dc352907fc980b868725387e98d8a6ac154754589d8d1b49e4535a9ef5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e33dc6741dc329dded492ce40cbe08fc", "guid": "bfdfe7dc352907fc980b868725387e981ad26ffd9f7a6c7d82a1a46d9066e25a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98808aa1c063dca7b7e8f767f3ccbcd84b", "guid": "bfdfe7dc352907fc980b868725387e987dc31116bae3a62f31067d4c11da1cfd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed46be6c6fd6a48b3ae0c2161d22dd1f", "guid": "bfdfe7dc352907fc980b868725387e983e6fc00b84cbde71e13089a8ccc43387"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988023722c0380aad0be96b829a0fda3d1", "guid": "bfdfe7dc352907fc980b868725387e9865dbcf3b4b037b85bc5956ca024cc13b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874f852516ebae1a7437fbabe53ac2ae0", "guid": "bfdfe7dc352907fc980b868725387e98de8330a5ec194c2c3aa0d25d6ff62346"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6117fc6e8f7ca3a0e0bee923632a60b", "guid": "bfdfe7dc352907fc980b868725387e98ef006cc1e5a5d5fc39d19d5e0c2fc15d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d43d44c2376fcbed356cf86e22d0f7ee", "guid": "bfdfe7dc352907fc980b868725387e987ccd96376b1cd20b928a55a1c4484adb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5eb62fcc4d69c4298767a4b11e4df3c", "guid": "bfdfe7dc352907fc980b868725387e98b155437dbda02998d77f1023d0284284"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833fff502f4c2ebe02010fb0261123d49", "guid": "bfdfe7dc352907fc980b868725387e980aea0d4c800566203f3f6e89d4fee3ad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4473ddead9af71f4d5c14294383de2a", "guid": "bfdfe7dc352907fc980b868725387e988ccec6f05c19d170c2ca5b79120bf34d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983978d2f08964ae6ccad659e703ae3e67", "guid": "bfdfe7dc352907fc980b868725387e9804883b4a3a8b0873b2b5a4a7628ad8e0", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98de4ed717f59dabb1ae557b78d6222750", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9851455d447959dc6d8f93702186d43e68", "guid": "bfdfe7dc352907fc980b868725387e988c0cced15658c63c47e568edbcabe67c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ae35119293ff773b1ea36134d20727c", "guid": "bfdfe7dc352907fc980b868725387e981885feb11fa8f2ee79ce6c9c7e48f7cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897bf879d6fc9765e4925786a228c02d1", "guid": "bfdfe7dc352907fc980b868725387e989137d906646b2dabfc93c9e2bb914d58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4e23e19d3b4b1bafa25dbb23c7a914d", "guid": "bfdfe7dc352907fc980b868725387e986b322804e57a392d27381153b1106dba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987718b479c67fcf4aaa066f20472c214f", "guid": "bfdfe7dc352907fc980b868725387e9847a6be636625aaa3a62742c8ddd9d75a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859719b5a1c9d590f1c96a12a519cf841", "guid": "bfdfe7dc352907fc980b868725387e98c1dd883108bc7d8de93fe67230db3b26"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f8b9d7e0b59782cd5eae6bef88d4d3b", "guid": "bfdfe7dc352907fc980b868725387e98b22f2835466ce2ef78fb04dd68a442a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857ec996582ab60f60a342fc2118d37fe", "guid": "bfdfe7dc352907fc980b868725387e985fa916f59227972b17be4246a3117891"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864532e8cc55ed69b0dbc9aead0109246", "guid": "bfdfe7dc352907fc980b868725387e9825e3847c1d78fb8234948d2bc336dde0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f77ef7662d4a83750d2c76d327efc735", "guid": "bfdfe7dc352907fc980b868725387e9884fb9c9d4ef7bf7482efa7fb6b06061c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854a8493c5592fceac7b3289ec99ac8fe", "guid": "bfdfe7dc352907fc980b868725387e981fee19963b59627dd2fc32d4ee98814b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818c8ea0451c28b77203fc218ab5ee94b", "guid": "bfdfe7dc352907fc980b868725387e9892be62368166ab148bb4c1fdd3ffe91b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b13fc00b8620196ab3602f015784171", "guid": "bfdfe7dc352907fc980b868725387e987d4aa59213945174932b26b7b20d65cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98337d70b4fcf1214e4965970d90e279af", "guid": "bfdfe7dc352907fc980b868725387e98315bfd5b1725da804ea9ff3252e365aa"}], "guid": "bfdfe7dc352907fc980b868725387e9889715ccb64dd7ef591c68be35e10ff70", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9831e748fa35c434cc0f07d820e7512aa7", "guid": "bfdfe7dc352907fc980b868725387e984f3819ef9e5ae2af3494e0c92403bc3f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3cc4fd8ffed88f53cc8841f4a410de3", "guid": "bfdfe7dc352907fc980b868725387e983681acfe4291cd2c4ace87a25764d3a5"}], "guid": "bfdfe7dc352907fc980b868725387e98eba39d390bea53e68b374cd8b4f583e4", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98581004a83215171992f77320ce6f4597", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98e6e68ca93817886e2c03b6c09c07daee", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}