//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import 'package:nylo_framework/nylo_framework.dart';

class NoResultsForProductsWidget extends StatelessWidget {
  const NoResultsForProductsWidget({super.key});

  @override
  Widget build(BuildContext context) => Column(
        children: [
          Text(
            trans("No results"),
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ],
      );
}
