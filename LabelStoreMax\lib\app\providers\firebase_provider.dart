import 'package:firebase_core/firebase_core.dart';

import '/bootstrap/app_helper.dart';
import '/firebase_options.dart';
import '/app/services/fcm_service.dart';
import 'package:nylo_framework/nylo_framework.dart';


class FirebaseProvider implements NyProvider {
  @override
  boot(Nylo nylo) async {
    return null;
  }

  @override
  afterBoot(Nylo nylo) async {
    bool? firebaseFcmIsEnabled =
        AppHelper.instance.appConfig?.firebaseFcmIsEnabled;
    firebaseFcmIsEnabled ??= getEnv('FCM_ENABLED', defaultValue: false);

    if (firebaseFcmIsEnabled != true) return;

    try {
      // Initialize Firebase
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
      print('Firebase: Initialized successfully');

      // Initialize FCM service
      await FCMService().initialize();
      print('FCM: Service initialized successfully');

    } catch (e) {
      print('Firebase: Error during initialization: $e');
      printError(e);
    }
  }
}
