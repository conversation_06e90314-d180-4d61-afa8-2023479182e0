import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import '/bootstrap/app_helper.dart';
import '/firebase_options.dart';
import 'package:nylo_framework/nylo_framework.dart';


class FirebaseProvider implements NyProvider {
  @override
  boot(Nylo nylo) async {
    return null;
  }

  @override
  afterBoot(Nylo nylo) async {
    bool? firebaseFcmIsEnabled =
        AppHelper.instance.appConfig?.firebaseFcmIsEnabled;
    firebaseFcmIsEnabled ??= getEnv('FCM_ENABLED', defaultValue: false);

    if (firebaseFcmIsEnabled != true) return;

    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );

    FirebaseMessaging messaging = FirebaseMessaging.instance;
    NotificationSettings settings = await messaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    if (settings.authorizationStatus != AuthorizationStatus.authorized) {
      return;
    }

    // TODO: Implement user authentication with WooCommerce
    // WpUser? wpUser = await WPJsonAPI.wpUser();
    // if (wpUser != null && wpUser.id != null) {
    //   // Store user ID in NyStorage or similar
    // }

    try {
      String? token = await messaging.getToken();
      if (token != null) {
        // TODO: Store FCM token for push notifications
        await NyStorage.save('fcm_token', token);
      }
    } catch (e) {
      printError(e);
    }
  }
}
