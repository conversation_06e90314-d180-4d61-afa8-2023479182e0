//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';
import 'package:nylo_framework/nylo_framework.dart';

class WooCommerceService {
  static final WooCommerceService _instance = WooCommerceService._internal();
  late WooCommerce _wooCommerce;

  factory WooCommerceService() {
    return _instance;
  }

  WooCommerceService._internal() {
    _wooCommerce = WooCommerce(
      baseUrl: getEnv('WOO_BASE_URL', defaultValue: 'https://velvete.ly'),
      username: getEnv('WOO_CONSUMER_KEY', defaultValue: 'ck_18128d84570a5c30870239c83e9cfcae0be0b4f9'),
      password: getEnv('WOO_CONSUMER_SECRET', defaultValue: 'cs_e079d293dbffc0b44387b7eba6d8704f83638cb2'),
      useFaker: false,
      isDebug: getEnv('APP_DEBUG', defaultValue: 'true') == 'true',
    );
  }

  WooCommerce get wooCommerce => _wooCommerce;

  /// Products API methods
  Future<List<WooProduct>> getProducts({
    int page = 1,
    int perPage = 10,
    String? search,
    List<int>? categories,
    String orderBy = 'date',
    String order = 'desc',
    String? status,
    bool? featured,
    String? sku,
    String? slug,
  }) async {
    try {
      return await _woocommerceClient.getProducts(
        page: page,
        perPage: perPage,
        search: search,
        category: categories?.join(','),
        orderBy: orderBy,
        order: order,
        status: status,
        featured: featured,
        sku: sku,
        slug: slug,
      );
    } catch (e) {
      throw Exception('Failed to fetch products: $e');
    }
  }

  Future<WooProduct> getProduct(int productId) async {
    try {
      return await _woocommerceClient.getProductById(productId);
    } catch (e) {
      throw Exception('Failed to fetch product: $e');
    }
  }

  Future<List<WooProductVariation>> getProductVariations(int productId) async {
    try {
      return await _woocommerceClient.getProductVariations(productId);
    } catch (e) {
      throw Exception('Failed to fetch product variations: $e');
    }
  }

  /// Categories API methods
  Future<List<WooProductCategory>> getCategories({
    int page = 1,
    int perPage = 100,
    String orderBy = 'name',
    String order = 'asc',
    bool hideEmpty = true,
    int? parent,
  }) async {
    try {
      return await _woocommerceClient.getProductCategories(
        page: page,
        perPage: perPage,
        orderBy: orderBy,
        order: order,
        hideEmpty: hideEmpty,
        parent: parent,
      );
    } catch (e) {
      throw Exception('Failed to fetch categories: $e');
    }
  }

  /// Orders API methods
  Future<WooOrder> createOrder(WooOrderPayload orderPayload) async {
    try {
      return await _woocommerceClient.createOrder(orderPayload);
    } catch (e) {
      throw Exception('Failed to create order: $e');
    }
  }

  Future<List<WooOrder>> getOrders({
    int page = 1,
    int perPage = 10,
    String? status,
    int? customer,
    String? orderBy,
    String? order,
  }) async {
    try {
      return await _woocommerceClient.getOrders(
        page: page,
        perPage: perPage,
        status: status,
        customer: customer,
        orderBy: orderBy,
        order: order,
      );
    } catch (e) {
      throw Exception('Failed to fetch orders: $e');
    }
  }

  /// Customers API methods
  Future<WooCustomer> createCustomer(WooCustomer customer) async {
    try {
      return await _woocommerceClient.createCustomer(customer);
    } catch (e) {
      throw Exception('Failed to create customer: $e');
    }
  }

  Future<WooCustomer> getCustomer(int customerId) async {
    try {
      return await _woocommerceClient.getCustomerById(customerId);
    } catch (e) {
      throw Exception('Failed to fetch customer: $e');
    }
  }

  /// Coupons API methods
  Future<List<WooCoupon>> getCoupons({
    int page = 1,
    int perPage = 10,
    String? search,
    String? code,
  }) async {
    try {
      return await _woocommerceClient.getCoupons(
        page: page,
        perPage: perPage,
        search: search,
        code: code,
      );
    } catch (e) {
      throw Exception('Failed to fetch coupons: $e');
    }
  }

  /// Shipping methods
  Future<List<WooShippingZone>> getShippingZones() async {
    try {
      return await _woocommerceClient.getShippingZones();
    } catch (e) {
      throw Exception('Failed to fetch shipping zones: $e');
    }
  }

  Future<List<WooShippingMethod>> getShippingMethods(int zoneId) async {
    try {
      return await _woocommerceClient.getShippingZoneMethods(zoneId);
    } catch (e) {
      throw Exception('Failed to fetch shipping methods: $e');
    }
  }

  /// Tax rates
  Future<List<WooTaxRate>> getTaxRates({
    String? taxClass,
  }) async {
    try {
      return await _woocommerceClient.getTaxRates(taxClass: taxClass);
    } catch (e) {
      throw Exception('Failed to fetch tax rates: $e');
    }
  }

  /// Product reviews
  Future<List<WooProductReview>> getProductReviews(int productId, {
    int page = 1,
    int perPage = 10,
  }) async {
    try {
      return await _woocommerceClient.getProductReviews(
        productId: productId,
        page: page,
        perPage: perPage,
      );
    } catch (e) {
      throw Exception('Failed to fetch product reviews: $e');
    }
  }

  Future<WooProductReview> createProductReview(WooProductReview review) async {
    try {
      return await _woocommerceClient.createProductReview(review);
    } catch (e) {
      throw Exception('Failed to create product review: $e');
    }
  }
}
