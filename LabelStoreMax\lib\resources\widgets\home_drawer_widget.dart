//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/resources/widgets/store_logo_widget.dart';
import '/resources/pages/account_detail_page.dart';
import '/resources/pages/account_login_page.dart';
import '/resources/pages/cart_page.dart';
import '/resources/pages/wishlist_page_widget.dart';
import 'package:wp_json_api/wp_json_api.dart';
import '/resources/pages/browse_category_page.dart';
import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';
import '/bootstrap/app_helper.dart';
import '/bootstrap/helpers.dart';
import '/resources/widgets/app_version_widget.dart';

import 'package:nylo_framework/theme/helper/ny_theme.dart';
import 'package:nylo_framework/nylo_framework.dart';


class HomeDrawerWidget extends StatefulWidget {
  const HomeDrawerWidget(
      {super.key,
      this.productCategories = const []});

  final List<WooProductCategory> productCategories;

  @override
  createState() => _HomeDrawerWidgetState();
}

class _HomeDrawerWidgetState extends State<HomeDrawerWidget> {
  List<Map<String, String>> _menuLinks = []; // Simplified menu links
  String? _themeType;

  @override
  void initState() {
    super.initState();
    _menuLinks = []; // TODO: Add menu links configuration
    _themeType = AppHelper.instance.appConfig!.theme;
  }

  @override
  Widget build(BuildContext context) {
    bool isDark = (Theme.of(context).brightness == Brightness.dark);
    return Drawer(
      child: Container(
        color: ThemeColor.get(context).background,
        child: ListView(
          padding: EdgeInsets.zero,
          children: <Widget>[
            DrawerHeader(
              decoration: BoxDecoration(
                color: ThemeColor.get(context).background,
              ),
              child: Center(child: StoreLogo()),
            ),
            if (["compo"].contains(_themeType) == false)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.only(left: 16, top: 8, bottom: 8),
                    child: Text(
                      trans("Menu"),
                      style: Theme.of(context).textTheme.titleSmall!.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                  ),
                  ListTile(
                    title: Text(
                      trans("Profile"),
                      style: Theme.of(context)
                          .textTheme
                          .bodyMedium!
                          .copyWith(fontSize: 16),
                    ),
                    leading: Icon(Icons.account_circle),
                    onTap: _actionProfile,
                  ),
                    ListTile(
                      title: Text(
                        trans("Wishlist"),
                        style: Theme.of(context)
                            .textTheme
                            .bodyMedium!
                            .copyWith(fontSize: 16),
                      ),
                      leading: Icon(Icons.favorite_border),
                      onTap: _actionWishlist,
                    ),
                  ListTile(
                    title: Text(
                      trans("Cart"),
                      style: Theme.of(context)
                          .textTheme
                          .bodyMedium!
                          .copyWith(fontSize: 16),
                    ),
                    leading: Icon(Icons.shopping_cart),
                    onTap: _actionCart,
                  ),
                ],
              ),
            if (widget.productCategories.isNotEmpty)
              Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Padding(
                      padding: EdgeInsets.only(left: 16, top: 8, bottom: 8),
                      child: Text(
                        trans("Categories".tr()),
                        style: Theme.of(context).textTheme.titleSmall!.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    ...widget.productCategories.map((collection) {
                      return ListTile(
                        title: Text(
                          collection.name ?? "",
                          style: Theme.of(context)
                              .textTheme
                              .bodyMedium!
                              .copyWith(fontSize: 16),
                        ),
                        trailing: Icon(Icons.keyboard_arrow_right_rounded),
                        onTap: () {
                          routeTo(BrowseCategoryPage.path, data: collection);
                        },
                      );
                    })
                  ]),
            // TODO: Add terms and privacy links configuration
              ListTile(
                title: Text(
                  trans("Privacy policy"),
                  style: Theme.of(context)
                      .textTheme
                      .bodyMedium!
                      .copyWith(fontSize: 16),
                ),
                trailing: Icon(Icons.keyboard_arrow_right_rounded),
                leading: Icon(Icons.account_balance),
                onTap: () {}, // TODO: Add privacy action
              ),
            ListTile(
              title: Text(trans((isDark ? "Light Mode" : "Dark Mode")),
                  style: Theme.of(context)
                      .textTheme
                      .bodyMedium!
                      .copyWith(fontSize: 16)),
              leading: Icon(Icons.brightness_4_rounded),
              onTap: () {
                setState(() {
                  NyTheme.set(context,
                      id: isDark
                          ? "default_light_theme"
                          : "default_dark_theme");
                });
              },
            ),
            if (_menuLinks.isNotEmpty)
              Padding(
                padding: EdgeInsets.only(left: 16, top: 8, bottom: 8),
                child: Text(
                  trans("Social"),
                  style: Theme.of(context).textTheme.titleSmall,
                ),
              ),
            // TODO: Add menu links when configuration is available
            ListTile(
              title: Text("Change language".tr()),
              leading: Icon(Icons.language),
              onTap: () {
                NyLanguageSwitcher.showBottomModal(context);
              },
            ),
            ListTile(
              title: AppVersionWidget(),
            ),
          ],
        ),
      ),
    );
  }

  // TODO: Add terms and privacy actions when configuration is available

  _actionProfile() async {
    Navigator.pop(context);
    if (!(await WPJsonAPI.wpUserLoggedIn())) {
      UserAuth.instance.redirect = AccountDetailPage.path.name;
      routeTo(AccountLoginPage.path);
      return;
    }
    routeTo(AccountDetailPage.path);
  }

  /// Wishlist action
  _actionWishlist() async {
    Navigator.pop(context);
    routeTo(WishListPageWidget.path);
  }

  /// Cart action
  _actionCart() {
    Navigator.pop(context);
    routeTo(CartPage.path);
  }
}
