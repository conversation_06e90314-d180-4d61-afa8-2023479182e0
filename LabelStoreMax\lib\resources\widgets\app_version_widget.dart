//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import 'package:nylo_framework/nylo_framework.dart';
import 'package:package_info_plus/package_info_plus.dart';

class AppVersionWidget extends StatelessWidget {
  const AppVersionWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return NyFutureBuilder<PackageInfo>(
      future: PackageInfo.fromPlatform(),
      child: (BuildContext context, data) => Padding(
        padding: EdgeInsets.only(top: 15, bottom: 15),
        child: Text("${trans("Version")}: ${data?.version}",
            style: Theme.of(context)
                .textTheme
                .bodyMedium!
                .copyWith(fontWeight: FontWeight.w300)),
      ),
      loadingStyle: LoadingStyle.none(),
    );
  }
}
