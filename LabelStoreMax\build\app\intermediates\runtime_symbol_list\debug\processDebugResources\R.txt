int anim abc_fade_in 0x7f010000
int anim abc_fade_out 0x7f010001
int anim abc_grow_fade_in_from_bottom 0x7f010002
int anim abc_popup_enter 0x7f010003
int anim abc_popup_exit 0x7f010004
int anim abc_shrink_fade_out_from_bottom 0x7f010005
int anim abc_slide_in_bottom 0x7f010006
int anim abc_slide_in_top 0x7f010007
int anim abc_slide_out_bottom 0x7f010008
int anim abc_slide_out_top 0x7f010009
int anim abc_tooltip_enter 0x7f01000a
int anim abc_tooltip_exit 0x7f01000b
int anim btn_checkbox_to_checked_box_inner_merged_animation 0x7f01000c
int anim btn_checkbox_to_checked_box_outer_merged_animation 0x7f01000d
int anim btn_checkbox_to_checked_icon_null_animation 0x7f01000e
int anim btn_checkbox_to_unchecked_box_inner_merged_animation 0x7f01000f
int anim btn_checkbox_to_unchecked_check_path_merged_animation 0x7f010010
int anim btn_checkbox_to_unchecked_icon_null_animation 0x7f010011
int anim btn_radio_to_off_mtrl_dot_group_animation 0x7f010012
int anim btn_radio_to_off_mtrl_ring_outer_animation 0x7f010013
int anim btn_radio_to_off_mtrl_ring_outer_path_animation 0x7f010014
int anim btn_radio_to_on_mtrl_dot_group_animation 0x7f010015
int anim btn_radio_to_on_mtrl_ring_outer_animation 0x7f010016
int anim btn_radio_to_on_mtrl_ring_outer_path_animation 0x7f010017
int anim design_bottom_sheet_slide_in 0x7f010018
int anim design_bottom_sheet_slide_out 0x7f010019
int anim design_snackbar_in 0x7f01001a
int anim design_snackbar_out 0x7f01001b
int anim fragment_fast_out_extra_slow_in 0x7f01001c
int anim linear_indeterminate_line1_head_interpolator 0x7f01001d
int anim linear_indeterminate_line1_tail_interpolator 0x7f01001e
int anim linear_indeterminate_line2_head_interpolator 0x7f01001f
int anim linear_indeterminate_line2_tail_interpolator 0x7f010020
int anim m3_bottom_sheet_slide_in 0x7f010021
int anim m3_bottom_sheet_slide_out 0x7f010022
int anim m3_motion_fade_enter 0x7f010023
int anim m3_motion_fade_exit 0x7f010024
int anim m3_side_sheet_enter_from_left 0x7f010025
int anim m3_side_sheet_enter_from_right 0x7f010026
int anim m3_side_sheet_exit_to_left 0x7f010027
int anim m3_side_sheet_exit_to_right 0x7f010028
int anim mtrl_bottom_sheet_slide_in 0x7f010029
int anim mtrl_bottom_sheet_slide_out 0x7f01002a
int anim mtrl_card_lowers_interpolator 0x7f01002b
int anim stripe_3ds2_challenge_transition_slide_in 0x7f01002c
int anim stripe_3ds2_challenge_transition_slide_out 0x7f01002d
int anim stripe_card_widget_progress_fade_in 0x7f01002e
int anim stripe_card_widget_progress_fade_out 0x7f01002f
int anim stripe_slide_down 0x7f010030
int anim stripe_slide_up 0x7f010031
int anim stripe_transition_fade_in 0x7f010032
int anim stripe_transition_fade_out 0x7f010033
int animator design_appbar_state_list_animator 0x7f020000
int animator design_fab_hide_motion_spec 0x7f020001
int animator design_fab_show_motion_spec 0x7f020002
int animator fragment_close_enter 0x7f020003
int animator fragment_close_exit 0x7f020004
int animator fragment_fade_enter 0x7f020005
int animator fragment_fade_exit 0x7f020006
int animator fragment_open_enter 0x7f020007
int animator fragment_open_exit 0x7f020008
int animator m3_appbar_state_list_animator 0x7f020009
int animator m3_btn_elevated_btn_state_list_anim 0x7f02000a
int animator m3_btn_state_list_anim 0x7f02000b
int animator m3_card_elevated_state_list_anim 0x7f02000c
int animator m3_card_state_list_anim 0x7f02000d
int animator m3_chip_state_list_anim 0x7f02000e
int animator m3_elevated_chip_state_list_anim 0x7f02000f
int animator m3_extended_fab_change_size_collapse_motion_spec 0x7f020010
int animator m3_extended_fab_change_size_expand_motion_spec 0x7f020011
int animator m3_extended_fab_hide_motion_spec 0x7f020012
int animator m3_extended_fab_show_motion_spec 0x7f020013
int animator m3_extended_fab_state_list_animator 0x7f020014
int animator mtrl_btn_state_list_anim 0x7f020015
int animator mtrl_btn_unelevated_state_list_anim 0x7f020016
int animator mtrl_card_state_list_anim 0x7f020017
int animator mtrl_chip_state_list_anim 0x7f020018
int animator mtrl_extended_fab_change_size_collapse_motion_spec 0x7f020019
int animator mtrl_extended_fab_change_size_expand_motion_spec 0x7f02001a
int animator mtrl_extended_fab_hide_motion_spec 0x7f02001b
int animator mtrl_extended_fab_show_motion_spec 0x7f02001c
int animator mtrl_extended_fab_state_list_animator 0x7f02001d
int animator mtrl_fab_hide_motion_spec 0x7f02001e
int animator mtrl_fab_show_motion_spec 0x7f02001f
int animator mtrl_fab_transformation_sheet_collapse_spec 0x7f020020
int animator mtrl_fab_transformation_sheet_expand_spec 0x7f020021
int attr SharedValue 0x7f030000
int attr SharedValueId 0x7f030001
int attr action 0x7f030002
int attr actionBarDivider 0x7f030003
int attr actionBarItemBackground 0x7f030004
int attr actionBarPopupTheme 0x7f030005
int attr actionBarSize 0x7f030006
int attr actionBarSplitStyle 0x7f030007
int attr actionBarStyle 0x7f030008
int attr actionBarTabBarStyle 0x7f030009
int attr actionBarTabStyle 0x7f03000a
int attr actionBarTabTextStyle 0x7f03000b
int attr actionBarTheme 0x7f03000c
int attr actionBarWidgetTheme 0x7f03000d
int attr actionButtonStyle 0x7f03000e
int attr actionDropDownStyle 0x7f03000f
int attr actionLayout 0x7f030010
int attr actionMenuTextAppearance 0x7f030011
int attr actionMenuTextColor 0x7f030012
int attr actionModeBackground 0x7f030013
int attr actionModeCloseButtonStyle 0x7f030014
int attr actionModeCloseContentDescription 0x7f030015
int attr actionModeCloseDrawable 0x7f030016
int attr actionModeCopyDrawable 0x7f030017
int attr actionModeCutDrawable 0x7f030018
int attr actionModeFindDrawable 0x7f030019
int attr actionModePasteDrawable 0x7f03001a
int attr actionModePopupWindowStyle 0x7f03001b
int attr actionModeSelectAllDrawable 0x7f03001c
int attr actionModeShareDrawable 0x7f03001d
int attr actionModeSplitBackground 0x7f03001e
int attr actionModeStyle 0x7f03001f
int attr actionModeTheme 0x7f030020
int attr actionModeWebSearchDrawable 0x7f030021
int attr actionOverflowButtonStyle 0x7f030022
int attr actionOverflowMenuStyle 0x7f030023
int attr actionProviderClass 0x7f030024
int attr actionTextColorAlpha 0x7f030025
int attr actionViewClass 0x7f030026
int attr activeIndicatorLabelPadding 0x7f030027
int attr activityAction 0x7f030028
int attr activityChooserViewStyle 0x7f030029
int attr activityName 0x7f03002a
int attr addElevationShadow 0x7f03002b
int attr adjustable 0x7f03002c
int attr alertDialogButtonGroupStyle 0x7f03002d
int attr alertDialogCenterButtons 0x7f03002e
int attr alertDialogStyle 0x7f03002f
int attr alertDialogTheme 0x7f030030
int attr allowDividerAbove 0x7f030031
int attr allowDividerAfterLastItem 0x7f030032
int attr allowDividerBelow 0x7f030033
int attr allowStacking 0x7f030034
int attr alpha 0x7f030035
int attr alphabeticModifiers 0x7f030036
int attr altSrc 0x7f030037
int attr alwaysExpand 0x7f030038
int attr ambientEnabled 0x7f030039
int attr animateCircleAngleTo 0x7f03003a
int attr animateMenuItems 0x7f03003b
int attr animateNavigationIcon 0x7f03003c
int attr animateRelativeTo 0x7f03003d
int attr animationBackgroundColor 0x7f03003e
int attr animationMode 0x7f03003f
int attr appBarLayoutStyle 0x7f030040
int attr appTheme 0x7f030041
int attr applyMotionScene 0x7f030042
int attr arcMode 0x7f030043
int attr argType 0x7f030044
int attr arrowHeadLength 0x7f030045
int attr arrowShaftLength 0x7f030046
int attr attributeName 0x7f030047
int attr autoAdjustToWithinGrandparentBounds 0x7f030048
int attr autoCompleteMode 0x7f030049
int attr autoCompleteTextViewStyle 0x7f03004a
int attr autoShowKeyboard 0x7f03004b
int attr autoSizeMaxTextSize 0x7f03004c
int attr autoSizeMinTextSize 0x7f03004d
int attr autoSizePresetSizes 0x7f03004e
int attr autoSizeStepGranularity 0x7f03004f
int attr autoSizeTextType 0x7f030050
int attr autoTransition 0x7f030051
int attr backHandlingEnabled 0x7f030052
int attr background 0x7f030053
int attr backgroundColor 0x7f030054
int attr backgroundColorStateList 0x7f030055
int attr backgroundInsetBottom 0x7f030056
int attr backgroundInsetEnd 0x7f030057
int attr backgroundInsetStart 0x7f030058
int attr backgroundInsetTop 0x7f030059
int attr backgroundOverlayColorAlpha 0x7f03005a
int attr backgroundSplit 0x7f03005b
int attr backgroundStacked 0x7f03005c
int attr backgroundTint 0x7f03005d
int attr backgroundTintMode 0x7f03005e
int attr badgeGravity 0x7f03005f
int attr badgeHeight 0x7f030060
int attr badgeRadius 0x7f030061
int attr badgeShapeAppearance 0x7f030062
int attr badgeShapeAppearanceOverlay 0x7f030063
int attr badgeStyle 0x7f030064
int attr badgeText 0x7f030065
int attr badgeTextAppearance 0x7f030066
int attr badgeTextColor 0x7f030067
int attr badgeVerticalPadding 0x7f030068
int attr badgeWidePadding 0x7f030069
int attr badgeWidth 0x7f03006a
int attr badgeWithTextHeight 0x7f03006b
int attr badgeWithTextRadius 0x7f03006c
int attr badgeWithTextShapeAppearance 0x7f03006d
int attr badgeWithTextShapeAppearanceOverlay 0x7f03006e
int attr badgeWithTextWidth 0x7f03006f
int attr barLength 0x7f030070
int attr barrierAllowsGoneWidgets 0x7f030071
int attr barrierDirection 0x7f030072
int attr barrierMargin 0x7f030073
int attr behavior_autoHide 0x7f030074
int attr behavior_autoShrink 0x7f030075
int attr behavior_draggable 0x7f030076
int attr behavior_expandedOffset 0x7f030077
int attr behavior_fitToContents 0x7f030078
int attr behavior_halfExpandedRatio 0x7f030079
int attr behavior_hideable 0x7f03007a
int attr behavior_overlapTop 0x7f03007b
int attr behavior_peekHeight 0x7f03007c
int attr behavior_saveFlags 0x7f03007d
int attr behavior_significantVelocityThreshold 0x7f03007e
int attr behavior_skipCollapsed 0x7f03007f
int attr blendSrc 0x7f030080
int attr borderRound 0x7f030081
int attr borderRoundPercent 0x7f030082
int attr borderWidth 0x7f030083
int attr borderlessButtonStyle 0x7f030084
int attr bottomAppBarStyle 0x7f030085
int attr bottomInsetScrimEnabled 0x7f030086
int attr bottomNavigationStyle 0x7f030087
int attr bottomSheetDialogTheme 0x7f030088
int attr bottomSheetDragHandleStyle 0x7f030089
int attr bottomSheetStyle 0x7f03008a
int attr boxBackgroundColor 0x7f03008b
int attr boxBackgroundMode 0x7f03008c
int attr boxCollapsedPaddingTop 0x7f03008d
int attr boxCornerRadiusBottomEnd 0x7f03008e
int attr boxCornerRadiusBottomStart 0x7f03008f
int attr boxCornerRadiusTopEnd 0x7f030090
int attr boxCornerRadiusTopStart 0x7f030091
int attr boxStrokeColor 0x7f030092
int attr boxStrokeErrorColor 0x7f030093
int attr boxStrokeWidth 0x7f030094
int attr boxStrokeWidthFocused 0x7f030095
int attr brightness 0x7f030096
int attr buttonBarButtonStyle 0x7f030097
int attr buttonBarNegativeButtonStyle 0x7f030098
int attr buttonBarNeutralButtonStyle 0x7f030099
int attr buttonBarPositiveButtonStyle 0x7f03009a
int attr buttonBarStyle 0x7f03009b
int attr buttonCompat 0x7f03009c
int attr buttonGravity 0x7f03009d
int attr buttonIcon 0x7f03009e
int attr buttonIconDimen 0x7f03009f
int attr buttonIconTint 0x7f0300a0
int attr buttonIconTintMode 0x7f0300a1
int attr buttonPanelSideLayout 0x7f0300a2
int attr buttonSize 0x7f0300a3
int attr buttonStyle 0x7f0300a4
int attr buttonStyleSmall 0x7f0300a5
int attr buttonTheme 0x7f0300a6
int attr buttonTint 0x7f0300a7
int attr buttonTintMode 0x7f0300a8
int attr buyButtonAppearance 0x7f0300a9
int attr buyButtonHeight 0x7f0300aa
int attr buyButtonText 0x7f0300ab
int attr buyButtonWidth 0x7f0300ac
int attr cameraBearing 0x7f0300ad
int attr cameraMaxZoomPreference 0x7f0300ae
int attr cameraMinZoomPreference 0x7f0300af
int attr cameraTargetLat 0x7f0300b0
int attr cameraTargetLng 0x7f0300b1
int attr cameraTilt 0x7f0300b2
int attr cameraZoom 0x7f0300b3
int attr cardBackgroundColor 0x7f0300b4
int attr cardCornerRadius 0x7f0300b5
int attr cardElevation 0x7f0300b6
int attr cardForegroundColor 0x7f0300b7
int attr cardFormStyle 0x7f0300b8
int attr cardHintText 0x7f0300b9
int attr cardMaxElevation 0x7f0300ba
int attr cardPreventCornerOverlap 0x7f0300bb
int attr cardTextErrorColor 0x7f0300bc
int attr cardTint 0x7f0300bd
int attr cardUseCompatPadding 0x7f0300be
int attr cardViewStyle 0x7f0300bf
int attr carousel_alignment 0x7f0300c0
int attr carousel_backwardTransition 0x7f0300c1
int attr carousel_emptyViewsBehavior 0x7f0300c2
int attr carousel_firstView 0x7f0300c3
int attr carousel_forwardTransition 0x7f0300c4
int attr carousel_infinite 0x7f0300c5
int attr carousel_nextState 0x7f0300c6
int attr carousel_previousState 0x7f0300c7
int attr carousel_touchUpMode 0x7f0300c8
int attr carousel_touchUp_dampeningFactor 0x7f0300c9
int attr carousel_touchUp_velocityThreshold 0x7f0300ca
int attr centerIfNoTextEnabled 0x7f0300cb
int attr chainUseRtl 0x7f0300cc
int attr checkBoxPreferenceStyle 0x7f0300cd
int attr checkMarkCompat 0x7f0300ce
int attr checkMarkTint 0x7f0300cf
int attr checkMarkTintMode 0x7f0300d0
int attr checkboxStyle 0x7f0300d1
int attr checkedButton 0x7f0300d2
int attr checkedChip 0x7f0300d3
int attr checkedIcon 0x7f0300d4
int attr checkedIconEnabled 0x7f0300d5
int attr checkedIconGravity 0x7f0300d6
int attr checkedIconMargin 0x7f0300d7
int attr checkedIconSize 0x7f0300d8
int attr checkedIconTint 0x7f0300d9
int attr checkedIconVisible 0x7f0300da
int attr checkedState 0x7f0300db
int attr checkedTextViewStyle 0x7f0300dc
int attr chipBackgroundColor 0x7f0300dd
int attr chipCornerRadius 0x7f0300de
int attr chipEndPadding 0x7f0300df
int attr chipGroupStyle 0x7f0300e0
int attr chipIcon 0x7f0300e1
int attr chipIconEnabled 0x7f0300e2
int attr chipIconSize 0x7f0300e3
int attr chipIconTint 0x7f0300e4
int attr chipIconVisible 0x7f0300e5
int attr chipMinHeight 0x7f0300e6
int attr chipMinTouchTargetSize 0x7f0300e7
int attr chipSpacing 0x7f0300e8
int attr chipSpacingHorizontal 0x7f0300e9
int attr chipSpacingVertical 0x7f0300ea
int attr chipStandaloneStyle 0x7f0300eb
int attr chipStartPadding 0x7f0300ec
int attr chipStrokeColor 0x7f0300ed
int attr chipStrokeWidth 0x7f0300ee
int attr chipStyle 0x7f0300ef
int attr chipSurfaceColor 0x7f0300f0
int attr circleCrop 0x7f0300f1
int attr circleRadius 0x7f0300f2
int attr circularProgressIndicatorStyle 0x7f0300f3
int attr circularflow_angles 0x7f0300f4
int attr circularflow_defaultAngle 0x7f0300f5
int attr circularflow_defaultRadius 0x7f0300f6
int attr circularflow_radiusInDP 0x7f0300f7
int attr circularflow_viewCenter 0x7f0300f8
int attr clearTop 0x7f0300f9
int attr clearsTag 0x7f0300fa
int attr clickAction 0x7f0300fb
int attr clockFaceBackgroundColor 0x7f0300fc
int attr clockHandColor 0x7f0300fd
int attr clockIcon 0x7f0300fe
int attr clockNumberTextColor 0x7f0300ff
int attr closeIcon 0x7f030100
int attr closeIconEnabled 0x7f030101
int attr closeIconEndPadding 0x7f030102
int attr closeIconSize 0x7f030103
int attr closeIconStartPadding 0x7f030104
int attr closeIconTint 0x7f030105
int attr closeIconVisible 0x7f030106
int attr closeItemLayout 0x7f030107
int attr collapseContentDescription 0x7f030108
int attr collapseIcon 0x7f030109
int attr collapsedSize 0x7f03010a
int attr collapsedTitleGravity 0x7f03010b
int attr collapsedTitleTextAppearance 0x7f03010c
int attr collapsedTitleTextColor 0x7f03010d
int attr collapsingToolbarLayoutLargeSize 0x7f03010e
int attr collapsingToolbarLayoutLargeStyle 0x7f03010f
int attr collapsingToolbarLayoutMediumSize 0x7f030110
int attr collapsingToolbarLayoutMediumStyle 0x7f030111
int attr collapsingToolbarLayoutStyle 0x7f030112
int attr color 0x7f030113
int attr colorAccent 0x7f030114
int attr colorBackgroundFloating 0x7f030115
int attr colorButtonNormal 0x7f030116
int attr colorContainer 0x7f030117
int attr colorControlActivated 0x7f030118
int attr colorControlHighlight 0x7f030119
int attr colorControlNormal 0x7f03011a
int attr colorError 0x7f03011b
int attr colorErrorContainer 0x7f03011c
int attr colorOnBackground 0x7f03011d
int attr colorOnContainer 0x7f03011e
int attr colorOnContainerUnchecked 0x7f03011f
int attr colorOnError 0x7f030120
int attr colorOnErrorContainer 0x7f030121
int attr colorOnPrimary 0x7f030122
int attr colorOnPrimaryContainer 0x7f030123
int attr colorOnPrimaryFixed 0x7f030124
int attr colorOnPrimaryFixedVariant 0x7f030125
int attr colorOnPrimarySurface 0x7f030126
int attr colorOnSecondary 0x7f030127
int attr colorOnSecondaryContainer 0x7f030128
int attr colorOnSecondaryFixed 0x7f030129
int attr colorOnSecondaryFixedVariant 0x7f03012a
int attr colorOnSurface 0x7f03012b
int attr colorOnSurfaceInverse 0x7f03012c
int attr colorOnSurfaceVariant 0x7f03012d
int attr colorOnTertiary 0x7f03012e
int attr colorOnTertiaryContainer 0x7f03012f
int attr colorOnTertiaryFixed 0x7f030130
int attr colorOnTertiaryFixedVariant 0x7f030131
int attr colorOutline 0x7f030132
int attr colorOutlineVariant 0x7f030133
int attr colorPrimary 0x7f030134
int attr colorPrimaryContainer 0x7f030135
int attr colorPrimaryDark 0x7f030136
int attr colorPrimaryFixed 0x7f030137
int attr colorPrimaryFixedDim 0x7f030138
int attr colorPrimaryInverse 0x7f030139
int attr colorPrimarySurface 0x7f03013a
int attr colorPrimaryVariant 0x7f03013b
int attr colorScheme 0x7f03013c
int attr colorSecondary 0x7f03013d
int attr colorSecondaryContainer 0x7f03013e
int attr colorSecondaryFixed 0x7f03013f
int attr colorSecondaryFixedDim 0x7f030140
int attr colorSecondaryVariant 0x7f030141
int attr colorSurface 0x7f030142
int attr colorSurfaceBright 0x7f030143
int attr colorSurfaceContainer 0x7f030144
int attr colorSurfaceContainerHigh 0x7f030145
int attr colorSurfaceContainerHighest 0x7f030146
int attr colorSurfaceContainerLow 0x7f030147
int attr colorSurfaceContainerLowest 0x7f030148
int attr colorSurfaceDim 0x7f030149
int attr colorSurfaceInverse 0x7f03014a
int attr colorSurfaceVariant 0x7f03014b
int attr colorSwitchThumbNormal 0x7f03014c
int attr colorTertiary 0x7f03014d
int attr colorTertiaryContainer 0x7f03014e
int attr colorTertiaryFixed 0x7f03014f
int attr colorTertiaryFixedDim 0x7f030150
int attr commitIcon 0x7f030151
int attr companyName 0x7f030152
int attr compatShadowEnabled 0x7f030153
int attr constraintRotate 0x7f030154
int attr constraintSet 0x7f030155
int attr constraintSetEnd 0x7f030156
int attr constraintSetStart 0x7f030157
int attr constraint_referenced_ids 0x7f030158
int attr constraint_referenced_tags 0x7f030159
int attr constraints 0x7f03015a
int attr content 0x7f03015b
int attr contentDescription 0x7f03015c
int attr contentInsetEnd 0x7f03015d
int attr contentInsetEndWithActions 0x7f03015e
int attr contentInsetLeft 0x7f03015f
int attr contentInsetRight 0x7f030160
int attr contentInsetStart 0x7f030161
int attr contentInsetStartWithNavigation 0x7f030162
int attr contentPadding 0x7f030163
int attr contentPaddingBottom 0x7f030164
int attr contentPaddingEnd 0x7f030165
int attr contentPaddingLeft 0x7f030166
int attr contentPaddingRight 0x7f030167
int attr contentPaddingStart 0x7f030168
int attr contentPaddingTop 0x7f030169
int attr contentScrim 0x7f03016a
int attr contrast 0x7f03016b
int attr controlBackground 0x7f03016c
int attr coordinatorLayoutStyle 0x7f03016d
int attr coplanarSiblingViewId 0x7f03016e
int attr cornerFamily 0x7f03016f
int attr cornerFamilyBottomLeft 0x7f030170
int attr cornerFamilyBottomRight 0x7f030171
int attr cornerFamilyTopLeft 0x7f030172
int attr cornerFamilyTopRight 0x7f030173
int attr cornerRadius 0x7f030174
int attr cornerSize 0x7f030175
int attr cornerSizeBottomLeft 0x7f030176
int attr cornerSizeBottomRight 0x7f030177
int attr cornerSizeTopLeft 0x7f030178
int attr cornerSizeTopRight 0x7f030179
int attr counterEnabled 0x7f03017a
int attr counterMaxLength 0x7f03017b
int attr counterOverflowTextAppearance 0x7f03017c
int attr counterOverflowTextColor 0x7f03017d
int attr counterTextAppearance 0x7f03017e
int attr counterTextColor 0x7f03017f
int attr countryAutoCompleteStyle 0x7f030180
int attr countryItemLayout 0x7f030181
int attr crossfade 0x7f030182
int attr currentState 0x7f030183
int attr cursorColor 0x7f030184
int attr cursorErrorColor 0x7f030185
int attr curveFit 0x7f030186
int attr customBoolean 0x7f030187
int attr customColorDrawableValue 0x7f030188
int attr customColorValue 0x7f030189
int attr customDimension 0x7f03018a
int attr customFloatValue 0x7f03018b
int attr customIntegerValue 0x7f03018c
int attr customNavigationLayout 0x7f03018d
int attr customPixelDimension 0x7f03018e
int attr customReference 0x7f03018f
int attr customStringValue 0x7f030190
int attr customThemeStyle 0x7f030191
int attr data 0x7f030192
int attr dataPattern 0x7f030193
int attr dayInvalidStyle 0x7f030194
int attr daySelectedStyle 0x7f030195
int attr dayStyle 0x7f030196
int attr dayTodayStyle 0x7f030197
int attr defaultDuration 0x7f030198
int attr defaultMarginsEnabled 0x7f030199
int attr defaultQueryHint 0x7f03019a
int attr defaultScrollFlagsEnabled 0x7f03019b
int attr defaultState 0x7f03019c
int attr defaultValue 0x7f03019d
int attr deltaPolarAngle 0x7f03019e
int attr deltaPolarRadius 0x7f03019f
int attr dependency 0x7f0301a0
int attr deriveConstraintsFrom 0x7f0301a1
int attr destination 0x7f0301a2
int attr dialogCornerRadius 0x7f0301a3
int attr dialogIcon 0x7f0301a4
int attr dialogLayout 0x7f0301a5
int attr dialogMessage 0x7f0301a6
int attr dialogPreferenceStyle 0x7f0301a7
int attr dialogPreferredPadding 0x7f0301a8
int attr dialogTheme 0x7f0301a9
int attr dialogTitle 0x7f0301aa
int attr disableDependentsState 0x7f0301ab
int attr displayOptions 0x7f0301ac
int attr divider 0x7f0301ad
int attr dividerColor 0x7f0301ae
int attr dividerHorizontal 0x7f0301af
int attr dividerInsetEnd 0x7f0301b0
int attr dividerInsetStart 0x7f0301b1
int attr dividerPadding 0x7f0301b2
int attr dividerThickness 0x7f0301b3
int attr dividerVertical 0x7f0301b4
int attr dragDirection 0x7f0301b5
int attr dragScale 0x7f0301b6
int attr dragThreshold 0x7f0301b7
int attr drawPath 0x7f0301b8
int attr drawableBottomCompat 0x7f0301b9
int attr drawableEndCompat 0x7f0301ba
int attr drawableLeftCompat 0x7f0301bb
int attr drawableRightCompat 0x7f0301bc
int attr drawableSize 0x7f0301bd
int attr drawableStartCompat 0x7f0301be
int attr drawableTint 0x7f0301bf
int attr drawableTintMode 0x7f0301c0
int attr drawableTopCompat 0x7f0301c1
int attr drawerArrowStyle 0x7f0301c2
int attr drawerLayoutCornerSize 0x7f0301c3
int attr drawerLayoutStyle 0x7f0301c4
int attr dropDownBackgroundTint 0x7f0301c5
int attr dropDownListViewStyle 0x7f0301c6
int attr dropdownListPreferredItemHeight 0x7f0301c7
int attr dropdownPreferenceStyle 0x7f0301c8
int attr duration 0x7f0301c9
int attr dynamicColorThemeOverlay 0x7f0301ca
int attr editTextBackground 0x7f0301cb
int attr editTextColor 0x7f0301cc
int attr editTextPreferenceStyle 0x7f0301cd
int attr editTextStyle 0x7f0301ce
int attr elevation 0x7f0301cf
int attr elevationOverlayAccentColor 0x7f0301d0
int attr elevationOverlayColor 0x7f0301d1
int attr elevationOverlayEnabled 0x7f0301d2
int attr emojiCompatEnabled 0x7f0301d3
int attr enableCopying 0x7f0301d4
int attr enableEdgeToEdge 0x7f0301d5
int attr enabled 0x7f0301d6
int attr endIconCheckable 0x7f0301d7
int attr endIconContentDescription 0x7f0301d8
int attr endIconDrawable 0x7f0301d9
int attr endIconMinSize 0x7f0301da
int attr endIconMode 0x7f0301db
int attr endIconScaleType 0x7f0301dc
int attr endIconTint 0x7f0301dd
int attr endIconTintMode 0x7f0301de
int attr enforceMaterialTheme 0x7f0301df
int attr enforceTextAppearance 0x7f0301e0
int attr ensureMinTouchTargetSize 0x7f0301e1
int attr enterAnim 0x7f0301e2
int attr entries 0x7f0301e3
int attr entryValues 0x7f0301e4
int attr environment 0x7f0301e5
int attr errorAccessibilityLabel 0x7f0301e6
int attr errorAccessibilityLiveRegion 0x7f0301e7
int attr errorContentDescription 0x7f0301e8
int attr errorEnabled 0x7f0301e9
int attr errorIconDrawable 0x7f0301ea
int attr errorIconTint 0x7f0301eb
int attr errorIconTintMode 0x7f0301ec
int attr errorShown 0x7f0301ed
int attr errorTextAppearance 0x7f0301ee
int attr errorTextColor 0x7f0301ef
int attr exitAnim 0x7f0301f0
int attr expandActivityOverflowButtonDrawable 0x7f0301f1
int attr expanded 0x7f0301f2
int attr expandedHintEnabled 0x7f0301f3
int attr expandedTitleGravity 0x7f0301f4
int attr expandedTitleMargin 0x7f0301f5
int attr expandedTitleMarginBottom 0x7f0301f6
int attr expandedTitleMarginEnd 0x7f0301f7
int attr expandedTitleMarginStart 0x7f0301f8
int attr expandedTitleMarginTop 0x7f0301f9
int attr expandedTitleTextAppearance 0x7f0301fa
int attr expandedTitleTextColor 0x7f0301fb
int attr extendMotionSpec 0x7f0301fc
int attr extendStrategy 0x7f0301fd
int attr extendedFloatingActionButtonPrimaryStyle 0x7f0301fe
int attr extendedFloatingActionButtonSecondaryStyle 0x7f0301ff
int attr extendedFloatingActionButtonStyle 0x7f030200
int attr extendedFloatingActionButtonSurfaceStyle 0x7f030201
int attr extendedFloatingActionButtonTertiaryStyle 0x7f030202
int attr extraMultilineHeightEnabled 0x7f030203
int attr fabAlignmentMode 0x7f030204
int attr fabAlignmentModeEndMargin 0x7f030205
int attr fabAnchorMode 0x7f030206
int attr fabAnimationMode 0x7f030207
int attr fabCradleMargin 0x7f030208
int attr fabCradleRoundedCornerRadius 0x7f030209
int attr fabCradleVerticalOffset 0x7f03020a
int attr fabCustomSize 0x7f03020b
int attr fabSize 0x7f03020c
int attr fastScrollEnabled 0x7f03020d
int attr fastScrollHorizontalThumbDrawable 0x7f03020e
int attr fastScrollHorizontalTrackDrawable 0x7f03020f
int attr fastScrollVerticalThumbDrawable 0x7f030210
int attr fastScrollVerticalTrackDrawable 0x7f030211
int attr finishPrimaryWithPlaceholder 0x7f030212
int attr finishPrimaryWithSecondary 0x7f030213
int attr finishSecondaryWithPrimary 0x7f030214
int attr firstBaselineToTopHeight 0x7f030215
int attr floatingActionButtonLargePrimaryStyle 0x7f030216
int attr floatingActionButtonLargeSecondaryStyle 0x7f030217
int attr floatingActionButtonLargeStyle 0x7f030218
int attr floatingActionButtonLargeSurfaceStyle 0x7f030219
int attr floatingActionButtonLargeTertiaryStyle 0x7f03021a
int attr floatingActionButtonPrimaryStyle 0x7f03021b
int attr floatingActionButtonSecondaryStyle 0x7f03021c
int attr floatingActionButtonSmallPrimaryStyle 0x7f03021d
int attr floatingActionButtonSmallSecondaryStyle 0x7f03021e
int attr floatingActionButtonSmallStyle 0x7f03021f
int attr floatingActionButtonSmallSurfaceStyle 0x7f030220
int attr floatingActionButtonSmallTertiaryStyle 0x7f030221
int attr floatingActionButtonStyle 0x7f030222
int attr floatingActionButtonSurfaceStyle 0x7f030223
int attr floatingActionButtonTertiaryStyle 0x7f030224
int attr flow_firstHorizontalBias 0x7f030225
int attr flow_firstHorizontalStyle 0x7f030226
int attr flow_firstVerticalBias 0x7f030227
int attr flow_firstVerticalStyle 0x7f030228
int attr flow_horizontalAlign 0x7f030229
int attr flow_horizontalBias 0x7f03022a
int attr flow_horizontalGap 0x7f03022b
int attr flow_horizontalStyle 0x7f03022c
int attr flow_lastHorizontalBias 0x7f03022d
int attr flow_lastHorizontalStyle 0x7f03022e
int attr flow_lastVerticalBias 0x7f03022f
int attr flow_lastVerticalStyle 0x7f030230
int attr flow_maxElementsWrap 0x7f030231
int attr flow_padding 0x7f030232
int attr flow_verticalAlign 0x7f030233
int attr flow_verticalBias 0x7f030234
int attr flow_verticalGap 0x7f030235
int attr flow_verticalStyle 0x7f030236
int attr flow_wrapMode 0x7f030237
int attr font 0x7f030238
int attr fontFamily 0x7f030239
int attr fontProviderAuthority 0x7f03023a
int attr fontProviderCerts 0x7f03023b
int attr fontProviderFetchStrategy 0x7f03023c
int attr fontProviderFetchTimeout 0x7f03023d
int attr fontProviderPackage 0x7f03023e
int attr fontProviderQuery 0x7f03023f
int attr fontProviderSystemFontFamily 0x7f030240
int attr fontStyle 0x7f030241
int attr fontVariationSettings 0x7f030242
int attr fontWeight 0x7f030243
int attr forceApplySystemWindowInsetTop 0x7f030244
int attr forceDefaultNavigationOnClickListener 0x7f030245
int attr foregroundInsidePadding 0x7f030246
int attr fragment 0x7f030247
int attr fragmentMode 0x7f030248
int attr fragmentStyle 0x7f030249
int attr framePosition 0x7f03024a
int attr gapBetweenBars 0x7f03024b
int attr gestureInsetBottomIgnored 0x7f03024c
int attr goIcon 0x7f03024d
int attr graph 0x7f03024e
int attr grid_columnWeights 0x7f03024f
int attr grid_columns 0x7f030250
int attr grid_horizontalGaps 0x7f030251
int attr grid_orientation 0x7f030252
int attr grid_rowWeights 0x7f030253
int attr grid_rows 0x7f030254
int attr grid_skips 0x7f030255
int attr grid_spans 0x7f030256
int attr grid_useRtl 0x7f030257
int attr grid_validateInputs 0x7f030258
int attr grid_verticalGaps 0x7f030259
int attr guidelineUseRtl 0x7f03025a
int attr haloColor 0x7f03025b
int attr haloRadius 0x7f03025c
int attr headerLayout 0x7f03025d
int attr height 0x7f03025e
int attr helperText 0x7f03025f
int attr helperTextEnabled 0x7f030260
int attr helperTextTextAppearance 0x7f030261
int attr helperTextTextColor 0x7f030262
int attr hideAnimationBehavior 0x7f030263
int attr hideMotionSpec 0x7f030264
int attr hideNavigationIcon 0x7f030265
int attr hideOnContentScroll 0x7f030266
int attr hideOnScroll 0x7f030267
int attr hintAnimationEnabled 0x7f030268
int attr hintEnabled 0x7f030269
int attr hintTextAppearance 0x7f03026a
int attr hintTextColor 0x7f03026b
int attr homeAsUpIndicator 0x7f03026c
int attr homeLayout 0x7f03026d
int attr horizontalOffset 0x7f03026e
int attr horizontalOffsetWithText 0x7f03026f
int attr hoveredFocusedTranslationZ 0x7f030270
int attr icon 0x7f030271
int attr iconEndPadding 0x7f030272
int attr iconGravity 0x7f030273
int attr iconPadding 0x7f030274
int attr iconSize 0x7f030275
int attr iconSpaceReserved 0x7f030276
int attr iconStartPadding 0x7f030277
int attr iconTint 0x7f030278
int attr iconTintMode 0x7f030279
int attr iconifiedByDefault 0x7f03027a
int attr ifTagNotSet 0x7f03027b
int attr ifTagSet 0x7f03027c
int attr imageAspectRatio 0x7f03027d
int attr imageAspectRatioAdjust 0x7f03027e
int attr imageButtonStyle 0x7f03027f
int attr imagePanX 0x7f030280
int attr imagePanY 0x7f030281
int attr imageRotate 0x7f030282
int attr imageZoom 0x7f030283
int attr indeterminateAnimationType 0x7f030284
int attr indeterminateProgressStyle 0x7f030285
int attr indicatorColor 0x7f030286
int attr indicatorDirectionCircular 0x7f030287
int attr indicatorDirectionLinear 0x7f030288
int attr indicatorInset 0x7f030289
int attr indicatorSize 0x7f03028a
int attr indicatorTrackGapSize 0x7f03028b
int attr initialActivityCount 0x7f03028c
int attr initialExpandedChildrenCount 0x7f03028d
int attr insetForeground 0x7f03028e
int attr isLightTheme 0x7f03028f
int attr isMaterial3DynamicColorApplied 0x7f030290
int attr isMaterial3Theme 0x7f030291
int attr isMaterialTheme 0x7f030292
int attr isPreferenceVisible 0x7f030293
int attr itemActiveIndicatorStyle 0x7f030294
int attr itemBackground 0x7f030295
int attr itemFillColor 0x7f030296
int attr itemHorizontalPadding 0x7f030297
int attr itemHorizontalTranslationEnabled 0x7f030298
int attr itemIconPadding 0x7f030299
int attr itemIconSize 0x7f03029a
int attr itemIconTint 0x7f03029b
int attr itemMaxLines 0x7f03029c
int attr itemMinHeight 0x7f03029d
int attr itemPadding 0x7f03029e
int attr itemPaddingBottom 0x7f03029f
int attr itemPaddingTop 0x7f0302a0
int attr itemRippleColor 0x7f0302a1
int attr itemShapeAppearance 0x7f0302a2
int attr itemShapeAppearanceOverlay 0x7f0302a3
int attr itemShapeFillColor 0x7f0302a4
int attr itemShapeInsetBottom 0x7f0302a5
int attr itemShapeInsetEnd 0x7f0302a6
int attr itemShapeInsetStart 0x7f0302a7
int attr itemShapeInsetTop 0x7f0302a8
int attr itemSpacing 0x7f0302a9
int attr itemStrokeColor 0x7f0302aa
int attr itemStrokeWidth 0x7f0302ab
int attr itemTextAppearance 0x7f0302ac
int attr itemTextAppearanceActive 0x7f0302ad
int attr itemTextAppearanceActiveBoldEnabled 0x7f0302ae
int attr itemTextAppearanceInactive 0x7f0302af
int attr itemTextColor 0x7f0302b0
int attr itemVerticalPadding 0x7f0302b1
int attr key 0x7f0302b2
int attr keyPositionType 0x7f0302b3
int attr keyboardIcon 0x7f0302b4
int attr keylines 0x7f0302b5
int attr lStar 0x7f0302b6
int attr labelBehavior 0x7f0302b7
int attr labelStyle 0x7f0302b8
int attr labelVisibilityMode 0x7f0302b9
int attr largeFontVerticalOffsetAdjustment 0x7f0302ba
int attr lastBaselineToBottomHeight 0x7f0302bb
int attr lastItemDecorated 0x7f0302bc
int attr latLngBoundsNorthEastLatitude 0x7f0302bd
int attr latLngBoundsNorthEastLongitude 0x7f0302be
int attr latLngBoundsSouthWestLatitude 0x7f0302bf
int attr latLngBoundsSouthWestLongitude 0x7f0302c0
int attr launchSingleTop 0x7f0302c1
int attr layout 0x7f0302c2
int attr layoutDescription 0x7f0302c3
int attr layoutDuringTransition 0x7f0302c4
int attr layoutManager 0x7f0302c5
int attr layout_anchor 0x7f0302c6
int attr layout_anchorGravity 0x7f0302c7
int attr layout_behavior 0x7f0302c8
int attr layout_collapseMode 0x7f0302c9
int attr layout_collapseParallaxMultiplier 0x7f0302ca
int attr layout_constrainedHeight 0x7f0302cb
int attr layout_constrainedWidth 0x7f0302cc
int attr layout_constraintBaseline_creator 0x7f0302cd
int attr layout_constraintBaseline_toBaselineOf 0x7f0302ce
int attr layout_constraintBaseline_toBottomOf 0x7f0302cf
int attr layout_constraintBaseline_toTopOf 0x7f0302d0
int attr layout_constraintBottom_creator 0x7f0302d1
int attr layout_constraintBottom_toBottomOf 0x7f0302d2
int attr layout_constraintBottom_toTopOf 0x7f0302d3
int attr layout_constraintCircle 0x7f0302d4
int attr layout_constraintCircleAngle 0x7f0302d5
int attr layout_constraintCircleRadius 0x7f0302d6
int attr layout_constraintDimensionRatio 0x7f0302d7
int attr layout_constraintEnd_toEndOf 0x7f0302d8
int attr layout_constraintEnd_toStartOf 0x7f0302d9
int attr layout_constraintGuide_begin 0x7f0302da
int attr layout_constraintGuide_end 0x7f0302db
int attr layout_constraintGuide_percent 0x7f0302dc
int attr layout_constraintHeight 0x7f0302dd
int attr layout_constraintHeight_default 0x7f0302de
int attr layout_constraintHeight_max 0x7f0302df
int attr layout_constraintHeight_min 0x7f0302e0
int attr layout_constraintHeight_percent 0x7f0302e1
int attr layout_constraintHorizontal_bias 0x7f0302e2
int attr layout_constraintHorizontal_chainStyle 0x7f0302e3
int attr layout_constraintHorizontal_weight 0x7f0302e4
int attr layout_constraintLeft_creator 0x7f0302e5
int attr layout_constraintLeft_toLeftOf 0x7f0302e6
int attr layout_constraintLeft_toRightOf 0x7f0302e7
int attr layout_constraintRight_creator 0x7f0302e8
int attr layout_constraintRight_toLeftOf 0x7f0302e9
int attr layout_constraintRight_toRightOf 0x7f0302ea
int attr layout_constraintStart_toEndOf 0x7f0302eb
int attr layout_constraintStart_toStartOf 0x7f0302ec
int attr layout_constraintTag 0x7f0302ed
int attr layout_constraintTop_creator 0x7f0302ee
int attr layout_constraintTop_toBottomOf 0x7f0302ef
int attr layout_constraintTop_toTopOf 0x7f0302f0
int attr layout_constraintVertical_bias 0x7f0302f1
int attr layout_constraintVertical_chainStyle 0x7f0302f2
int attr layout_constraintVertical_weight 0x7f0302f3
int attr layout_constraintWidth 0x7f0302f4
int attr layout_constraintWidth_default 0x7f0302f5
int attr layout_constraintWidth_max 0x7f0302f6
int attr layout_constraintWidth_min 0x7f0302f7
int attr layout_constraintWidth_percent 0x7f0302f8
int attr layout_dodgeInsetEdges 0x7f0302f9
int attr layout_editor_absoluteX 0x7f0302fa
int attr layout_editor_absoluteY 0x7f0302fb
int attr layout_goneMarginBaseline 0x7f0302fc
int attr layout_goneMarginBottom 0x7f0302fd
int attr layout_goneMarginEnd 0x7f0302fe
int attr layout_goneMarginLeft 0x7f0302ff
int attr layout_goneMarginRight 0x7f030300
int attr layout_goneMarginStart 0x7f030301
int attr layout_goneMarginTop 0x7f030302
int attr layout_insetEdge 0x7f030303
int attr layout_keyline 0x7f030304
int attr layout_marginBaseline 0x7f030305
int attr layout_optimizationLevel 0x7f030306
int attr layout_scrollEffect 0x7f030307
int attr layout_scrollFlags 0x7f030308
int attr layout_scrollInterpolator 0x7f030309
int attr layout_wrapBehaviorInParent 0x7f03030a
int attr liftOnScroll 0x7f03030b
int attr liftOnScrollColor 0x7f03030c
int attr liftOnScrollTargetViewId 0x7f03030d
int attr limitBoundsTo 0x7f03030e
int attr lineHeight 0x7f03030f
int attr lineSpacing 0x7f030310
int attr linearProgressIndicatorStyle 0x7f030311
int attr listChoiceBackgroundIndicator 0x7f030312
int attr listChoiceIndicatorMultipleAnimated 0x7f030313
int attr listChoiceIndicatorSingleAnimated 0x7f030314
int attr listDividerAlertDialog 0x7f030315
int attr listItemLayout 0x7f030316
int attr listLayout 0x7f030317
int attr listMenuViewStyle 0x7f030318
int attr listPopupWindowStyle 0x7f030319
int attr listPreferredItemHeight 0x7f03031a
int attr listPreferredItemHeightLarge 0x7f03031b
int attr listPreferredItemHeightSmall 0x7f03031c
int attr listPreferredItemPaddingEnd 0x7f03031d
int attr listPreferredItemPaddingLeft 0x7f03031e
int attr listPreferredItemPaddingRight 0x7f03031f
int attr listPreferredItemPaddingStart 0x7f030320
int attr liteMode 0x7f030321
int attr logo 0x7f030322
int attr logoAdjustViewBounds 0x7f030323
int attr logoDescription 0x7f030324
int attr logoScaleType 0x7f030325
int attr mapId 0x7f030326
int attr mapType 0x7f030327
int attr marginHorizontal 0x7f030328
int attr marginLeftSystemWindowInsets 0x7f030329
int attr marginRightSystemWindowInsets 0x7f03032a
int attr marginTopSystemWindowInsets 0x7f03032b
int attr maskedWalletDetailsBackground 0x7f03032c
int attr maskedWalletDetailsButtonBackground 0x7f03032d
int attr maskedWalletDetailsButtonTextAppearance 0x7f03032e
int attr maskedWalletDetailsHeaderTextAppearance 0x7f03032f
int attr maskedWalletDetailsLogoImageType 0x7f030330
int attr maskedWalletDetailsLogoTextColor 0x7f030331
int attr maskedWalletDetailsTextAppearance 0x7f030332
int attr materialAlertDialogBodyTextStyle 0x7f030333
int attr materialAlertDialogButtonSpacerVisibility 0x7f030334
int attr materialAlertDialogTheme 0x7f030335
int attr materialAlertDialogTitleIconStyle 0x7f030336
int attr materialAlertDialogTitlePanelStyle 0x7f030337
int attr materialAlertDialogTitleTextStyle 0x7f030338
int attr materialButtonOutlinedStyle 0x7f030339
int attr materialButtonStyle 0x7f03033a
int attr materialButtonToggleGroupStyle 0x7f03033b
int attr materialCalendarDay 0x7f03033c
int attr materialCalendarDayOfWeekLabel 0x7f03033d
int attr materialCalendarFullscreenTheme 0x7f03033e
int attr materialCalendarHeaderCancelButton 0x7f03033f
int attr materialCalendarHeaderConfirmButton 0x7f030340
int attr materialCalendarHeaderDivider 0x7f030341
int attr materialCalendarHeaderLayout 0x7f030342
int attr materialCalendarHeaderSelection 0x7f030343
int attr materialCalendarHeaderTitle 0x7f030344
int attr materialCalendarHeaderToggleButton 0x7f030345
int attr materialCalendarMonth 0x7f030346
int attr materialCalendarMonthNavigationButton 0x7f030347
int attr materialCalendarStyle 0x7f030348
int attr materialCalendarTheme 0x7f030349
int attr materialCalendarYearNavigationButton 0x7f03034a
int attr materialCardViewElevatedStyle 0x7f03034b
int attr materialCardViewFilledStyle 0x7f03034c
int attr materialCardViewOutlinedStyle 0x7f03034d
int attr materialCardViewStyle 0x7f03034e
int attr materialCircleRadius 0x7f03034f
int attr materialClockStyle 0x7f030350
int attr materialDisplayDividerStyle 0x7f030351
int attr materialDividerHeavyStyle 0x7f030352
int attr materialDividerStyle 0x7f030353
int attr materialIconButtonFilledStyle 0x7f030354
int attr materialIconButtonFilledTonalStyle 0x7f030355
int attr materialIconButtonOutlinedStyle 0x7f030356
int attr materialIconButtonStyle 0x7f030357
int attr materialSearchBarStyle 0x7f030358
int attr materialSearchViewPrefixStyle 0x7f030359
int attr materialSearchViewStyle 0x7f03035a
int attr materialSearchViewToolbarHeight 0x7f03035b
int attr materialSearchViewToolbarStyle 0x7f03035c
int attr materialSwitchStyle 0x7f03035d
int attr materialThemeOverlay 0x7f03035e
int attr materialTimePickerStyle 0x7f03035f
int attr materialTimePickerTheme 0x7f030360
int attr materialTimePickerTitleStyle 0x7f030361
int attr maxAcceleration 0x7f030362
int attr maxActionInlineWidth 0x7f030363
int attr maxButtonHeight 0x7f030364
int attr maxCharacterCount 0x7f030365
int attr maxHeight 0x7f030366
int attr maxImageSize 0x7f030367
int attr maxLines 0x7f030368
int attr maxNumber 0x7f030369
int attr maxVelocity 0x7f03036a
int attr maxWidth 0x7f03036b
int attr measureWithLargestChild 0x7f03036c
int attr menu 0x7f03036d
int attr menuAlignmentMode 0x7f03036e
int attr menuGravity 0x7f03036f
int attr methodName 0x7f030370
int attr mimeType 0x7f030371
int attr min 0x7f030372
int attr minHeight 0x7f030373
int attr minHideDelay 0x7f030374
int attr minSeparation 0x7f030375
int attr minTouchTargetSize 0x7f030376
int attr minWidth 0x7f030377
int attr mock_diagonalsColor 0x7f030378
int attr mock_label 0x7f030379
int attr mock_labelBackgroundColor 0x7f03037a
int attr mock_labelColor 0x7f03037b
int attr mock_showDiagonals 0x7f03037c
int attr mock_showLabel 0x7f03037d
int attr motionDebug 0x7f03037e
int attr motionDurationExtraLong1 0x7f03037f
int attr motionDurationExtraLong2 0x7f030380
int attr motionDurationExtraLong3 0x7f030381
int attr motionDurationExtraLong4 0x7f030382
int attr motionDurationLong1 0x7f030383
int attr motionDurationLong2 0x7f030384
int attr motionDurationLong3 0x7f030385
int attr motionDurationLong4 0x7f030386
int attr motionDurationMedium1 0x7f030387
int attr motionDurationMedium2 0x7f030388
int attr motionDurationMedium3 0x7f030389
int attr motionDurationMedium4 0x7f03038a
int attr motionDurationShort1 0x7f03038b
int attr motionDurationShort2 0x7f03038c
int attr motionDurationShort3 0x7f03038d
int attr motionDurationShort4 0x7f03038e
int attr motionEasingAccelerated 0x7f03038f
int attr motionEasingDecelerated 0x7f030390
int attr motionEasingEmphasized 0x7f030391
int attr motionEasingEmphasizedAccelerateInterpolator 0x7f030392
int attr motionEasingEmphasizedDecelerateInterpolator 0x7f030393
int attr motionEasingEmphasizedInterpolator 0x7f030394
int attr motionEasingLinear 0x7f030395
int attr motionEasingLinearInterpolator 0x7f030396
int attr motionEasingStandard 0x7f030397
int attr motionEasingStandardAccelerateInterpolator 0x7f030398
int attr motionEasingStandardDecelerateInterpolator 0x7f030399
int attr motionEasingStandardInterpolator 0x7f03039a
int attr motionEffect_alpha 0x7f03039b
int attr motionEffect_end 0x7f03039c
int attr motionEffect_move 0x7f03039d
int attr motionEffect_start 0x7f03039e
int attr motionEffect_strict 0x7f03039f
int attr motionEffect_translationX 0x7f0303a0
int attr motionEffect_translationY 0x7f0303a1
int attr motionEffect_viewTransition 0x7f0303a2
int attr motionInterpolator 0x7f0303a3
int attr motionPath 0x7f0303a4
int attr motionPathRotate 0x7f0303a5
int attr motionProgress 0x7f0303a6
int attr motionStagger 0x7f0303a7
int attr motionTarget 0x7f0303a8
int attr motion_postLayoutCollision 0x7f0303a9
int attr motion_triggerOnCollision 0x7f0303aa
int attr moveWhenScrollAtTop 0x7f0303ab
int attr multiChoiceItemLayout 0x7f0303ac
int attr navGraph 0x7f0303ad
int attr navigationContentDescription 0x7f0303ae
int attr navigationIcon 0x7f0303af
int attr navigationIconTint 0x7f0303b0
int attr navigationMode 0x7f0303b1
int attr navigationRailStyle 0x7f0303b2
int attr navigationViewStyle 0x7f0303b3
int attr negativeButtonText 0x7f0303b4
int attr nestedScrollFlags 0x7f0303b5
int attr nestedScrollViewStyle 0x7f0303b6
int attr nestedScrollable 0x7f0303b7
int attr nullable 0x7f0303b8
int attr number 0x7f0303b9
int attr numericModifiers 0x7f0303ba
int attr offsetAlignmentMode 0x7f0303bb
int attr onCross 0x7f0303bc
int attr onHide 0x7f0303bd
int attr onNegativeCross 0x7f0303be
int attr onPositiveCross 0x7f0303bf
int attr onShow 0x7f0303c0
int attr onStateTransition 0x7f0303c1
int attr onTouchUp 0x7f0303c2
int attr order 0x7f0303c3
int attr orderingFromXml 0x7f0303c4
int attr overlapAnchor 0x7f0303c5
int attr overlay 0x7f0303c6
int attr paddingBottomNoButtons 0x7f0303c7
int attr paddingBottomSystemWindowInsets 0x7f0303c8
int attr paddingEnd 0x7f0303c9
int attr paddingLeftSystemWindowInsets 0x7f0303ca
int attr paddingRightSystemWindowInsets 0x7f0303cb
int attr paddingStart 0x7f0303cc
int attr paddingStartSystemWindowInsets 0x7f0303cd
int attr paddingTopNoTitle 0x7f0303ce
int attr paddingTopSystemWindowInsets 0x7f0303cf
int attr panelBackground 0x7f0303d0
int attr panelMenuListTheme 0x7f0303d1
int attr panelMenuListWidth 0x7f0303d2
int attr passwordToggleContentDescription 0x7f0303d3
int attr passwordToggleDrawable 0x7f0303d4
int attr passwordToggleEnabled 0x7f0303d5
int attr passwordToggleTint 0x7f0303d6
int attr passwordToggleTintMode 0x7f0303d7
int attr pathMotionArc 0x7f0303d8
int attr path_percent 0x7f0303d9
int attr payButtonGenericBackground 0x7f0303da
int attr payButtonGenericLogoImage 0x7f0303db
int attr payButtonGenericRippleColor 0x7f0303dc
int attr payButtonGenericRippleMask 0x7f0303dd
int attr percentHeight 0x7f0303de
int attr percentWidth 0x7f0303df
int attr percentX 0x7f0303e0
int attr percentY 0x7f0303e1
int attr perpendicularPath_percent 0x7f0303e2
int attr persistent 0x7f0303e3
int attr pivotAnchor 0x7f0303e4
int attr placeholderActivityName 0x7f0303e5
int attr placeholderText 0x7f0303e6
int attr placeholderTextAppearance 0x7f0303e7
int attr placeholderTextColor 0x7f0303e8
int attr placeholder_emptyVisibility 0x7f0303e9
int attr polarRelativeTo 0x7f0303ea
int attr popEnterAnim 0x7f0303eb
int attr popExitAnim 0x7f0303ec
int attr popUpTo 0x7f0303ed
int attr popUpToInclusive 0x7f0303ee
int attr popUpToSaveState 0x7f0303ef
int attr popupMenuBackground 0x7f0303f0
int attr popupMenuStyle 0x7f0303f1
int attr popupTheme 0x7f0303f2
int attr popupWindowStyle 0x7f0303f3
int attr positiveButtonText 0x7f0303f4
int attr preferenceCategoryStyle 0x7f0303f5
int attr preferenceCategoryTitleTextAppearance 0x7f0303f6
int attr preferenceCategoryTitleTextColor 0x7f0303f7
int attr preferenceFragmentCompatStyle 0x7f0303f8
int attr preferenceFragmentListStyle 0x7f0303f9
int attr preferenceFragmentStyle 0x7f0303fa
int attr preferenceInformationStyle 0x7f0303fb
int attr preferenceScreenStyle 0x7f0303fc
int attr preferenceStyle 0x7f0303fd
int attr preferenceTheme 0x7f0303fe
int attr prefixText 0x7f0303ff
int attr prefixTextAppearance 0x7f030400
int attr prefixTextColor 0x7f030401
int attr preserveIconSpacing 0x7f030402
int attr pressedTranslationZ 0x7f030403
int attr primaryActivityName 0x7f030404
int attr progressBarPadding 0x7f030405
int attr progressBarStyle 0x7f030406
int attr quantizeMotionInterpolator 0x7f030407
int attr quantizeMotionPhase 0x7f030408
int attr quantizeMotionSteps 0x7f030409
int attr queryBackground 0x7f03040a
int attr queryHint 0x7f03040b
int attr queryPatterns 0x7f03040c
int attr radioButtonStyle 0x7f03040d
int attr rangeFillColor 0x7f03040e
int attr ratingBarStyle 0x7f03040f
int attr ratingBarStyleIndicator 0x7f030410
int attr ratingBarStyleSmall 0x7f030411
int attr reactiveGuide_animateChange 0x7f030412
int attr reactiveGuide_applyToAllConstraintSets 0x7f030413
int attr reactiveGuide_applyToConstraintSet 0x7f030414
int attr reactiveGuide_valueId 0x7f030415
int attr recyclerViewStyle 0x7f030416
int attr region_heightLessThan 0x7f030417
int attr region_heightMoreThan 0x7f030418
int attr region_widthLessThan 0x7f030419
int attr region_widthMoreThan 0x7f03041a
int attr removeEmbeddedFabElevation 0x7f03041b
int attr restoreState 0x7f03041c
int attr reverseLayout 0x7f03041d
int attr rippleColor 0x7f03041e
int attr rotationCenterId 0x7f03041f
int attr round 0x7f030420
int attr roundPercent 0x7f030421
int attr route 0x7f030422
int attr saturation 0x7f030423
int attr scaleFromTextSize 0x7f030424
int attr scopeUris 0x7f030425
int attr scrimAnimationDuration 0x7f030426
int attr scrimBackground 0x7f030427
int attr scrimVisibleHeightTrigger 0x7f030428
int attr searchHintIcon 0x7f030429
int attr searchIcon 0x7f03042a
int attr searchPrefixText 0x7f03042b
int attr searchViewStyle 0x7f03042c
int attr secondaryActivityAction 0x7f03042d
int attr secondaryActivityName 0x7f03042e
int attr seekBarIncrement 0x7f03042f
int attr seekBarPreferenceStyle 0x7f030430
int attr seekBarStyle 0x7f030431
int attr selectable 0x7f030432
int attr selectableItemBackground 0x7f030433
int attr selectableItemBackgroundBorderless 0x7f030434
int attr selectionRequired 0x7f030435
int attr selectorSize 0x7f030436
int attr setsTag 0x7f030437
int attr shapeAppearance 0x7f030438
int attr shapeAppearanceCornerExtraLarge 0x7f030439
int attr shapeAppearanceCornerExtraSmall 0x7f03043a
int attr shapeAppearanceCornerLarge 0x7f03043b
int attr shapeAppearanceCornerMedium 0x7f03043c
int attr shapeAppearanceCornerSmall 0x7f03043d
int attr shapeAppearanceLargeComponent 0x7f03043e
int attr shapeAppearanceMediumComponent 0x7f03043f
int attr shapeAppearanceOverlay 0x7f030440
int attr shapeAppearanceSmallComponent 0x7f030441
int attr shapeCornerFamily 0x7f030442
int attr shortcutMatchRequired 0x7f030443
int attr shouldDisableView 0x7f030444
int attr shouldRemoveExpandedCorners 0x7f030445
int attr shouldRequirePostalCode 0x7f030446
int attr shouldRequireUsZipCode 0x7f030447
int attr shouldShowPostalCode 0x7f030448
int attr showAnimationBehavior 0x7f030449
int attr showAsAction 0x7f03044a
int attr showDelay 0x7f03044b
int attr showDividers 0x7f03044c
int attr showMarker 0x7f03044d
int attr showMotionSpec 0x7f03044e
int attr showPaths 0x7f03044f
int attr showSeekBarValue 0x7f030450
int attr showText 0x7f030451
int attr showTitle 0x7f030452
int attr shrinkMotionSpec 0x7f030453
int attr sideSheetDialogTheme 0x7f030454
int attr sideSheetModalStyle 0x7f030455
int attr simpleItemLayout 0x7f030456
int attr simpleItemSelectedColor 0x7f030457
int attr simpleItemSelectedRippleColor 0x7f030458
int attr simpleItems 0x7f030459
int attr singleChoiceItemLayout 0x7f03045a
int attr singleLine 0x7f03045b
int attr singleLineTitle 0x7f03045c
int attr singleSelection 0x7f03045d
int attr sizePercent 0x7f03045e
int attr sliderStyle 0x7f03045f
int attr snackbarButtonStyle 0x7f030460
int attr snackbarStyle 0x7f030461
int attr snackbarTextViewStyle 0x7f030462
int attr spanCount 0x7f030463
int attr spinBars 0x7f030464
int attr spinnerDropDownItemStyle 0x7f030465
int attr spinnerStyle 0x7f030466
int attr splitLayoutDirection 0x7f030467
int attr splitMaxAspectRatioInLandscape 0x7f030468
int attr splitMaxAspectRatioInPortrait 0x7f030469
int attr splitMinHeightDp 0x7f03046a
int attr splitMinSmallestWidthDp 0x7f03046b
int attr splitMinWidthDp 0x7f03046c
int attr splitRatio 0x7f03046d
int attr splitTrack 0x7f03046e
int attr springBoundary 0x7f03046f
int attr springDamping 0x7f030470
int attr springMass 0x7f030471
int attr springStiffness 0x7f030472
int attr springStopThreshold 0x7f030473
int attr srcCompat 0x7f030474
int attr stackFromEnd 0x7f030475
int attr staggered 0x7f030476
int attr startDestination 0x7f030477
int attr startIconCheckable 0x7f030478
int attr startIconContentDescription 0x7f030479
int attr startIconDrawable 0x7f03047a
int attr startIconMinSize 0x7f03047b
int attr startIconScaleType 0x7f03047c
int attr startIconTint 0x7f03047d
int attr startIconTintMode 0x7f03047e
int attr stateLabels 0x7f03047f
int attr state_above_anchor 0x7f030480
int attr state_collapsed 0x7f030481
int attr state_collapsible 0x7f030482
int attr state_dragged 0x7f030483
int attr state_error 0x7f030484
int attr state_indeterminate 0x7f030485
int attr state_liftable 0x7f030486
int attr state_lifted 0x7f030487
int attr state_with_icon 0x7f030488
int attr statusBarBackground 0x7f030489
int attr statusBarForeground 0x7f03048a
int attr statusBarScrim 0x7f03048b
int attr stickyPlaceholder 0x7f03048c
int attr stripe_ic_paymentsheet_card_amex_ref 0x7f03048d
int attr stripe_ic_paymentsheet_card_cartes_bancaires_ref 0x7f03048e
int attr stripe_ic_paymentsheet_card_dinersclub_ref 0x7f03048f
int attr stripe_ic_paymentsheet_card_discover_ref 0x7f030490
int attr stripe_ic_paymentsheet_card_jcb_ref 0x7f030491
int attr stripe_ic_paymentsheet_card_mastercard_ref 0x7f030492
int attr stripe_ic_paymentsheet_card_unionpay_ref 0x7f030493
int attr stripe_ic_paymentsheet_card_unknown_ref 0x7f030494
int attr stripe_ic_paymentsheet_card_visa_ref 0x7f030495
int attr stripe_ic_paymentsheet_link_ref 0x7f030496
int attr stripe_ic_paymentsheet_sepa_ref 0x7f030497
int attr strokeColor 0x7f030498
int attr strokeWidth 0x7f030499
int attr subMenuArrow 0x7f03049a
int attr subheaderColor 0x7f03049b
int attr subheaderInsetEnd 0x7f03049c
int attr subheaderInsetStart 0x7f03049d
int attr subheaderTextAppearance 0x7f03049e
int attr submitBackground 0x7f03049f
int attr subtitle 0x7f0304a0
int attr subtitleCentered 0x7f0304a1
int attr subtitleTextAppearance 0x7f0304a2
int attr subtitleTextColor 0x7f0304a3
int attr subtitleTextStyle 0x7f0304a4
int attr suffixText 0x7f0304a5
int attr suffixTextAppearance 0x7f0304a6
int attr suffixTextColor 0x7f0304a7
int attr suggestionRowLayout 0x7f0304a8
int attr summary 0x7f0304a9
int attr summaryOff 0x7f0304aa
int attr summaryOn 0x7f0304ab
int attr swipeRefreshLayoutProgressSpinnerBackgroundColor 0x7f0304ac
int attr switchMinWidth 0x7f0304ad
int attr switchPadding 0x7f0304ae
int attr switchPreferenceCompatStyle 0x7f0304af
int attr switchPreferenceStyle 0x7f0304b0
int attr switchStyle 0x7f0304b1
int attr switchTextAppearance 0x7f0304b2
int attr switchTextOff 0x7f0304b3
int attr switchTextOn 0x7f0304b4
int attr tabBackground 0x7f0304b5
int attr tabContentStart 0x7f0304b6
int attr tabGravity 0x7f0304b7
int attr tabIconTint 0x7f0304b8
int attr tabIconTintMode 0x7f0304b9
int attr tabIndicator 0x7f0304ba
int attr tabIndicatorAnimationDuration 0x7f0304bb
int attr tabIndicatorAnimationMode 0x7f0304bc
int attr tabIndicatorColor 0x7f0304bd
int attr tabIndicatorFullWidth 0x7f0304be
int attr tabIndicatorGravity 0x7f0304bf
int attr tabIndicatorHeight 0x7f0304c0
int attr tabInlineLabel 0x7f0304c1
int attr tabMaxWidth 0x7f0304c2
int attr tabMinWidth 0x7f0304c3
int attr tabMode 0x7f0304c4
int attr tabPadding 0x7f0304c5
int attr tabPaddingBottom 0x7f0304c6
int attr tabPaddingEnd 0x7f0304c7
int attr tabPaddingStart 0x7f0304c8
int attr tabPaddingTop 0x7f0304c9
int attr tabRippleColor 0x7f0304ca
int attr tabSecondaryStyle 0x7f0304cb
int attr tabSelectedTextAppearance 0x7f0304cc
int attr tabSelectedTextColor 0x7f0304cd
int attr tabStyle 0x7f0304ce
int attr tabTextAppearance 0x7f0304cf
int attr tabTextColor 0x7f0304d0
int attr tabUnboundedRipple 0x7f0304d1
int attr tag 0x7f0304d2
int attr targetId 0x7f0304d3
int attr targetPackage 0x7f0304d4
int attr telltales_tailColor 0x7f0304d5
int attr telltales_tailScale 0x7f0304d6
int attr telltales_velocityMode 0x7f0304d7
int attr textAllCaps 0x7f0304d8
int attr textAppearanceBody1 0x7f0304d9
int attr textAppearanceBody2 0x7f0304da
int attr textAppearanceBodyLarge 0x7f0304db
int attr textAppearanceBodyMedium 0x7f0304dc
int attr textAppearanceBodySmall 0x7f0304dd
int attr textAppearanceButton 0x7f0304de
int attr textAppearanceCaption 0x7f0304df
int attr textAppearanceDisplayLarge 0x7f0304e0
int attr textAppearanceDisplayMedium 0x7f0304e1
int attr textAppearanceDisplaySmall 0x7f0304e2
int attr textAppearanceHeadline1 0x7f0304e3
int attr textAppearanceHeadline2 0x7f0304e4
int attr textAppearanceHeadline3 0x7f0304e5
int attr textAppearanceHeadline4 0x7f0304e6
int attr textAppearanceHeadline5 0x7f0304e7
int attr textAppearanceHeadline6 0x7f0304e8
int attr textAppearanceHeadlineLarge 0x7f0304e9
int attr textAppearanceHeadlineMedium 0x7f0304ea
int attr textAppearanceHeadlineSmall 0x7f0304eb
int attr textAppearanceLabelLarge 0x7f0304ec
int attr textAppearanceLabelMedium 0x7f0304ed
int attr textAppearanceLabelSmall 0x7f0304ee
int attr textAppearanceLargePopupMenu 0x7f0304ef
int attr textAppearanceLineHeightEnabled 0x7f0304f0
int attr textAppearanceListItem 0x7f0304f1
int attr textAppearanceListItemSecondary 0x7f0304f2
int attr textAppearanceListItemSmall 0x7f0304f3
int attr textAppearanceOverline 0x7f0304f4
int attr textAppearancePopupMenuHeader 0x7f0304f5
int attr textAppearanceSearchResultSubtitle 0x7f0304f6
int attr textAppearanceSearchResultTitle 0x7f0304f7
int attr textAppearanceSmallPopupMenu 0x7f0304f8
int attr textAppearanceSubtitle1 0x7f0304f9
int attr textAppearanceSubtitle2 0x7f0304fa
int attr textAppearanceTitleLarge 0x7f0304fb
int attr textAppearanceTitleMedium 0x7f0304fc
int attr textAppearanceTitleSmall 0x7f0304fd
int attr textBackground 0x7f0304fe
int attr textBackgroundPanX 0x7f0304ff
int attr textBackgroundPanY 0x7f030500
int attr textBackgroundRotate 0x7f030501
int attr textBackgroundZoom 0x7f030502
int attr textColorAlertDialogListItem 0x7f030503
int attr textColorSearchUrl 0x7f030504
int attr textEndPadding 0x7f030505
int attr textFillColor 0x7f030506
int attr textInputFilledDenseStyle 0x7f030507
int attr textInputFilledExposedDropdownMenuStyle 0x7f030508
int attr textInputFilledStyle 0x7f030509
int attr textInputLayoutFocusedRectEnabled 0x7f03050a
int attr textInputOutlinedDenseStyle 0x7f03050b
int attr textInputOutlinedExposedDropdownMenuStyle 0x7f03050c
int attr textInputOutlinedStyle 0x7f03050d
int attr textInputStyle 0x7f03050e
int attr textLocale 0x7f03050f
int attr textOutlineColor 0x7f030510
int attr textOutlineThickness 0x7f030511
int attr textPanX 0x7f030512
int attr textPanY 0x7f030513
int attr textStartPadding 0x7f030514
int attr textureBlurFactor 0x7f030515
int attr textureEffect 0x7f030516
int attr textureHeight 0x7f030517
int attr textureWidth 0x7f030518
int attr theme 0x7f030519
int attr thickness 0x7f03051a
int attr thumbColor 0x7f03051b
int attr thumbElevation 0x7f03051c
int attr thumbHeight 0x7f03051d
int attr thumbIcon 0x7f03051e
int attr thumbIconSize 0x7f03051f
int attr thumbIconTint 0x7f030520
int attr thumbIconTintMode 0x7f030521
int attr thumbRadius 0x7f030522
int attr thumbStrokeColor 0x7f030523
int attr thumbStrokeWidth 0x7f030524
int attr thumbTextPadding 0x7f030525
int attr thumbTint 0x7f030526
int attr thumbTintMode 0x7f030527
int attr thumbTrackGapSize 0x7f030528
int attr thumbWidth 0x7f030529
int attr tickColor 0x7f03052a
int attr tickColorActive 0x7f03052b
int attr tickColorInactive 0x7f03052c
int attr tickMark 0x7f03052d
int attr tickMarkTint 0x7f03052e
int attr tickMarkTintMode 0x7f03052f
int attr tickRadiusActive 0x7f030530
int attr tickRadiusInactive 0x7f030531
int attr tickVisible 0x7f030532
int attr tint 0x7f030533
int attr tintMode 0x7f030534
int attr tintNavigationIcon 0x7f030535
int attr title 0x7f030536
int attr titleCentered 0x7f030537
int attr titleCollapseMode 0x7f030538
int attr titleEnabled 0x7f030539
int attr titleMargin 0x7f03053a
int attr titleMarginBottom 0x7f03053b
int attr titleMarginEnd 0x7f03053c
int attr titleMarginStart 0x7f03053d
int attr titleMarginTop 0x7f03053e
int attr titleMargins 0x7f03053f
int attr titlePositionInterpolator 0x7f030540
int attr titleTextAppearance 0x7f030541
int attr titleTextColor 0x7f030542
int attr titleTextEllipsize 0x7f030543
int attr titleTextStyle 0x7f030544
int attr toggleCheckedStateOnClick 0x7f030545
int attr toolbarId 0x7f030546
int attr toolbarNavigationButtonStyle 0x7f030547
int attr toolbarStyle 0x7f030548
int attr toolbarSurfaceStyle 0x7f030549
int attr toolbarTextColorStyle 0x7f03054a
int attr tooltipForegroundColor 0x7f03054b
int attr tooltipFrameBackground 0x7f03054c
int attr tooltipStyle 0x7f03054d
int attr tooltipText 0x7f03054e
int attr topInsetScrimEnabled 0x7f03054f
int attr touchAnchorId 0x7f030550
int attr touchAnchorSide 0x7f030551
int attr touchRegionId 0x7f030552
int attr track 0x7f030553
int attr trackColor 0x7f030554
int attr trackColorActive 0x7f030555
int attr trackColorInactive 0x7f030556
int attr trackCornerRadius 0x7f030557
int attr trackDecoration 0x7f030558
int attr trackDecorationTint 0x7f030559
int attr trackDecorationTintMode 0x7f03055a
int attr trackHeight 0x7f03055b
int attr trackInsideCornerSize 0x7f03055c
int attr trackStopIndicatorSize 0x7f03055d
int attr trackThickness 0x7f03055e
int attr trackTint 0x7f03055f
int attr trackTintMode 0x7f030560
int attr transformPivotTarget 0x7f030561
int attr transitionDisable 0x7f030562
int attr transitionEasing 0x7f030563
int attr transitionFlags 0x7f030564
int attr transitionPathRotate 0x7f030565
int attr transitionShapeAppearance 0x7f030566
int attr triggerId 0x7f030567
int attr triggerReceiver 0x7f030568
int attr triggerSlack 0x7f030569
int attr ttcIndex 0x7f03056a
int attr uiCompass 0x7f03056b
int attr uiMapToolbar 0x7f03056c
int attr uiRotateGestures 0x7f03056d
int attr uiScrollGestures 0x7f03056e
int attr uiScrollGesturesDuringRotateOrZoom 0x7f03056f
int attr uiTiltGestures 0x7f030570
int attr uiZoomControls 0x7f030571
int attr uiZoomGestures 0x7f030572
int attr upDuration 0x7f030573
int attr updatesContinuously 0x7f030574
int attr uri 0x7f030575
int attr useCompatPadding 0x7f030576
int attr useDrawerArrowDrawable 0x7f030577
int attr useMaterialThemeColors 0x7f030578
int attr useSimpleSummaryProvider 0x7f030579
int attr useViewLifecycle 0x7f03057a
int attr values 0x7f03057b
int attr verticalOffset 0x7f03057c
int attr verticalOffsetWithText 0x7f03057d
int attr viewInflaterClass 0x7f03057e
int attr viewTransitionMode 0x7f03057f
int attr viewTransitionOnCross 0x7f030580
int attr viewTransitionOnNegativeCross 0x7f030581
int attr viewTransitionOnPositiveCross 0x7f030582
int attr visibilityMode 0x7f030583
int attr voiceIcon 0x7f030584
int attr warmth 0x7f030585
int attr waveDecay 0x7f030586
int attr waveOffset 0x7f030587
int attr wavePeriod 0x7f030588
int attr wavePhase 0x7f030589
int attr waveShape 0x7f03058a
int attr waveVariesBy 0x7f03058b
int attr widgetLayout 0x7f03058c
int attr windowActionBar 0x7f03058d
int attr windowActionBarOverlay 0x7f03058e
int attr windowActionModeOverlay 0x7f03058f
int attr windowFixedHeightMajor 0x7f030590
int attr windowFixedHeightMinor 0x7f030591
int attr windowFixedWidthMajor 0x7f030592
int attr windowFixedWidthMinor 0x7f030593
int attr windowMinWidthMajor 0x7f030594
int attr windowMinWidthMinor 0x7f030595
int attr windowNoTitle 0x7f030596
int attr windowTransitionStyle 0x7f030597
int attr yearSelectedStyle 0x7f030598
int attr yearStyle 0x7f030599
int attr yearTodayStyle 0x7f03059a
int attr zOrderOnTop 0x7f03059b
int bool abc_action_bar_embed_tabs 0x7f040000
int bool abc_config_actionMenuItemAllCaps 0x7f040001
int bool config_materialPreferenceIconSpaceReserved 0x7f040002
int bool isTablet 0x7f040003
int bool mtrl_btn_textappearance_all_caps 0x7f040004
int color abc_background_cache_hint_selector_material_dark 0x7f050000
int color abc_background_cache_hint_selector_material_light 0x7f050001
int color abc_btn_colored_borderless_text_material 0x7f050002
int color abc_btn_colored_text_material 0x7f050003
int color abc_color_highlight_material 0x7f050004
int color abc_decor_view_status_guard 0x7f050005
int color abc_decor_view_status_guard_light 0x7f050006
int color abc_hint_foreground_material_dark 0x7f050007
int color abc_hint_foreground_material_light 0x7f050008
int color abc_primary_text_disable_only_material_dark 0x7f050009
int color abc_primary_text_disable_only_material_light 0x7f05000a
int color abc_primary_text_material_dark 0x7f05000b
int color abc_primary_text_material_light 0x7f05000c
int color abc_search_url_text 0x7f05000d
int color abc_search_url_text_normal 0x7f05000e
int color abc_search_url_text_pressed 0x7f05000f
int color abc_search_url_text_selected 0x7f050010
int color abc_secondary_text_material_dark 0x7f050011
int color abc_secondary_text_material_light 0x7f050012
int color abc_tint_btn_checkable 0x7f050013
int color abc_tint_default 0x7f050014
int color abc_tint_edittext 0x7f050015
int color abc_tint_seek_thumb 0x7f050016
int color abc_tint_spinner 0x7f050017
int color abc_tint_switch_track 0x7f050018
int color accent_material_dark 0x7f050019
int color accent_material_light 0x7f05001a
int color androidx_core_ripple_material_light 0x7f05001b
int color androidx_core_secondary_text_default_material_light 0x7f05001c
int color background_floating_material_dark 0x7f05001d
int color background_floating_material_light 0x7f05001e
int color background_material_dark 0x7f05001f
int color background_material_light 0x7f050020
int color bright_foreground_disabled_material_dark 0x7f050021
int color bright_foreground_disabled_material_light 0x7f050022
int color bright_foreground_inverse_material_dark 0x7f050023
int color bright_foreground_inverse_material_light 0x7f050024
int color bright_foreground_material_dark 0x7f050025
int color bright_foreground_material_light 0x7f050026
int color browser_actions_bg_grey 0x7f050027
int color browser_actions_divider_color 0x7f050028
int color browser_actions_text_color 0x7f050029
int color browser_actions_title_color 0x7f05002a
int color button_material_dark 0x7f05002b
int color button_material_light 0x7f05002c
int color call_notification_answer_color 0x7f05002d
int color call_notification_decline_color 0x7f05002e
int color cardview_dark_background 0x7f05002f
int color cardview_light_background 0x7f050030
int color cardview_shadow_end_color 0x7f050031
int color cardview_shadow_start_color 0x7f050032
int color common_google_signin_btn_text_dark 0x7f050033
int color common_google_signin_btn_text_dark_default 0x7f050034
int color common_google_signin_btn_text_dark_disabled 0x7f050035
int color common_google_signin_btn_text_dark_focused 0x7f050036
int color common_google_signin_btn_text_dark_pressed 0x7f050037
int color common_google_signin_btn_text_light 0x7f050038
int color common_google_signin_btn_text_light_default 0x7f050039
int color common_google_signin_btn_text_light_disabled 0x7f05003a
int color common_google_signin_btn_text_light_focused 0x7f05003b
int color common_google_signin_btn_text_light_pressed 0x7f05003c
int color common_google_signin_btn_tint 0x7f05003d
int color design_bottom_navigation_shadow_color 0x7f05003e
int color design_box_stroke_color 0x7f05003f
int color design_dark_default_color_background 0x7f050040
int color design_dark_default_color_error 0x7f050041
int color design_dark_default_color_on_background 0x7f050042
int color design_dark_default_color_on_error 0x7f050043
int color design_dark_default_color_on_primary 0x7f050044
int color design_dark_default_color_on_secondary 0x7f050045
int color design_dark_default_color_on_surface 0x7f050046
int color design_dark_default_color_primary 0x7f050047
int color design_dark_default_color_primary_dark 0x7f050048
int color design_dark_default_color_primary_variant 0x7f050049
int color design_dark_default_color_secondary 0x7f05004a
int color design_dark_default_color_secondary_variant 0x7f05004b
int color design_dark_default_color_surface 0x7f05004c
int color design_default_color_background 0x7f05004d
int color design_default_color_error 0x7f05004e
int color design_default_color_on_background 0x7f05004f
int color design_default_color_on_error 0x7f050050
int color design_default_color_on_primary 0x7f050051
int color design_default_color_on_secondary 0x7f050052
int color design_default_color_on_surface 0x7f050053
int color design_default_color_primary 0x7f050054
int color design_default_color_primary_dark 0x7f050055
int color design_default_color_primary_variant 0x7f050056
int color design_default_color_secondary 0x7f050057
int color design_default_color_secondary_variant 0x7f050058
int color design_default_color_surface 0x7f050059
int color design_error 0x7f05005a
int color design_fab_shadow_end_color 0x7f05005b
int color design_fab_shadow_mid_color 0x7f05005c
int color design_fab_shadow_start_color 0x7f05005d
int color design_fab_stroke_end_inner_color 0x7f05005e
int color design_fab_stroke_end_outer_color 0x7f05005f
int color design_fab_stroke_top_inner_color 0x7f050060
int color design_fab_stroke_top_outer_color 0x7f050061
int color design_icon_tint 0x7f050062
int color design_snackbar_background_color 0x7f050063
int color dim_foreground_disabled_material_dark 0x7f050064
int color dim_foreground_disabled_material_light 0x7f050065
int color dim_foreground_material_dark 0x7f050066
int color dim_foreground_material_light 0x7f050067
int color error_color_material_dark 0x7f050068
int color error_color_material_light 0x7f050069
int color foreground_material_dark 0x7f05006a
int color foreground_material_light 0x7f05006b
int color highlighted_text_material_dark 0x7f05006c
int color highlighted_text_material_light 0x7f05006d
int color m3_appbar_overlay_color 0x7f05006e
int color m3_assist_chip_icon_tint_color 0x7f05006f
int color m3_assist_chip_stroke_color 0x7f050070
int color m3_bottom_sheet_drag_handle_color 0x7f050071
int color m3_button_background_color_selector 0x7f050072
int color m3_button_foreground_color_selector 0x7f050073
int color m3_button_outline_color_selector 0x7f050074
int color m3_button_ripple_color 0x7f050075
int color m3_button_ripple_color_selector 0x7f050076
int color m3_calendar_item_disabled_text 0x7f050077
int color m3_calendar_item_stroke_color 0x7f050078
int color m3_card_foreground_color 0x7f050079
int color m3_card_ripple_color 0x7f05007a
int color m3_card_stroke_color 0x7f05007b
int color m3_checkbox_button_icon_tint 0x7f05007c
int color m3_checkbox_button_tint 0x7f05007d
int color m3_chip_assist_text_color 0x7f05007e
int color m3_chip_background_color 0x7f05007f
int color m3_chip_ripple_color 0x7f050080
int color m3_chip_stroke_color 0x7f050081
int color m3_chip_text_color 0x7f050082
int color m3_dark_default_color_primary_text 0x7f050083
int color m3_dark_default_color_secondary_text 0x7f050084
int color m3_dark_highlighted_text 0x7f050085
int color m3_dark_hint_foreground 0x7f050086
int color m3_dark_primary_text_disable_only 0x7f050087
int color m3_default_color_primary_text 0x7f050088
int color m3_default_color_secondary_text 0x7f050089
int color m3_dynamic_dark_default_color_primary_text 0x7f05008a
int color m3_dynamic_dark_default_color_secondary_text 0x7f05008b
int color m3_dynamic_dark_highlighted_text 0x7f05008c
int color m3_dynamic_dark_hint_foreground 0x7f05008d
int color m3_dynamic_dark_primary_text_disable_only 0x7f05008e
int color m3_dynamic_default_color_primary_text 0x7f05008f
int color m3_dynamic_default_color_secondary_text 0x7f050090
int color m3_dynamic_highlighted_text 0x7f050091
int color m3_dynamic_hint_foreground 0x7f050092
int color m3_dynamic_primary_text_disable_only 0x7f050093
int color m3_efab_ripple_color_selector 0x7f050094
int color m3_elevated_chip_background_color 0x7f050095
int color m3_fab_efab_background_color_selector 0x7f050096
int color m3_fab_efab_foreground_color_selector 0x7f050097
int color m3_fab_ripple_color_selector 0x7f050098
int color m3_filled_icon_button_container_color_selector 0x7f050099
int color m3_highlighted_text 0x7f05009a
int color m3_hint_foreground 0x7f05009b
int color m3_icon_button_icon_color_selector 0x7f05009c
int color m3_navigation_bar_item_with_indicator_icon_tint 0x7f05009d
int color m3_navigation_bar_item_with_indicator_label_tint 0x7f05009e
int color m3_navigation_bar_ripple_color_selector 0x7f05009f
int color m3_navigation_item_background_color 0x7f0500a0
int color m3_navigation_item_icon_tint 0x7f0500a1
int color m3_navigation_item_ripple_color 0x7f0500a2
int color m3_navigation_item_text_color 0x7f0500a3
int color m3_navigation_rail_item_with_indicator_icon_tint 0x7f0500a4
int color m3_navigation_rail_item_with_indicator_label_tint 0x7f0500a5
int color m3_navigation_rail_ripple_color_selector 0x7f0500a6
int color m3_popupmenu_overlay_color 0x7f0500a7
int color m3_primary_text_disable_only 0x7f0500a8
int color m3_radiobutton_button_tint 0x7f0500a9
int color m3_radiobutton_ripple_tint 0x7f0500aa
int color m3_ref_palette_black 0x7f0500ab
int color m3_ref_palette_dynamic_neutral0 0x7f0500ac
int color m3_ref_palette_dynamic_neutral10 0x7f0500ad
int color m3_ref_palette_dynamic_neutral100 0x7f0500ae
int color m3_ref_palette_dynamic_neutral12 0x7f0500af
int color m3_ref_palette_dynamic_neutral17 0x7f0500b0
int color m3_ref_palette_dynamic_neutral20 0x7f0500b1
int color m3_ref_palette_dynamic_neutral22 0x7f0500b2
int color m3_ref_palette_dynamic_neutral24 0x7f0500b3
int color m3_ref_palette_dynamic_neutral30 0x7f0500b4
int color m3_ref_palette_dynamic_neutral4 0x7f0500b5
int color m3_ref_palette_dynamic_neutral40 0x7f0500b6
int color m3_ref_palette_dynamic_neutral50 0x7f0500b7
int color m3_ref_palette_dynamic_neutral6 0x7f0500b8
int color m3_ref_palette_dynamic_neutral60 0x7f0500b9
int color m3_ref_palette_dynamic_neutral70 0x7f0500ba
int color m3_ref_palette_dynamic_neutral80 0x7f0500bb
int color m3_ref_palette_dynamic_neutral87 0x7f0500bc
int color m3_ref_palette_dynamic_neutral90 0x7f0500bd
int color m3_ref_palette_dynamic_neutral92 0x7f0500be
int color m3_ref_palette_dynamic_neutral94 0x7f0500bf
int color m3_ref_palette_dynamic_neutral95 0x7f0500c0
int color m3_ref_palette_dynamic_neutral96 0x7f0500c1
int color m3_ref_palette_dynamic_neutral98 0x7f0500c2
int color m3_ref_palette_dynamic_neutral99 0x7f0500c3
int color m3_ref_palette_dynamic_neutral_variant0 0x7f0500c4
int color m3_ref_palette_dynamic_neutral_variant10 0x7f0500c5
int color m3_ref_palette_dynamic_neutral_variant100 0x7f0500c6
int color m3_ref_palette_dynamic_neutral_variant12 0x7f0500c7
int color m3_ref_palette_dynamic_neutral_variant17 0x7f0500c8
int color m3_ref_palette_dynamic_neutral_variant20 0x7f0500c9
int color m3_ref_palette_dynamic_neutral_variant22 0x7f0500ca
int color m3_ref_palette_dynamic_neutral_variant24 0x7f0500cb
int color m3_ref_palette_dynamic_neutral_variant30 0x7f0500cc
int color m3_ref_palette_dynamic_neutral_variant4 0x7f0500cd
int color m3_ref_palette_dynamic_neutral_variant40 0x7f0500ce
int color m3_ref_palette_dynamic_neutral_variant50 0x7f0500cf
int color m3_ref_palette_dynamic_neutral_variant6 0x7f0500d0
int color m3_ref_palette_dynamic_neutral_variant60 0x7f0500d1
int color m3_ref_palette_dynamic_neutral_variant70 0x7f0500d2
int color m3_ref_palette_dynamic_neutral_variant80 0x7f0500d3
int color m3_ref_palette_dynamic_neutral_variant87 0x7f0500d4
int color m3_ref_palette_dynamic_neutral_variant90 0x7f0500d5
int color m3_ref_palette_dynamic_neutral_variant92 0x7f0500d6
int color m3_ref_palette_dynamic_neutral_variant94 0x7f0500d7
int color m3_ref_palette_dynamic_neutral_variant95 0x7f0500d8
int color m3_ref_palette_dynamic_neutral_variant96 0x7f0500d9
int color m3_ref_palette_dynamic_neutral_variant98 0x7f0500da
int color m3_ref_palette_dynamic_neutral_variant99 0x7f0500db
int color m3_ref_palette_dynamic_primary0 0x7f0500dc
int color m3_ref_palette_dynamic_primary10 0x7f0500dd
int color m3_ref_palette_dynamic_primary100 0x7f0500de
int color m3_ref_palette_dynamic_primary20 0x7f0500df
int color m3_ref_palette_dynamic_primary30 0x7f0500e0
int color m3_ref_palette_dynamic_primary40 0x7f0500e1
int color m3_ref_palette_dynamic_primary50 0x7f0500e2
int color m3_ref_palette_dynamic_primary60 0x7f0500e3
int color m3_ref_palette_dynamic_primary70 0x7f0500e4
int color m3_ref_palette_dynamic_primary80 0x7f0500e5
int color m3_ref_palette_dynamic_primary90 0x7f0500e6
int color m3_ref_palette_dynamic_primary95 0x7f0500e7
int color m3_ref_palette_dynamic_primary99 0x7f0500e8
int color m3_ref_palette_dynamic_secondary0 0x7f0500e9
int color m3_ref_palette_dynamic_secondary10 0x7f0500ea
int color m3_ref_palette_dynamic_secondary100 0x7f0500eb
int color m3_ref_palette_dynamic_secondary20 0x7f0500ec
int color m3_ref_palette_dynamic_secondary30 0x7f0500ed
int color m3_ref_palette_dynamic_secondary40 0x7f0500ee
int color m3_ref_palette_dynamic_secondary50 0x7f0500ef
int color m3_ref_palette_dynamic_secondary60 0x7f0500f0
int color m3_ref_palette_dynamic_secondary70 0x7f0500f1
int color m3_ref_palette_dynamic_secondary80 0x7f0500f2
int color m3_ref_palette_dynamic_secondary90 0x7f0500f3
int color m3_ref_palette_dynamic_secondary95 0x7f0500f4
int color m3_ref_palette_dynamic_secondary99 0x7f0500f5
int color m3_ref_palette_dynamic_tertiary0 0x7f0500f6
int color m3_ref_palette_dynamic_tertiary10 0x7f0500f7
int color m3_ref_palette_dynamic_tertiary100 0x7f0500f8
int color m3_ref_palette_dynamic_tertiary20 0x7f0500f9
int color m3_ref_palette_dynamic_tertiary30 0x7f0500fa
int color m3_ref_palette_dynamic_tertiary40 0x7f0500fb
int color m3_ref_palette_dynamic_tertiary50 0x7f0500fc
int color m3_ref_palette_dynamic_tertiary60 0x7f0500fd
int color m3_ref_palette_dynamic_tertiary70 0x7f0500fe
int color m3_ref_palette_dynamic_tertiary80 0x7f0500ff
int color m3_ref_palette_dynamic_tertiary90 0x7f050100
int color m3_ref_palette_dynamic_tertiary95 0x7f050101
int color m3_ref_palette_dynamic_tertiary99 0x7f050102
int color m3_ref_palette_error0 0x7f050103
int color m3_ref_palette_error10 0x7f050104
int color m3_ref_palette_error100 0x7f050105
int color m3_ref_palette_error20 0x7f050106
int color m3_ref_palette_error30 0x7f050107
int color m3_ref_palette_error40 0x7f050108
int color m3_ref_palette_error50 0x7f050109
int color m3_ref_palette_error60 0x7f05010a
int color m3_ref_palette_error70 0x7f05010b
int color m3_ref_palette_error80 0x7f05010c
int color m3_ref_palette_error90 0x7f05010d
int color m3_ref_palette_error95 0x7f05010e
int color m3_ref_palette_error99 0x7f05010f
int color m3_ref_palette_neutral0 0x7f050110
int color m3_ref_palette_neutral10 0x7f050111
int color m3_ref_palette_neutral100 0x7f050112
int color m3_ref_palette_neutral12 0x7f050113
int color m3_ref_palette_neutral17 0x7f050114
int color m3_ref_palette_neutral20 0x7f050115
int color m3_ref_palette_neutral22 0x7f050116
int color m3_ref_palette_neutral24 0x7f050117
int color m3_ref_palette_neutral30 0x7f050118
int color m3_ref_palette_neutral4 0x7f050119
int color m3_ref_palette_neutral40 0x7f05011a
int color m3_ref_palette_neutral50 0x7f05011b
int color m3_ref_palette_neutral6 0x7f05011c
int color m3_ref_palette_neutral60 0x7f05011d
int color m3_ref_palette_neutral70 0x7f05011e
int color m3_ref_palette_neutral80 0x7f05011f
int color m3_ref_palette_neutral87 0x7f050120
int color m3_ref_palette_neutral90 0x7f050121
int color m3_ref_palette_neutral92 0x7f050122
int color m3_ref_palette_neutral94 0x7f050123
int color m3_ref_palette_neutral95 0x7f050124
int color m3_ref_palette_neutral96 0x7f050125
int color m3_ref_palette_neutral98 0x7f050126
int color m3_ref_palette_neutral99 0x7f050127
int color m3_ref_palette_neutral_variant0 0x7f050128
int color m3_ref_palette_neutral_variant10 0x7f050129
int color m3_ref_palette_neutral_variant100 0x7f05012a
int color m3_ref_palette_neutral_variant20 0x7f05012b
int color m3_ref_palette_neutral_variant30 0x7f05012c
int color m3_ref_palette_neutral_variant40 0x7f05012d
int color m3_ref_palette_neutral_variant50 0x7f05012e
int color m3_ref_palette_neutral_variant60 0x7f05012f
int color m3_ref_palette_neutral_variant70 0x7f050130
int color m3_ref_palette_neutral_variant80 0x7f050131
int color m3_ref_palette_neutral_variant90 0x7f050132
int color m3_ref_palette_neutral_variant95 0x7f050133
int color m3_ref_palette_neutral_variant99 0x7f050134
int color m3_ref_palette_primary0 0x7f050135
int color m3_ref_palette_primary10 0x7f050136
int color m3_ref_palette_primary100 0x7f050137
int color m3_ref_palette_primary20 0x7f050138
int color m3_ref_palette_primary30 0x7f050139
int color m3_ref_palette_primary40 0x7f05013a
int color m3_ref_palette_primary50 0x7f05013b
int color m3_ref_palette_primary60 0x7f05013c
int color m3_ref_palette_primary70 0x7f05013d
int color m3_ref_palette_primary80 0x7f05013e
int color m3_ref_palette_primary90 0x7f05013f
int color m3_ref_palette_primary95 0x7f050140
int color m3_ref_palette_primary99 0x7f050141
int color m3_ref_palette_secondary0 0x7f050142
int color m3_ref_palette_secondary10 0x7f050143
int color m3_ref_palette_secondary100 0x7f050144
int color m3_ref_palette_secondary20 0x7f050145
int color m3_ref_palette_secondary30 0x7f050146
int color m3_ref_palette_secondary40 0x7f050147
int color m3_ref_palette_secondary50 0x7f050148
int color m3_ref_palette_secondary60 0x7f050149
int color m3_ref_palette_secondary70 0x7f05014a
int color m3_ref_palette_secondary80 0x7f05014b
int color m3_ref_palette_secondary90 0x7f05014c
int color m3_ref_palette_secondary95 0x7f05014d
int color m3_ref_palette_secondary99 0x7f05014e
int color m3_ref_palette_tertiary0 0x7f05014f
int color m3_ref_palette_tertiary10 0x7f050150
int color m3_ref_palette_tertiary100 0x7f050151
int color m3_ref_palette_tertiary20 0x7f050152
int color m3_ref_palette_tertiary30 0x7f050153
int color m3_ref_palette_tertiary40 0x7f050154
int color m3_ref_palette_tertiary50 0x7f050155
int color m3_ref_palette_tertiary60 0x7f050156
int color m3_ref_palette_tertiary70 0x7f050157
int color m3_ref_palette_tertiary80 0x7f050158
int color m3_ref_palette_tertiary90 0x7f050159
int color m3_ref_palette_tertiary95 0x7f05015a
int color m3_ref_palette_tertiary99 0x7f05015b
int color m3_ref_palette_white 0x7f05015c
int color m3_selection_control_ripple_color_selector 0x7f05015d
int color m3_simple_item_ripple_color 0x7f05015e
int color m3_slider_active_track_color 0x7f05015f
int color m3_slider_active_track_color_legacy 0x7f050160
int color m3_slider_halo_color_legacy 0x7f050161
int color m3_slider_inactive_track_color 0x7f050162
int color m3_slider_inactive_track_color_legacy 0x7f050163
int color m3_slider_thumb_color 0x7f050164
int color m3_slider_thumb_color_legacy 0x7f050165
int color m3_switch_thumb_tint 0x7f050166
int color m3_switch_track_tint 0x7f050167
int color m3_sys_color_dark_background 0x7f050168
int color m3_sys_color_dark_error 0x7f050169
int color m3_sys_color_dark_error_container 0x7f05016a
int color m3_sys_color_dark_inverse_on_surface 0x7f05016b
int color m3_sys_color_dark_inverse_primary 0x7f05016c
int color m3_sys_color_dark_inverse_surface 0x7f05016d
int color m3_sys_color_dark_on_background 0x7f05016e
int color m3_sys_color_dark_on_error 0x7f05016f
int color m3_sys_color_dark_on_error_container 0x7f050170
int color m3_sys_color_dark_on_primary 0x7f050171
int color m3_sys_color_dark_on_primary_container 0x7f050172
int color m3_sys_color_dark_on_secondary 0x7f050173
int color m3_sys_color_dark_on_secondary_container 0x7f050174
int color m3_sys_color_dark_on_surface 0x7f050175
int color m3_sys_color_dark_on_surface_variant 0x7f050176
int color m3_sys_color_dark_on_tertiary 0x7f050177
int color m3_sys_color_dark_on_tertiary_container 0x7f050178
int color m3_sys_color_dark_outline 0x7f050179
int color m3_sys_color_dark_outline_variant 0x7f05017a
int color m3_sys_color_dark_primary 0x7f05017b
int color m3_sys_color_dark_primary_container 0x7f05017c
int color m3_sys_color_dark_secondary 0x7f05017d
int color m3_sys_color_dark_secondary_container 0x7f05017e
int color m3_sys_color_dark_surface 0x7f05017f
int color m3_sys_color_dark_surface_bright 0x7f050180
int color m3_sys_color_dark_surface_container 0x7f050181
int color m3_sys_color_dark_surface_container_high 0x7f050182
int color m3_sys_color_dark_surface_container_highest 0x7f050183
int color m3_sys_color_dark_surface_container_low 0x7f050184
int color m3_sys_color_dark_surface_container_lowest 0x7f050185
int color m3_sys_color_dark_surface_dim 0x7f050186
int color m3_sys_color_dark_surface_variant 0x7f050187
int color m3_sys_color_dark_tertiary 0x7f050188
int color m3_sys_color_dark_tertiary_container 0x7f050189
int color m3_sys_color_dynamic_dark_background 0x7f05018a
int color m3_sys_color_dynamic_dark_error 0x7f05018b
int color m3_sys_color_dynamic_dark_error_container 0x7f05018c
int color m3_sys_color_dynamic_dark_inverse_on_surface 0x7f05018d
int color m3_sys_color_dynamic_dark_inverse_primary 0x7f05018e
int color m3_sys_color_dynamic_dark_inverse_surface 0x7f05018f
int color m3_sys_color_dynamic_dark_on_background 0x7f050190
int color m3_sys_color_dynamic_dark_on_error 0x7f050191
int color m3_sys_color_dynamic_dark_on_error_container 0x7f050192
int color m3_sys_color_dynamic_dark_on_primary 0x7f050193
int color m3_sys_color_dynamic_dark_on_primary_container 0x7f050194
int color m3_sys_color_dynamic_dark_on_secondary 0x7f050195
int color m3_sys_color_dynamic_dark_on_secondary_container 0x7f050196
int color m3_sys_color_dynamic_dark_on_surface 0x7f050197
int color m3_sys_color_dynamic_dark_on_surface_variant 0x7f050198
int color m3_sys_color_dynamic_dark_on_tertiary 0x7f050199
int color m3_sys_color_dynamic_dark_on_tertiary_container 0x7f05019a
int color m3_sys_color_dynamic_dark_outline 0x7f05019b
int color m3_sys_color_dynamic_dark_outline_variant 0x7f05019c
int color m3_sys_color_dynamic_dark_primary 0x7f05019d
int color m3_sys_color_dynamic_dark_primary_container 0x7f05019e
int color m3_sys_color_dynamic_dark_secondary 0x7f05019f
int color m3_sys_color_dynamic_dark_secondary_container 0x7f0501a0
int color m3_sys_color_dynamic_dark_surface 0x7f0501a1
int color m3_sys_color_dynamic_dark_surface_bright 0x7f0501a2
int color m3_sys_color_dynamic_dark_surface_container 0x7f0501a3
int color m3_sys_color_dynamic_dark_surface_container_high 0x7f0501a4
int color m3_sys_color_dynamic_dark_surface_container_highest 0x7f0501a5
int color m3_sys_color_dynamic_dark_surface_container_low 0x7f0501a6
int color m3_sys_color_dynamic_dark_surface_container_lowest 0x7f0501a7
int color m3_sys_color_dynamic_dark_surface_dim 0x7f0501a8
int color m3_sys_color_dynamic_dark_surface_variant 0x7f0501a9
int color m3_sys_color_dynamic_dark_tertiary 0x7f0501aa
int color m3_sys_color_dynamic_dark_tertiary_container 0x7f0501ab
int color m3_sys_color_dynamic_light_background 0x7f0501ac
int color m3_sys_color_dynamic_light_error 0x7f0501ad
int color m3_sys_color_dynamic_light_error_container 0x7f0501ae
int color m3_sys_color_dynamic_light_inverse_on_surface 0x7f0501af
int color m3_sys_color_dynamic_light_inverse_primary 0x7f0501b0
int color m3_sys_color_dynamic_light_inverse_surface 0x7f0501b1
int color m3_sys_color_dynamic_light_on_background 0x7f0501b2
int color m3_sys_color_dynamic_light_on_error 0x7f0501b3
int color m3_sys_color_dynamic_light_on_error_container 0x7f0501b4
int color m3_sys_color_dynamic_light_on_primary 0x7f0501b5
int color m3_sys_color_dynamic_light_on_primary_container 0x7f0501b6
int color m3_sys_color_dynamic_light_on_secondary 0x7f0501b7
int color m3_sys_color_dynamic_light_on_secondary_container 0x7f0501b8
int color m3_sys_color_dynamic_light_on_surface 0x7f0501b9
int color m3_sys_color_dynamic_light_on_surface_variant 0x7f0501ba
int color m3_sys_color_dynamic_light_on_tertiary 0x7f0501bb
int color m3_sys_color_dynamic_light_on_tertiary_container 0x7f0501bc
int color m3_sys_color_dynamic_light_outline 0x7f0501bd
int color m3_sys_color_dynamic_light_outline_variant 0x7f0501be
int color m3_sys_color_dynamic_light_primary 0x7f0501bf
int color m3_sys_color_dynamic_light_primary_container 0x7f0501c0
int color m3_sys_color_dynamic_light_secondary 0x7f0501c1
int color m3_sys_color_dynamic_light_secondary_container 0x7f0501c2
int color m3_sys_color_dynamic_light_surface 0x7f0501c3
int color m3_sys_color_dynamic_light_surface_bright 0x7f0501c4
int color m3_sys_color_dynamic_light_surface_container 0x7f0501c5
int color m3_sys_color_dynamic_light_surface_container_high 0x7f0501c6
int color m3_sys_color_dynamic_light_surface_container_highest 0x7f0501c7
int color m3_sys_color_dynamic_light_surface_container_low 0x7f0501c8
int color m3_sys_color_dynamic_light_surface_container_lowest 0x7f0501c9
int color m3_sys_color_dynamic_light_surface_dim 0x7f0501ca
int color m3_sys_color_dynamic_light_surface_variant 0x7f0501cb
int color m3_sys_color_dynamic_light_tertiary 0x7f0501cc
int color m3_sys_color_dynamic_light_tertiary_container 0x7f0501cd
int color m3_sys_color_dynamic_on_primary_fixed 0x7f0501ce
int color m3_sys_color_dynamic_on_primary_fixed_variant 0x7f0501cf
int color m3_sys_color_dynamic_on_secondary_fixed 0x7f0501d0
int color m3_sys_color_dynamic_on_secondary_fixed_variant 0x7f0501d1
int color m3_sys_color_dynamic_on_tertiary_fixed 0x7f0501d2
int color m3_sys_color_dynamic_on_tertiary_fixed_variant 0x7f0501d3
int color m3_sys_color_dynamic_primary_fixed 0x7f0501d4
int color m3_sys_color_dynamic_primary_fixed_dim 0x7f0501d5
int color m3_sys_color_dynamic_secondary_fixed 0x7f0501d6
int color m3_sys_color_dynamic_secondary_fixed_dim 0x7f0501d7
int color m3_sys_color_dynamic_tertiary_fixed 0x7f0501d8
int color m3_sys_color_dynamic_tertiary_fixed_dim 0x7f0501d9
int color m3_sys_color_light_background 0x7f0501da
int color m3_sys_color_light_error 0x7f0501db
int color m3_sys_color_light_error_container 0x7f0501dc
int color m3_sys_color_light_inverse_on_surface 0x7f0501dd
int color m3_sys_color_light_inverse_primary 0x7f0501de
int color m3_sys_color_light_inverse_surface 0x7f0501df
int color m3_sys_color_light_on_background 0x7f0501e0
int color m3_sys_color_light_on_error 0x7f0501e1
int color m3_sys_color_light_on_error_container 0x7f0501e2
int color m3_sys_color_light_on_primary 0x7f0501e3
int color m3_sys_color_light_on_primary_container 0x7f0501e4
int color m3_sys_color_light_on_secondary 0x7f0501e5
int color m3_sys_color_light_on_secondary_container 0x7f0501e6
int color m3_sys_color_light_on_surface 0x7f0501e7
int color m3_sys_color_light_on_surface_variant 0x7f0501e8
int color m3_sys_color_light_on_tertiary 0x7f0501e9
int color m3_sys_color_light_on_tertiary_container 0x7f0501ea
int color m3_sys_color_light_outline 0x7f0501eb
int color m3_sys_color_light_outline_variant 0x7f0501ec
int color m3_sys_color_light_primary 0x7f0501ed
int color m3_sys_color_light_primary_container 0x7f0501ee
int color m3_sys_color_light_secondary 0x7f0501ef
int color m3_sys_color_light_secondary_container 0x7f0501f0
int color m3_sys_color_light_surface 0x7f0501f1
int color m3_sys_color_light_surface_bright 0x7f0501f2
int color m3_sys_color_light_surface_container 0x7f0501f3
int color m3_sys_color_light_surface_container_high 0x7f0501f4
int color m3_sys_color_light_surface_container_highest 0x7f0501f5
int color m3_sys_color_light_surface_container_low 0x7f0501f6
int color m3_sys_color_light_surface_container_lowest 0x7f0501f7
int color m3_sys_color_light_surface_dim 0x7f0501f8
int color m3_sys_color_light_surface_variant 0x7f0501f9
int color m3_sys_color_light_tertiary 0x7f0501fa
int color m3_sys_color_light_tertiary_container 0x7f0501fb
int color m3_sys_color_on_primary_fixed 0x7f0501fc
int color m3_sys_color_on_primary_fixed_variant 0x7f0501fd
int color m3_sys_color_on_secondary_fixed 0x7f0501fe
int color m3_sys_color_on_secondary_fixed_variant 0x7f0501ff
int color m3_sys_color_on_tertiary_fixed 0x7f050200
int color m3_sys_color_on_tertiary_fixed_variant 0x7f050201
int color m3_sys_color_primary_fixed 0x7f050202
int color m3_sys_color_primary_fixed_dim 0x7f050203
int color m3_sys_color_secondary_fixed 0x7f050204
int color m3_sys_color_secondary_fixed_dim 0x7f050205
int color m3_sys_color_tertiary_fixed 0x7f050206
int color m3_sys_color_tertiary_fixed_dim 0x7f050207
int color m3_tabs_icon_color 0x7f050208
int color m3_tabs_icon_color_secondary 0x7f050209
int color m3_tabs_ripple_color 0x7f05020a
int color m3_tabs_ripple_color_secondary 0x7f05020b
int color m3_tabs_text_color 0x7f05020c
int color m3_tabs_text_color_secondary 0x7f05020d
int color m3_text_button_background_color_selector 0x7f05020e
int color m3_text_button_foreground_color_selector 0x7f05020f
int color m3_text_button_ripple_color_selector 0x7f050210
int color m3_textfield_filled_background_color 0x7f050211
int color m3_textfield_indicator_text_color 0x7f050212
int color m3_textfield_input_text_color 0x7f050213
int color m3_textfield_label_color 0x7f050214
int color m3_textfield_stroke_color 0x7f050215
int color m3_timepicker_button_background_color 0x7f050216
int color m3_timepicker_button_ripple_color 0x7f050217
int color m3_timepicker_button_text_color 0x7f050218
int color m3_timepicker_clock_text_color 0x7f050219
int color m3_timepicker_display_background_color 0x7f05021a
int color m3_timepicker_display_ripple_color 0x7f05021b
int color m3_timepicker_display_text_color 0x7f05021c
int color m3_timepicker_secondary_text_button_ripple_color 0x7f05021d
int color m3_timepicker_secondary_text_button_text_color 0x7f05021e
int color m3_timepicker_time_input_stroke_color 0x7f05021f
int color m3_tonal_button_ripple_color_selector 0x7f050220
int color material_blue_grey_800 0x7f050221
int color material_blue_grey_900 0x7f050222
int color material_blue_grey_950 0x7f050223
int color material_cursor_color 0x7f050224
int color material_deep_teal_200 0x7f050225
int color material_deep_teal_500 0x7f050226
int color material_divider_color 0x7f050227
int color material_dynamic_color_dark_error 0x7f050228
int color material_dynamic_color_dark_error_container 0x7f050229
int color material_dynamic_color_dark_on_error 0x7f05022a
int color material_dynamic_color_dark_on_error_container 0x7f05022b
int color material_dynamic_color_light_error 0x7f05022c
int color material_dynamic_color_light_error_container 0x7f05022d
int color material_dynamic_color_light_on_error 0x7f05022e
int color material_dynamic_color_light_on_error_container 0x7f05022f
int color material_dynamic_neutral0 0x7f050230
int color material_dynamic_neutral10 0x7f050231
int color material_dynamic_neutral100 0x7f050232
int color material_dynamic_neutral20 0x7f050233
int color material_dynamic_neutral30 0x7f050234
int color material_dynamic_neutral40 0x7f050235
int color material_dynamic_neutral50 0x7f050236
int color material_dynamic_neutral60 0x7f050237
int color material_dynamic_neutral70 0x7f050238
int color material_dynamic_neutral80 0x7f050239
int color material_dynamic_neutral90 0x7f05023a
int color material_dynamic_neutral95 0x7f05023b
int color material_dynamic_neutral99 0x7f05023c
int color material_dynamic_neutral_variant0 0x7f05023d
int color material_dynamic_neutral_variant10 0x7f05023e
int color material_dynamic_neutral_variant100 0x7f05023f
int color material_dynamic_neutral_variant20 0x7f050240
int color material_dynamic_neutral_variant30 0x7f050241
int color material_dynamic_neutral_variant40 0x7f050242
int color material_dynamic_neutral_variant50 0x7f050243
int color material_dynamic_neutral_variant60 0x7f050244
int color material_dynamic_neutral_variant70 0x7f050245
int color material_dynamic_neutral_variant80 0x7f050246
int color material_dynamic_neutral_variant90 0x7f050247
int color material_dynamic_neutral_variant95 0x7f050248
int color material_dynamic_neutral_variant99 0x7f050249
int color material_dynamic_primary0 0x7f05024a
int color material_dynamic_primary10 0x7f05024b
int color material_dynamic_primary100 0x7f05024c
int color material_dynamic_primary20 0x7f05024d
int color material_dynamic_primary30 0x7f05024e
int color material_dynamic_primary40 0x7f05024f
int color material_dynamic_primary50 0x7f050250
int color material_dynamic_primary60 0x7f050251
int color material_dynamic_primary70 0x7f050252
int color material_dynamic_primary80 0x7f050253
int color material_dynamic_primary90 0x7f050254
int color material_dynamic_primary95 0x7f050255
int color material_dynamic_primary99 0x7f050256
int color material_dynamic_secondary0 0x7f050257
int color material_dynamic_secondary10 0x7f050258
int color material_dynamic_secondary100 0x7f050259
int color material_dynamic_secondary20 0x7f05025a
int color material_dynamic_secondary30 0x7f05025b
int color material_dynamic_secondary40 0x7f05025c
int color material_dynamic_secondary50 0x7f05025d
int color material_dynamic_secondary60 0x7f05025e
int color material_dynamic_secondary70 0x7f05025f
int color material_dynamic_secondary80 0x7f050260
int color material_dynamic_secondary90 0x7f050261
int color material_dynamic_secondary95 0x7f050262
int color material_dynamic_secondary99 0x7f050263
int color material_dynamic_tertiary0 0x7f050264
int color material_dynamic_tertiary10 0x7f050265
int color material_dynamic_tertiary100 0x7f050266
int color material_dynamic_tertiary20 0x7f050267
int color material_dynamic_tertiary30 0x7f050268
int color material_dynamic_tertiary40 0x7f050269
int color material_dynamic_tertiary50 0x7f05026a
int color material_dynamic_tertiary60 0x7f05026b
int color material_dynamic_tertiary70 0x7f05026c
int color material_dynamic_tertiary80 0x7f05026d
int color material_dynamic_tertiary90 0x7f05026e
int color material_dynamic_tertiary95 0x7f05026f
int color material_dynamic_tertiary99 0x7f050270
int color material_grey_100 0x7f050271
int color material_grey_300 0x7f050272
int color material_grey_50 0x7f050273
int color material_grey_600 0x7f050274
int color material_grey_800 0x7f050275
int color material_grey_850 0x7f050276
int color material_grey_900 0x7f050277
int color material_harmonized_color_error 0x7f050278
int color material_harmonized_color_error_container 0x7f050279
int color material_harmonized_color_on_error 0x7f05027a
int color material_harmonized_color_on_error_container 0x7f05027b
int color material_on_background_disabled 0x7f05027c
int color material_on_background_emphasis_high_type 0x7f05027d
int color material_on_background_emphasis_medium 0x7f05027e
int color material_on_primary_disabled 0x7f05027f
int color material_on_primary_emphasis_high_type 0x7f050280
int color material_on_primary_emphasis_medium 0x7f050281
int color material_on_surface_disabled 0x7f050282
int color material_on_surface_emphasis_high_type 0x7f050283
int color material_on_surface_emphasis_medium 0x7f050284
int color material_on_surface_stroke 0x7f050285
int color material_personalized__highlighted_text 0x7f050286
int color material_personalized__highlighted_text_inverse 0x7f050287
int color material_personalized_color_background 0x7f050288
int color material_personalized_color_control_activated 0x7f050289
int color material_personalized_color_control_highlight 0x7f05028a
int color material_personalized_color_control_normal 0x7f05028b
int color material_personalized_color_error 0x7f05028c
int color material_personalized_color_error_container 0x7f05028d
int color material_personalized_color_on_background 0x7f05028e
int color material_personalized_color_on_error 0x7f05028f
int color material_personalized_color_on_error_container 0x7f050290
int color material_personalized_color_on_primary 0x7f050291
int color material_personalized_color_on_primary_container 0x7f050292
int color material_personalized_color_on_secondary 0x7f050293
int color material_personalized_color_on_secondary_container 0x7f050294
int color material_personalized_color_on_surface 0x7f050295
int color material_personalized_color_on_surface_inverse 0x7f050296
int color material_personalized_color_on_surface_variant 0x7f050297
int color material_personalized_color_on_tertiary 0x7f050298
int color material_personalized_color_on_tertiary_container 0x7f050299
int color material_personalized_color_outline 0x7f05029a
int color material_personalized_color_outline_variant 0x7f05029b
int color material_personalized_color_primary 0x7f05029c
int color material_personalized_color_primary_container 0x7f05029d
int color material_personalized_color_primary_inverse 0x7f05029e
int color material_personalized_color_primary_text 0x7f05029f
int color material_personalized_color_primary_text_inverse 0x7f0502a0
int color material_personalized_color_secondary 0x7f0502a1
int color material_personalized_color_secondary_container 0x7f0502a2
int color material_personalized_color_secondary_text 0x7f0502a3
int color material_personalized_color_secondary_text_inverse 0x7f0502a4
int color material_personalized_color_surface 0x7f0502a5
int color material_personalized_color_surface_bright 0x7f0502a6
int color material_personalized_color_surface_container 0x7f0502a7
int color material_personalized_color_surface_container_high 0x7f0502a8
int color material_personalized_color_surface_container_highest 0x7f0502a9
int color material_personalized_color_surface_container_low 0x7f0502aa
int color material_personalized_color_surface_container_lowest 0x7f0502ab
int color material_personalized_color_surface_dim 0x7f0502ac
int color material_personalized_color_surface_inverse 0x7f0502ad
int color material_personalized_color_surface_variant 0x7f0502ae
int color material_personalized_color_tertiary 0x7f0502af
int color material_personalized_color_tertiary_container 0x7f0502b0
int color material_personalized_color_text_hint_foreground_inverse 0x7f0502b1
int color material_personalized_color_text_primary_inverse 0x7f0502b2
int color material_personalized_color_text_primary_inverse_disable_only 0x7f0502b3
int color material_personalized_color_text_secondary_and_tertiary_inverse 0x7f0502b4
int color material_personalized_color_text_secondary_and_tertiary_inverse_disabled 0x7f0502b5
int color material_personalized_hint_foreground 0x7f0502b6
int color material_personalized_hint_foreground_inverse 0x7f0502b7
int color material_personalized_primary_inverse_text_disable_only 0x7f0502b8
int color material_personalized_primary_text_disable_only 0x7f0502b9
int color material_slider_active_tick_marks_color 0x7f0502ba
int color material_slider_active_track_color 0x7f0502bb
int color material_slider_halo_color 0x7f0502bc
int color material_slider_inactive_tick_marks_color 0x7f0502bd
int color material_slider_inactive_track_color 0x7f0502be
int color material_slider_thumb_color 0x7f0502bf
int color material_timepicker_button_background 0x7f0502c0
int color material_timepicker_button_stroke 0x7f0502c1
int color material_timepicker_clock_text_color 0x7f0502c2
int color material_timepicker_clockface 0x7f0502c3
int color material_timepicker_modebutton_tint 0x7f0502c4
int color mtrl_btn_bg_color_selector 0x7f0502c5
int color mtrl_btn_ripple_color 0x7f0502c6
int color mtrl_btn_stroke_color_selector 0x7f0502c7
int color mtrl_btn_text_btn_bg_color_selector 0x7f0502c8
int color mtrl_btn_text_btn_ripple_color 0x7f0502c9
int color mtrl_btn_text_color_disabled 0x7f0502ca
int color mtrl_btn_text_color_selector 0x7f0502cb
int color mtrl_btn_transparent_bg_color 0x7f0502cc
int color mtrl_calendar_item_stroke_color 0x7f0502cd
int color mtrl_calendar_selected_range 0x7f0502ce
int color mtrl_card_view_foreground 0x7f0502cf
int color mtrl_card_view_ripple 0x7f0502d0
int color mtrl_chip_background_color 0x7f0502d1
int color mtrl_chip_close_icon_tint 0x7f0502d2
int color mtrl_chip_surface_color 0x7f0502d3
int color mtrl_chip_text_color 0x7f0502d4
int color mtrl_choice_chip_background_color 0x7f0502d5
int color mtrl_choice_chip_ripple_color 0x7f0502d6
int color mtrl_choice_chip_text_color 0x7f0502d7
int color mtrl_error 0x7f0502d8
int color mtrl_fab_bg_color_selector 0x7f0502d9
int color mtrl_fab_icon_text_color_selector 0x7f0502da
int color mtrl_fab_ripple_color 0x7f0502db
int color mtrl_filled_background_color 0x7f0502dc
int color mtrl_filled_icon_tint 0x7f0502dd
int color mtrl_filled_stroke_color 0x7f0502de
int color mtrl_indicator_text_color 0x7f0502df
int color mtrl_navigation_bar_colored_item_tint 0x7f0502e0
int color mtrl_navigation_bar_colored_ripple_color 0x7f0502e1
int color mtrl_navigation_bar_item_tint 0x7f0502e2
int color mtrl_navigation_bar_ripple_color 0x7f0502e3
int color mtrl_navigation_item_background_color 0x7f0502e4
int color mtrl_navigation_item_icon_tint 0x7f0502e5
int color mtrl_navigation_item_text_color 0x7f0502e6
int color mtrl_on_primary_text_btn_text_color_selector 0x7f0502e7
int color mtrl_on_surface_ripple_color 0x7f0502e8
int color mtrl_outlined_icon_tint 0x7f0502e9
int color mtrl_outlined_stroke_color 0x7f0502ea
int color mtrl_popupmenu_overlay_color 0x7f0502eb
int color mtrl_scrim_color 0x7f0502ec
int color mtrl_switch_thumb_icon_tint 0x7f0502ed
int color mtrl_switch_thumb_tint 0x7f0502ee
int color mtrl_switch_track_decoration_tint 0x7f0502ef
int color mtrl_switch_track_tint 0x7f0502f0
int color mtrl_tabs_colored_ripple_color 0x7f0502f1
int color mtrl_tabs_icon_color_selector 0x7f0502f2
int color mtrl_tabs_icon_color_selector_colored 0x7f0502f3
int color mtrl_tabs_legacy_text_color_selector 0x7f0502f4
int color mtrl_tabs_ripple_color 0x7f0502f5
int color mtrl_text_btn_text_color_selector 0x7f0502f6
int color mtrl_textinput_default_box_stroke_color 0x7f0502f7
int color mtrl_textinput_disabled_color 0x7f0502f8
int color mtrl_textinput_filled_box_default_background_color 0x7f0502f9
int color mtrl_textinput_focused_box_stroke_color 0x7f0502fa
int color mtrl_textinput_hovered_box_stroke_color 0x7f0502fb
int color notification_action_color_filter 0x7f0502fc
int color notification_icon_bg_color 0x7f0502fd
int color notification_material_background_media_default_color 0x7f0502fe
int color pay_button_generic_background_color_dark 0x7f0502ff
int color pay_button_generic_background_color_light 0x7f050300
int color pay_button_generic_background_outline_color_dark 0x7f050301
int color pay_button_generic_background_outline_color_light 0x7f050302
int color pay_button_generic_ripple_color_dark 0x7f050303
int color pay_button_generic_ripple_color_light 0x7f050304
int color preference_fallback_accent_color 0x7f050305
int color primary_dark_material_dark 0x7f050306
int color primary_dark_material_light 0x7f050307
int color primary_material_dark 0x7f050308
int color primary_material_light 0x7f050309
int color primary_text_default_material_dark 0x7f05030a
int color primary_text_default_material_light 0x7f05030b
int color primary_text_disabled_material_dark 0x7f05030c
int color primary_text_disabled_material_light 0x7f05030d
int color ripple_material_dark 0x7f05030e
int color ripple_material_light 0x7f05030f
int color secondary_text_default_material_dark 0x7f050310
int color secondary_text_default_material_light 0x7f050311
int color secondary_text_disabled_material_dark 0x7f050312
int color secondary_text_disabled_material_light 0x7f050313
int color stripe_3ds2_accent 0x7f050314
int color stripe_3ds2_background 0x7f050315
int color stripe_3ds2_chevron 0x7f050316
int color stripe_3ds2_control_activated 0x7f050317
int color stripe_3ds2_divider 0x7f050318
int color stripe_3ds2_primary 0x7f050319
int color stripe_3ds2_primary_dark 0x7f05031a
int color stripe_3ds2_text_color 0x7f05031b
int color stripe_3ds2_text_color_primary 0x7f05031c
int color stripe_3ds2_text_edit 0x7f05031d
int color stripe_3ds2_text_info_toggled 0x7f05031e
int color stripe_3ds2_text_input_fill 0x7f05031f
int color stripe_3ds2_text_input_hint 0x7f050320
int color stripe_accent_color_default 0x7f050321
int color stripe_add_payment_method_pressed 0x7f050322
int color stripe_card_form_view_background_default 0x7f050323
int color stripe_card_form_view_background_disabled 0x7f050324
int color stripe_card_form_view_form_border 0x7f050325
int color stripe_card_form_view_form_error 0x7f050326
int color stripe_card_form_view_text_color 0x7f050327
int color stripe_card_form_view_textinput_color 0x7f050328
int color stripe_card_form_view_textinput_disabled_color 0x7f050329
int color stripe_card_multiline_textinput_hint_color 0x7f05032a
int color stripe_card_widget_progress_background 0x7f05032b
int color stripe_card_widget_progress_foreground 0x7f05032c
int color stripe_clear_icon_tint 0x7f05032d
int color stripe_color_text_secondary_default 0x7f05032e
int color stripe_color_text_unselected_primary_default 0x7f05032f
int color stripe_color_text_unselected_secondary_default 0x7f050330
int color stripe_control_normal_color_default 0x7f050331
int color stripe_divider_text_color 0x7f050332
int color stripe_error_text_dark_theme 0x7f050333
int color stripe_error_text_light_theme 0x7f050334
int color stripe_info_zone_header_text 0x7f050335
int color stripe_link_window_background 0x7f050336
int color stripe_mandate_text_color 0x7f050337
int color stripe_paymentsheet_add_payment_method_form_stroke 0x7f050338
int color stripe_paymentsheet_add_pm_card_selected_stroke 0x7f050339
int color stripe_paymentsheet_add_pm_card_unselected_stroke 0x7f05033a
int color stripe_paymentsheet_background 0x7f05033b
int color stripe_paymentsheet_card_stroke 0x7f05033c
int color stripe_paymentsheet_country_chevron_color 0x7f05033d
int color stripe_paymentsheet_elements_background_default 0x7f05033e
int color stripe_paymentsheet_elements_background_disabled 0x7f05033f
int color stripe_paymentsheet_elements_background_states 0x7f050340
int color stripe_paymentsheet_form 0x7f050341
int color stripe_paymentsheet_form_border 0x7f050342
int color stripe_paymentsheet_form_error 0x7f050343
int color stripe_paymentsheet_googlepay_divider_background 0x7f050344
int color stripe_paymentsheet_googlepay_divider_line 0x7f050345
int color stripe_paymentsheet_googlepay_divider_text 0x7f050346
int color stripe_paymentsheet_googlepay_primary_button_background_color 0x7f050347
int color stripe_paymentsheet_googlepay_primary_button_tint_color 0x7f050348
int color stripe_paymentsheet_header_text 0x7f050349
int color stripe_paymentsheet_link_mark 0x7f05034a
int color stripe_paymentsheet_payment_method_label_text 0x7f05034b
int color stripe_paymentsheet_payment_method_label_text_disabled 0x7f05034c
int color stripe_paymentsheet_payment_option_selected_stroke 0x7f05034d
int color stripe_paymentsheet_payment_option_unselected_stroke 0x7f05034e
int color stripe_paymentsheet_primary_button_confirming_progress 0x7f05034f
int color stripe_paymentsheet_primary_button_default_background 0x7f050350
int color stripe_paymentsheet_primary_button_success_background 0x7f050351
int color stripe_paymentsheet_save_checkbox_color 0x7f050352
int color stripe_paymentsheet_testmode_background 0x7f050353
int color stripe_paymentsheet_testmode_text 0x7f050354
int color stripe_paymentsheet_textinput_color 0x7f050355
int color stripe_paymentsheet_textinputlayout_hint 0x7f050356
int color stripe_paymentsheet_title_text 0x7f050357
int color stripe_paymentsheet_toolbar_items_color 0x7f050358
int color stripe_swipe_start_payment_method 0x7f050359
int color stripe_swipe_threshold_payment_method 0x7f05035a
int color stripe_text_color_secondary 0x7f05035b
int color stripe_title_text_color 0x7f05035c
int color stripe_toolbar_color_default 0x7f05035d
int color stripe_toolbar_color_default_dark 0x7f05035e
int color switch_thumb_disabled_material_dark 0x7f05035f
int color switch_thumb_disabled_material_light 0x7f050360
int color switch_thumb_material_dark 0x7f050361
int color switch_thumb_material_light 0x7f050362
int color switch_thumb_normal_material_dark 0x7f050363
int color switch_thumb_normal_material_light 0x7f050364
int color tooltip_background_dark 0x7f050365
int color tooltip_background_light 0x7f050366
int color vector_tint_color 0x7f050367
int color vector_tint_theme_color 0x7f050368
int color wallet_bright_foreground_disabled_holo_light 0x7f050369
int color wallet_bright_foreground_holo_dark 0x7f05036a
int color wallet_bright_foreground_holo_light 0x7f05036b
int color wallet_dim_foreground_disabled_holo_dark 0x7f05036c
int color wallet_dim_foreground_holo_dark 0x7f05036d
int color wallet_highlighted_text_holo_dark 0x7f05036e
int color wallet_highlighted_text_holo_light 0x7f05036f
int color wallet_hint_foreground_holo_dark 0x7f050370
int color wallet_hint_foreground_holo_light 0x7f050371
int color wallet_holo_blue_light 0x7f050372
int color wallet_link_text_light 0x7f050373
int color wallet_primary_text_holo_light 0x7f050374
int color wallet_secondary_text_holo_dark 0x7f050375
int dimen abc_action_bar_content_inset_material 0x7f060000
int dimen abc_action_bar_content_inset_with_nav 0x7f060001
int dimen abc_action_bar_default_height_material 0x7f060002
int dimen abc_action_bar_default_padding_end_material 0x7f060003
int dimen abc_action_bar_default_padding_start_material 0x7f060004
int dimen abc_action_bar_elevation_material 0x7f060005
int dimen abc_action_bar_icon_vertical_padding_material 0x7f060006
int dimen abc_action_bar_overflow_padding_end_material 0x7f060007
int dimen abc_action_bar_overflow_padding_start_material 0x7f060008
int dimen abc_action_bar_stacked_max_height 0x7f060009
int dimen abc_action_bar_stacked_tab_max_width 0x7f06000a
int dimen abc_action_bar_subtitle_bottom_margin_material 0x7f06000b
int dimen abc_action_bar_subtitle_top_margin_material 0x7f06000c
int dimen abc_action_button_min_height_material 0x7f06000d
int dimen abc_action_button_min_width_material 0x7f06000e
int dimen abc_action_button_min_width_overflow_material 0x7f06000f
int dimen abc_alert_dialog_button_bar_height 0x7f060010
int dimen abc_alert_dialog_button_dimen 0x7f060011
int dimen abc_button_inset_horizontal_material 0x7f060012
int dimen abc_button_inset_vertical_material 0x7f060013
int dimen abc_button_padding_horizontal_material 0x7f060014
int dimen abc_button_padding_vertical_material 0x7f060015
int dimen abc_cascading_menus_min_smallest_width 0x7f060016
int dimen abc_config_prefDialogWidth 0x7f060017
int dimen abc_control_corner_material 0x7f060018
int dimen abc_control_inset_material 0x7f060019
int dimen abc_control_padding_material 0x7f06001a
int dimen abc_dialog_corner_radius_material 0x7f06001b
int dimen abc_dialog_fixed_height_major 0x7f06001c
int dimen abc_dialog_fixed_height_minor 0x7f06001d
int dimen abc_dialog_fixed_width_major 0x7f06001e
int dimen abc_dialog_fixed_width_minor 0x7f06001f
int dimen abc_dialog_list_padding_bottom_no_buttons 0x7f060020
int dimen abc_dialog_list_padding_top_no_title 0x7f060021
int dimen abc_dialog_min_width_major 0x7f060022
int dimen abc_dialog_min_width_minor 0x7f060023
int dimen abc_dialog_padding_material 0x7f060024
int dimen abc_dialog_padding_top_material 0x7f060025
int dimen abc_dialog_title_divider_material 0x7f060026
int dimen abc_disabled_alpha_material_dark 0x7f060027
int dimen abc_disabled_alpha_material_light 0x7f060028
int dimen abc_dropdownitem_icon_width 0x7f060029
int dimen abc_dropdownitem_text_padding_left 0x7f06002a
int dimen abc_dropdownitem_text_padding_right 0x7f06002b
int dimen abc_edit_text_inset_bottom_material 0x7f06002c
int dimen abc_edit_text_inset_horizontal_material 0x7f06002d
int dimen abc_edit_text_inset_top_material 0x7f06002e
int dimen abc_floating_window_z 0x7f06002f
int dimen abc_list_item_height_large_material 0x7f060030
int dimen abc_list_item_height_material 0x7f060031
int dimen abc_list_item_height_small_material 0x7f060032
int dimen abc_list_item_padding_horizontal_material 0x7f060033
int dimen abc_panel_menu_list_width 0x7f060034
int dimen abc_progress_bar_height_material 0x7f060035
int dimen abc_search_view_preferred_height 0x7f060036
int dimen abc_search_view_preferred_width 0x7f060037
int dimen abc_seekbar_track_background_height_material 0x7f060038
int dimen abc_seekbar_track_progress_height_material 0x7f060039
int dimen abc_select_dialog_padding_start_material 0x7f06003a
int dimen abc_star_big 0x7f06003b
int dimen abc_star_medium 0x7f06003c
int dimen abc_star_small 0x7f06003d
int dimen abc_switch_padding 0x7f06003e
int dimen abc_text_size_body_1_material 0x7f06003f
int dimen abc_text_size_body_2_material 0x7f060040
int dimen abc_text_size_button_material 0x7f060041
int dimen abc_text_size_caption_material 0x7f060042
int dimen abc_text_size_display_1_material 0x7f060043
int dimen abc_text_size_display_2_material 0x7f060044
int dimen abc_text_size_display_3_material 0x7f060045
int dimen abc_text_size_display_4_material 0x7f060046
int dimen abc_text_size_headline_material 0x7f060047
int dimen abc_text_size_large_material 0x7f060048
int dimen abc_text_size_medium_material 0x7f060049
int dimen abc_text_size_menu_header_material 0x7f06004a
int dimen abc_text_size_menu_material 0x7f06004b
int dimen abc_text_size_small_material 0x7f06004c
int dimen abc_text_size_subhead_material 0x7f06004d
int dimen abc_text_size_subtitle_material_toolbar 0x7f06004e
int dimen abc_text_size_title_material 0x7f06004f
int dimen abc_text_size_title_material_toolbar 0x7f060050
int dimen appcompat_dialog_background_inset 0x7f060051
int dimen browser_actions_context_menu_max_width 0x7f060052
int dimen browser_actions_context_menu_min_padding 0x7f060053
int dimen cardview_compat_inset_shadow 0x7f060054
int dimen cardview_default_elevation 0x7f060055
int dimen cardview_default_radius 0x7f060056
int dimen clock_face_margin_start 0x7f060057
int dimen compat_button_inset_horizontal_material 0x7f060058
int dimen compat_button_inset_vertical_material 0x7f060059
int dimen compat_button_padding_horizontal_material 0x7f06005a
int dimen compat_button_padding_vertical_material 0x7f06005b
int dimen compat_control_corner_material 0x7f06005c
int dimen compat_notification_large_icon_max_height 0x7f06005d
int dimen compat_notification_large_icon_max_width 0x7f06005e
int dimen def_drawer_elevation 0x7f06005f
int dimen design_appbar_elevation 0x7f060060
int dimen design_bottom_navigation_active_item_max_width 0x7f060061
int dimen design_bottom_navigation_active_item_min_width 0x7f060062
int dimen design_bottom_navigation_active_text_size 0x7f060063
int dimen design_bottom_navigation_elevation 0x7f060064
int dimen design_bottom_navigation_height 0x7f060065
int dimen design_bottom_navigation_icon_size 0x7f060066
int dimen design_bottom_navigation_item_max_width 0x7f060067
int dimen design_bottom_navigation_item_min_width 0x7f060068
int dimen design_bottom_navigation_label_padding 0x7f060069
int dimen design_bottom_navigation_margin 0x7f06006a
int dimen design_bottom_navigation_shadow_height 0x7f06006b
int dimen design_bottom_navigation_text_size 0x7f06006c
int dimen design_bottom_sheet_elevation 0x7f06006d
int dimen design_bottom_sheet_modal_elevation 0x7f06006e
int dimen design_bottom_sheet_peek_height_min 0x7f06006f
int dimen design_fab_border_width 0x7f060070
int dimen design_fab_elevation 0x7f060071
int dimen design_fab_image_size 0x7f060072
int dimen design_fab_size_mini 0x7f060073
int dimen design_fab_size_normal 0x7f060074
int dimen design_fab_translation_z_hovered_focused 0x7f060075
int dimen design_fab_translation_z_pressed 0x7f060076
int dimen design_navigation_elevation 0x7f060077
int dimen design_navigation_icon_padding 0x7f060078
int dimen design_navigation_icon_size 0x7f060079
int dimen design_navigation_item_horizontal_padding 0x7f06007a
int dimen design_navigation_item_icon_padding 0x7f06007b
int dimen design_navigation_item_vertical_padding 0x7f06007c
int dimen design_navigation_max_width 0x7f06007d
int dimen design_navigation_padding_bottom 0x7f06007e
int dimen design_navigation_separator_vertical_padding 0x7f06007f
int dimen design_snackbar_action_inline_max_width 0x7f060080
int dimen design_snackbar_action_text_color_alpha 0x7f060081
int dimen design_snackbar_background_corner_radius 0x7f060082
int dimen design_snackbar_elevation 0x7f060083
int dimen design_snackbar_extra_spacing_horizontal 0x7f060084
int dimen design_snackbar_max_width 0x7f060085
int dimen design_snackbar_min_width 0x7f060086
int dimen design_snackbar_padding_horizontal 0x7f060087
int dimen design_snackbar_padding_vertical 0x7f060088
int dimen design_snackbar_padding_vertical_2lines 0x7f060089
int dimen design_snackbar_text_size 0x7f06008a
int dimen design_tab_max_width 0x7f06008b
int dimen design_tab_scrollable_min_width 0x7f06008c
int dimen design_tab_text_size 0x7f06008d
int dimen design_tab_text_size_2line 0x7f06008e
int dimen design_textinput_caption_translate_y 0x7f06008f
int dimen disabled_alpha_material_dark 0x7f060090
int dimen disabled_alpha_material_light 0x7f060091
int dimen fastscroll_default_thickness 0x7f060092
int dimen fastscroll_margin 0x7f060093
int dimen fastscroll_minimum_range 0x7f060094
int dimen highlight_alpha_material_colored 0x7f060095
int dimen highlight_alpha_material_dark 0x7f060096
int dimen highlight_alpha_material_light 0x7f060097
int dimen hint_alpha_material_dark 0x7f060098
int dimen hint_alpha_material_light 0x7f060099
int dimen hint_pressed_alpha_material_dark 0x7f06009a
int dimen hint_pressed_alpha_material_light 0x7f06009b
int dimen item_touch_helper_max_drag_scroll_per_frame 0x7f06009c
int dimen item_touch_helper_swipe_escape_max_velocity 0x7f06009d
int dimen item_touch_helper_swipe_escape_velocity 0x7f06009e
int dimen m3_alert_dialog_action_bottom_padding 0x7f06009f
int dimen m3_alert_dialog_action_top_padding 0x7f0600a0
int dimen m3_alert_dialog_corner_size 0x7f0600a1
int dimen m3_alert_dialog_elevation 0x7f0600a2
int dimen m3_alert_dialog_icon_margin 0x7f0600a3
int dimen m3_alert_dialog_icon_size 0x7f0600a4
int dimen m3_alert_dialog_title_bottom_margin 0x7f0600a5
int dimen m3_appbar_expanded_title_margin_bottom 0x7f0600a6
int dimen m3_appbar_expanded_title_margin_horizontal 0x7f0600a7
int dimen m3_appbar_scrim_height_trigger 0x7f0600a8
int dimen m3_appbar_scrim_height_trigger_large 0x7f0600a9
int dimen m3_appbar_scrim_height_trigger_medium 0x7f0600aa
int dimen m3_appbar_size_compact 0x7f0600ab
int dimen m3_appbar_size_large 0x7f0600ac
int dimen m3_appbar_size_medium 0x7f0600ad
int dimen m3_back_progress_bottom_container_max_scale_x_distance 0x7f0600ae
int dimen m3_back_progress_bottom_container_max_scale_y_distance 0x7f0600af
int dimen m3_back_progress_main_container_max_translation_y 0x7f0600b0
int dimen m3_back_progress_main_container_min_edge_gap 0x7f0600b1
int dimen m3_back_progress_side_container_max_scale_x_distance_grow 0x7f0600b2
int dimen m3_back_progress_side_container_max_scale_x_distance_shrink 0x7f0600b3
int dimen m3_back_progress_side_container_max_scale_y_distance 0x7f0600b4
int dimen m3_badge_horizontal_offset 0x7f0600b5
int dimen m3_badge_offset 0x7f0600b6
int dimen m3_badge_size 0x7f0600b7
int dimen m3_badge_vertical_offset 0x7f0600b8
int dimen m3_badge_with_text_horizontal_offset 0x7f0600b9
int dimen m3_badge_with_text_offset 0x7f0600ba
int dimen m3_badge_with_text_size 0x7f0600bb
int dimen m3_badge_with_text_vertical_offset 0x7f0600bc
int dimen m3_badge_with_text_vertical_padding 0x7f0600bd
int dimen m3_bottom_nav_item_active_indicator_height 0x7f0600be
int dimen m3_bottom_nav_item_active_indicator_margin_horizontal 0x7f0600bf
int dimen m3_bottom_nav_item_active_indicator_width 0x7f0600c0
int dimen m3_bottom_nav_item_padding_bottom 0x7f0600c1
int dimen m3_bottom_nav_item_padding_top 0x7f0600c2
int dimen m3_bottom_nav_min_height 0x7f0600c3
int dimen m3_bottom_sheet_drag_handle_bottom_padding 0x7f0600c4
int dimen m3_bottom_sheet_elevation 0x7f0600c5
int dimen m3_bottom_sheet_modal_elevation 0x7f0600c6
int dimen m3_bottomappbar_fab_cradle_margin 0x7f0600c7
int dimen m3_bottomappbar_fab_cradle_rounded_corner_radius 0x7f0600c8
int dimen m3_bottomappbar_fab_cradle_vertical_offset 0x7f0600c9
int dimen m3_bottomappbar_fab_end_margin 0x7f0600ca
int dimen m3_bottomappbar_height 0x7f0600cb
int dimen m3_bottomappbar_horizontal_padding 0x7f0600cc
int dimen m3_btn_dialog_btn_min_width 0x7f0600cd
int dimen m3_btn_dialog_btn_spacing 0x7f0600ce
int dimen m3_btn_disabled_elevation 0x7f0600cf
int dimen m3_btn_disabled_translation_z 0x7f0600d0
int dimen m3_btn_elevated_btn_elevation 0x7f0600d1
int dimen m3_btn_elevation 0x7f0600d2
int dimen m3_btn_icon_btn_padding_left 0x7f0600d3
int dimen m3_btn_icon_btn_padding_right 0x7f0600d4
int dimen m3_btn_icon_only_default_padding 0x7f0600d5
int dimen m3_btn_icon_only_default_size 0x7f0600d6
int dimen m3_btn_icon_only_icon_padding 0x7f0600d7
int dimen m3_btn_icon_only_min_width 0x7f0600d8
int dimen m3_btn_inset 0x7f0600d9
int dimen m3_btn_max_width 0x7f0600da
int dimen m3_btn_padding_bottom 0x7f0600db
int dimen m3_btn_padding_left 0x7f0600dc
int dimen m3_btn_padding_right 0x7f0600dd
int dimen m3_btn_padding_top 0x7f0600de
int dimen m3_btn_stroke_size 0x7f0600df
int dimen m3_btn_text_btn_icon_padding_left 0x7f0600e0
int dimen m3_btn_text_btn_icon_padding_right 0x7f0600e1
int dimen m3_btn_text_btn_padding_left 0x7f0600e2
int dimen m3_btn_text_btn_padding_right 0x7f0600e3
int dimen m3_btn_translation_z_base 0x7f0600e4
int dimen m3_btn_translation_z_hovered 0x7f0600e5
int dimen m3_card_disabled_z 0x7f0600e6
int dimen m3_card_dragged_z 0x7f0600e7
int dimen m3_card_elevated_disabled_z 0x7f0600e8
int dimen m3_card_elevated_dragged_z 0x7f0600e9
int dimen m3_card_elevated_elevation 0x7f0600ea
int dimen m3_card_elevated_hovered_z 0x7f0600eb
int dimen m3_card_elevation 0x7f0600ec
int dimen m3_card_hovered_z 0x7f0600ed
int dimen m3_card_stroke_width 0x7f0600ee
int dimen m3_carousel_debug_keyline_width 0x7f0600ef
int dimen m3_carousel_extra_small_item_size 0x7f0600f0
int dimen m3_carousel_gone_size 0x7f0600f1
int dimen m3_carousel_small_item_default_corner_size 0x7f0600f2
int dimen m3_carousel_small_item_size_max 0x7f0600f3
int dimen m3_carousel_small_item_size_min 0x7f0600f4
int dimen m3_chip_checked_hovered_translation_z 0x7f0600f5
int dimen m3_chip_corner_size 0x7f0600f6
int dimen m3_chip_disabled_translation_z 0x7f0600f7
int dimen m3_chip_dragged_translation_z 0x7f0600f8
int dimen m3_chip_elevated_elevation 0x7f0600f9
int dimen m3_chip_hovered_translation_z 0x7f0600fa
int dimen m3_chip_icon_size 0x7f0600fb
int dimen m3_comp_assist_chip_container_height 0x7f0600fc
int dimen m3_comp_assist_chip_elevated_container_elevation 0x7f0600fd
int dimen m3_comp_assist_chip_flat_container_elevation 0x7f0600fe
int dimen m3_comp_assist_chip_flat_outline_width 0x7f0600ff
int dimen m3_comp_assist_chip_with_icon_icon_size 0x7f060100
int dimen m3_comp_badge_large_size 0x7f060101
int dimen m3_comp_badge_size 0x7f060102
int dimen m3_comp_bottom_app_bar_container_elevation 0x7f060103
int dimen m3_comp_bottom_app_bar_container_height 0x7f060104
int dimen m3_comp_checkbox_selected_disabled_container_opacity 0x7f060105
int dimen m3_comp_date_picker_modal_date_today_container_outline_width 0x7f060106
int dimen m3_comp_date_picker_modal_header_container_height 0x7f060107
int dimen m3_comp_date_picker_modal_range_selection_header_container_height 0x7f060108
int dimen m3_comp_divider_thickness 0x7f060109
int dimen m3_comp_elevated_button_container_elevation 0x7f06010a
int dimen m3_comp_elevated_button_disabled_container_elevation 0x7f06010b
int dimen m3_comp_elevated_card_container_elevation 0x7f06010c
int dimen m3_comp_elevated_card_icon_size 0x7f06010d
int dimen m3_comp_extended_fab_primary_container_elevation 0x7f06010e
int dimen m3_comp_extended_fab_primary_container_height 0x7f06010f
int dimen m3_comp_extended_fab_primary_focus_container_elevation 0x7f060110
int dimen m3_comp_extended_fab_primary_focus_state_layer_opacity 0x7f060111
int dimen m3_comp_extended_fab_primary_hover_container_elevation 0x7f060112
int dimen m3_comp_extended_fab_primary_hover_state_layer_opacity 0x7f060113
int dimen m3_comp_extended_fab_primary_icon_size 0x7f060114
int dimen m3_comp_extended_fab_primary_pressed_container_elevation 0x7f060115
int dimen m3_comp_extended_fab_primary_pressed_state_layer_opacity 0x7f060116
int dimen m3_comp_fab_primary_container_elevation 0x7f060117
int dimen m3_comp_fab_primary_container_height 0x7f060118
int dimen m3_comp_fab_primary_focus_state_layer_opacity 0x7f060119
int dimen m3_comp_fab_primary_hover_container_elevation 0x7f06011a
int dimen m3_comp_fab_primary_hover_state_layer_opacity 0x7f06011b
int dimen m3_comp_fab_primary_icon_size 0x7f06011c
int dimen m3_comp_fab_primary_large_container_height 0x7f06011d
int dimen m3_comp_fab_primary_large_icon_size 0x7f06011e
int dimen m3_comp_fab_primary_pressed_container_elevation 0x7f06011f
int dimen m3_comp_fab_primary_pressed_state_layer_opacity 0x7f060120
int dimen m3_comp_fab_primary_small_container_height 0x7f060121
int dimen m3_comp_fab_primary_small_icon_size 0x7f060122
int dimen m3_comp_filled_autocomplete_menu_container_elevation 0x7f060123
int dimen m3_comp_filled_button_container_elevation 0x7f060124
int dimen m3_comp_filled_button_with_icon_icon_size 0x7f060125
int dimen m3_comp_filled_card_container_elevation 0x7f060126
int dimen m3_comp_filled_card_dragged_state_layer_opacity 0x7f060127
int dimen m3_comp_filled_card_focus_state_layer_opacity 0x7f060128
int dimen m3_comp_filled_card_hover_state_layer_opacity 0x7f060129
int dimen m3_comp_filled_card_icon_size 0x7f06012a
int dimen m3_comp_filled_card_pressed_state_layer_opacity 0x7f06012b
int dimen m3_comp_filled_text_field_disabled_active_indicator_opacity 0x7f06012c
int dimen m3_comp_filter_chip_container_height 0x7f06012d
int dimen m3_comp_filter_chip_elevated_container_elevation 0x7f06012e
int dimen m3_comp_filter_chip_flat_container_elevation 0x7f06012f
int dimen m3_comp_filter_chip_flat_unselected_outline_width 0x7f060130
int dimen m3_comp_filter_chip_with_icon_icon_size 0x7f060131
int dimen m3_comp_input_chip_container_elevation 0x7f060132
int dimen m3_comp_input_chip_container_height 0x7f060133
int dimen m3_comp_input_chip_unselected_outline_width 0x7f060134
int dimen m3_comp_input_chip_with_avatar_avatar_size 0x7f060135
int dimen m3_comp_input_chip_with_leading_icon_leading_icon_size 0x7f060136
int dimen m3_comp_menu_container_elevation 0x7f060137
int dimen m3_comp_navigation_bar_active_indicator_height 0x7f060138
int dimen m3_comp_navigation_bar_active_indicator_width 0x7f060139
int dimen m3_comp_navigation_bar_container_elevation 0x7f06013a
int dimen m3_comp_navigation_bar_container_height 0x7f06013b
int dimen m3_comp_navigation_bar_focus_state_layer_opacity 0x7f06013c
int dimen m3_comp_navigation_bar_hover_state_layer_opacity 0x7f06013d
int dimen m3_comp_navigation_bar_icon_size 0x7f06013e
int dimen m3_comp_navigation_bar_pressed_state_layer_opacity 0x7f06013f
int dimen m3_comp_navigation_drawer_container_width 0x7f060140
int dimen m3_comp_navigation_drawer_focus_state_layer_opacity 0x7f060141
int dimen m3_comp_navigation_drawer_hover_state_layer_opacity 0x7f060142
int dimen m3_comp_navigation_drawer_icon_size 0x7f060143
int dimen m3_comp_navigation_drawer_modal_container_elevation 0x7f060144
int dimen m3_comp_navigation_drawer_pressed_state_layer_opacity 0x7f060145
int dimen m3_comp_navigation_drawer_standard_container_elevation 0x7f060146
int dimen m3_comp_navigation_rail_active_indicator_height 0x7f060147
int dimen m3_comp_navigation_rail_active_indicator_width 0x7f060148
int dimen m3_comp_navigation_rail_container_elevation 0x7f060149
int dimen m3_comp_navigation_rail_container_width 0x7f06014a
int dimen m3_comp_navigation_rail_focus_state_layer_opacity 0x7f06014b
int dimen m3_comp_navigation_rail_hover_state_layer_opacity 0x7f06014c
int dimen m3_comp_navigation_rail_icon_size 0x7f06014d
int dimen m3_comp_navigation_rail_pressed_state_layer_opacity 0x7f06014e
int dimen m3_comp_outlined_autocomplete_menu_container_elevation 0x7f06014f
int dimen m3_comp_outlined_button_disabled_outline_opacity 0x7f060150
int dimen m3_comp_outlined_button_outline_width 0x7f060151
int dimen m3_comp_outlined_card_container_elevation 0x7f060152
int dimen m3_comp_outlined_card_disabled_outline_opacity 0x7f060153
int dimen m3_comp_outlined_card_icon_size 0x7f060154
int dimen m3_comp_outlined_card_outline_width 0x7f060155
int dimen m3_comp_outlined_icon_button_unselected_outline_width 0x7f060156
int dimen m3_comp_outlined_text_field_disabled_input_text_opacity 0x7f060157
int dimen m3_comp_outlined_text_field_disabled_label_text_opacity 0x7f060158
int dimen m3_comp_outlined_text_field_disabled_supporting_text_opacity 0x7f060159
int dimen m3_comp_outlined_text_field_focus_outline_width 0x7f06015a
int dimen m3_comp_outlined_text_field_outline_width 0x7f06015b
int dimen m3_comp_primary_navigation_tab_active_focus_state_layer_opacity 0x7f06015c
int dimen m3_comp_primary_navigation_tab_active_hover_state_layer_opacity 0x7f06015d
int dimen m3_comp_primary_navigation_tab_active_indicator_height 0x7f06015e
int dimen m3_comp_primary_navigation_tab_active_pressed_state_layer_opacity 0x7f06015f
int dimen m3_comp_primary_navigation_tab_inactive_focus_state_layer_opacity 0x7f060160
int dimen m3_comp_primary_navigation_tab_inactive_hover_state_layer_opacity 0x7f060161
int dimen m3_comp_primary_navigation_tab_inactive_pressed_state_layer_opacity 0x7f060162
int dimen m3_comp_primary_navigation_tab_with_icon_icon_size 0x7f060163
int dimen m3_comp_progress_indicator_active_indicator_track_space 0x7f060164
int dimen m3_comp_progress_indicator_stop_indicator_size 0x7f060165
int dimen m3_comp_progress_indicator_track_thickness 0x7f060166
int dimen m3_comp_radio_button_disabled_selected_icon_opacity 0x7f060167
int dimen m3_comp_radio_button_disabled_unselected_icon_opacity 0x7f060168
int dimen m3_comp_radio_button_selected_focus_state_layer_opacity 0x7f060169
int dimen m3_comp_radio_button_selected_hover_state_layer_opacity 0x7f06016a
int dimen m3_comp_radio_button_selected_pressed_state_layer_opacity 0x7f06016b
int dimen m3_comp_radio_button_unselected_focus_state_layer_opacity 0x7f06016c
int dimen m3_comp_radio_button_unselected_hover_state_layer_opacity 0x7f06016d
int dimen m3_comp_radio_button_unselected_pressed_state_layer_opacity 0x7f06016e
int dimen m3_comp_scrim_container_opacity 0x7f06016f
int dimen m3_comp_search_bar_avatar_size 0x7f060170
int dimen m3_comp_search_bar_container_elevation 0x7f060171
int dimen m3_comp_search_bar_container_height 0x7f060172
int dimen m3_comp_search_bar_hover_state_layer_opacity 0x7f060173
int dimen m3_comp_search_bar_pressed_state_layer_opacity 0x7f060174
int dimen m3_comp_search_view_container_elevation 0x7f060175
int dimen m3_comp_search_view_docked_header_container_height 0x7f060176
int dimen m3_comp_search_view_full_screen_header_container_height 0x7f060177
int dimen m3_comp_secondary_navigation_tab_active_indicator_height 0x7f060178
int dimen m3_comp_secondary_navigation_tab_focus_state_layer_opacity 0x7f060179
int dimen m3_comp_secondary_navigation_tab_hover_state_layer_opacity 0x7f06017a
int dimen m3_comp_secondary_navigation_tab_pressed_state_layer_opacity 0x7f06017b
int dimen m3_comp_sheet_bottom_docked_drag_handle_height 0x7f06017c
int dimen m3_comp_sheet_bottom_docked_drag_handle_width 0x7f06017d
int dimen m3_comp_sheet_bottom_docked_modal_container_elevation 0x7f06017e
int dimen m3_comp_sheet_bottom_docked_standard_container_elevation 0x7f06017f
int dimen m3_comp_sheet_side_docked_container_width 0x7f060180
int dimen m3_comp_sheet_side_docked_modal_container_elevation 0x7f060181
int dimen m3_comp_sheet_side_docked_standard_container_elevation 0x7f060182
int dimen m3_comp_slider_active_handle_height 0x7f060183
int dimen m3_comp_slider_active_handle_leading_space 0x7f060184
int dimen m3_comp_slider_active_handle_width 0x7f060185
int dimen m3_comp_slider_disabled_active_track_opacity 0x7f060186
int dimen m3_comp_slider_disabled_handle_opacity 0x7f060187
int dimen m3_comp_slider_disabled_inactive_track_opacity 0x7f060188
int dimen m3_comp_slider_inactive_track_height 0x7f060189
int dimen m3_comp_slider_stop_indicator_size 0x7f06018a
int dimen m3_comp_snackbar_container_elevation 0x7f06018b
int dimen m3_comp_suggestion_chip_container_height 0x7f06018c
int dimen m3_comp_suggestion_chip_elevated_container_elevation 0x7f06018d
int dimen m3_comp_suggestion_chip_flat_container_elevation 0x7f06018e
int dimen m3_comp_suggestion_chip_flat_outline_width 0x7f06018f
int dimen m3_comp_suggestion_chip_with_leading_icon_leading_icon_size 0x7f060190
int dimen m3_comp_switch_disabled_selected_handle_opacity 0x7f060191
int dimen m3_comp_switch_disabled_selected_icon_opacity 0x7f060192
int dimen m3_comp_switch_disabled_track_opacity 0x7f060193
int dimen m3_comp_switch_disabled_unselected_handle_opacity 0x7f060194
int dimen m3_comp_switch_disabled_unselected_icon_opacity 0x7f060195
int dimen m3_comp_switch_selected_focus_state_layer_opacity 0x7f060196
int dimen m3_comp_switch_selected_hover_state_layer_opacity 0x7f060197
int dimen m3_comp_switch_selected_pressed_state_layer_opacity 0x7f060198
int dimen m3_comp_switch_track_height 0x7f060199
int dimen m3_comp_switch_track_width 0x7f06019a
int dimen m3_comp_switch_unselected_focus_state_layer_opacity 0x7f06019b
int dimen m3_comp_switch_unselected_hover_state_layer_opacity 0x7f06019c
int dimen m3_comp_switch_unselected_pressed_state_layer_opacity 0x7f06019d
int dimen m3_comp_text_button_focus_state_layer_opacity 0x7f06019e
int dimen m3_comp_text_button_hover_state_layer_opacity 0x7f06019f
int dimen m3_comp_text_button_pressed_state_layer_opacity 0x7f0601a0
int dimen m3_comp_time_input_time_input_field_focus_outline_width 0x7f0601a1
int dimen m3_comp_time_picker_container_elevation 0x7f0601a2
int dimen m3_comp_time_picker_period_selector_focus_state_layer_opacity 0x7f0601a3
int dimen m3_comp_time_picker_period_selector_hover_state_layer_opacity 0x7f0601a4
int dimen m3_comp_time_picker_period_selector_outline_width 0x7f0601a5
int dimen m3_comp_time_picker_period_selector_pressed_state_layer_opacity 0x7f0601a6
int dimen m3_comp_time_picker_time_selector_focus_state_layer_opacity 0x7f0601a7
int dimen m3_comp_time_picker_time_selector_hover_state_layer_opacity 0x7f0601a8
int dimen m3_comp_time_picker_time_selector_pressed_state_layer_opacity 0x7f0601a9
int dimen m3_comp_top_app_bar_large_container_height 0x7f0601aa
int dimen m3_comp_top_app_bar_medium_container_height 0x7f0601ab
int dimen m3_comp_top_app_bar_small_container_elevation 0x7f0601ac
int dimen m3_comp_top_app_bar_small_container_height 0x7f0601ad
int dimen m3_comp_top_app_bar_small_on_scroll_container_elevation 0x7f0601ae
int dimen m3_datepicker_elevation 0x7f0601af
int dimen m3_divider_heavy_thickness 0x7f0601b0
int dimen m3_extended_fab_bottom_padding 0x7f0601b1
int dimen m3_extended_fab_end_padding 0x7f0601b2
int dimen m3_extended_fab_icon_padding 0x7f0601b3
int dimen m3_extended_fab_min_height 0x7f0601b4
int dimen m3_extended_fab_start_padding 0x7f0601b5
int dimen m3_extended_fab_top_padding 0x7f0601b6
int dimen m3_fab_border_width 0x7f0601b7
int dimen m3_fab_corner_size 0x7f0601b8
int dimen m3_fab_translation_z_hovered_focused 0x7f0601b9
int dimen m3_fab_translation_z_pressed 0x7f0601ba
int dimen m3_large_fab_max_image_size 0x7f0601bb
int dimen m3_large_fab_size 0x7f0601bc
int dimen m3_large_text_vertical_offset_adjustment 0x7f0601bd
int dimen m3_menu_elevation 0x7f0601be
int dimen m3_nav_badge_with_text_vertical_offset 0x7f0601bf
int dimen m3_navigation_drawer_layout_corner_size 0x7f0601c0
int dimen m3_navigation_item_active_indicator_label_padding 0x7f0601c1
int dimen m3_navigation_item_horizontal_padding 0x7f0601c2
int dimen m3_navigation_item_icon_padding 0x7f0601c3
int dimen m3_navigation_item_shape_inset_bottom 0x7f0601c4
int dimen m3_navigation_item_shape_inset_end 0x7f0601c5
int dimen m3_navigation_item_shape_inset_start 0x7f0601c6
int dimen m3_navigation_item_shape_inset_top 0x7f0601c7
int dimen m3_navigation_item_vertical_padding 0x7f0601c8
int dimen m3_navigation_menu_divider_horizontal_padding 0x7f0601c9
int dimen m3_navigation_menu_headline_horizontal_padding 0x7f0601ca
int dimen m3_navigation_rail_default_width 0x7f0601cb
int dimen m3_navigation_rail_elevation 0x7f0601cc
int dimen m3_navigation_rail_icon_size 0x7f0601cd
int dimen m3_navigation_rail_item_active_indicator_height 0x7f0601ce
int dimen m3_navigation_rail_item_active_indicator_margin_horizontal 0x7f0601cf
int dimen m3_navigation_rail_item_active_indicator_width 0x7f0601d0
int dimen m3_navigation_rail_item_min_height 0x7f0601d1
int dimen m3_navigation_rail_item_padding_bottom 0x7f0601d2
int dimen m3_navigation_rail_item_padding_bottom_with_large_font 0x7f0601d3
int dimen m3_navigation_rail_item_padding_top 0x7f0601d4
int dimen m3_navigation_rail_item_padding_top_with_large_font 0x7f0601d5
int dimen m3_navigation_rail_label_padding_horizontal 0x7f0601d6
int dimen m3_ripple_default_alpha 0x7f0601d7
int dimen m3_ripple_focused_alpha 0x7f0601d8
int dimen m3_ripple_hovered_alpha 0x7f0601d9
int dimen m3_ripple_pressed_alpha 0x7f0601da
int dimen m3_ripple_selectable_pressed_alpha 0x7f0601db
int dimen m3_searchbar_elevation 0x7f0601dc
int dimen m3_searchbar_height 0x7f0601dd
int dimen m3_searchbar_margin_horizontal 0x7f0601de
int dimen m3_searchbar_margin_vertical 0x7f0601df
int dimen m3_searchbar_outlined_stroke_width 0x7f0601e0
int dimen m3_searchbar_padding_start 0x7f0601e1
int dimen m3_searchbar_text_margin_start_no_navigation_icon 0x7f0601e2
int dimen m3_searchbar_text_size 0x7f0601e3
int dimen m3_searchview_divider_size 0x7f0601e4
int dimen m3_searchview_elevation 0x7f0601e5
int dimen m3_searchview_height 0x7f0601e6
int dimen m3_side_sheet_margin_detached 0x7f0601e7
int dimen m3_side_sheet_modal_elevation 0x7f0601e8
int dimen m3_side_sheet_standard_elevation 0x7f0601e9
int dimen m3_side_sheet_width 0x7f0601ea
int dimen m3_simple_item_color_hovered_alpha 0x7f0601eb
int dimen m3_simple_item_color_selected_alpha 0x7f0601ec
int dimen m3_slider_thumb_elevation 0x7f0601ed
int dimen m3_small_fab_max_image_size 0x7f0601ee
int dimen m3_small_fab_size 0x7f0601ef
int dimen m3_snackbar_action_text_color_alpha 0x7f0601f0
int dimen m3_snackbar_margin 0x7f0601f1
int dimen m3_sys_elevation_level0 0x7f0601f2
int dimen m3_sys_elevation_level1 0x7f0601f3
int dimen m3_sys_elevation_level2 0x7f0601f4
int dimen m3_sys_elevation_level3 0x7f0601f5
int dimen m3_sys_elevation_level4 0x7f0601f6
int dimen m3_sys_elevation_level5 0x7f0601f7
int dimen m3_sys_motion_easing_emphasized_accelerate_control_x1 0x7f0601f8
int dimen m3_sys_motion_easing_emphasized_accelerate_control_x2 0x7f0601f9
int dimen m3_sys_motion_easing_emphasized_accelerate_control_y1 0x7f0601fa
int dimen m3_sys_motion_easing_emphasized_accelerate_control_y2 0x7f0601fb
int dimen m3_sys_motion_easing_emphasized_decelerate_control_x1 0x7f0601fc
int dimen m3_sys_motion_easing_emphasized_decelerate_control_x2 0x7f0601fd
int dimen m3_sys_motion_easing_emphasized_decelerate_control_y1 0x7f0601fe
int dimen m3_sys_motion_easing_emphasized_decelerate_control_y2 0x7f0601ff
int dimen m3_sys_motion_easing_legacy_accelerate_control_x1 0x7f060200
int dimen m3_sys_motion_easing_legacy_accelerate_control_x2 0x7f060201
int dimen m3_sys_motion_easing_legacy_accelerate_control_y1 0x7f060202
int dimen m3_sys_motion_easing_legacy_accelerate_control_y2 0x7f060203
int dimen m3_sys_motion_easing_legacy_control_x1 0x7f060204
int dimen m3_sys_motion_easing_legacy_control_x2 0x7f060205
int dimen m3_sys_motion_easing_legacy_control_y1 0x7f060206
int dimen m3_sys_motion_easing_legacy_control_y2 0x7f060207
int dimen m3_sys_motion_easing_legacy_decelerate_control_x1 0x7f060208
int dimen m3_sys_motion_easing_legacy_decelerate_control_x2 0x7f060209
int dimen m3_sys_motion_easing_legacy_decelerate_control_y1 0x7f06020a
int dimen m3_sys_motion_easing_legacy_decelerate_control_y2 0x7f06020b
int dimen m3_sys_motion_easing_linear_control_x1 0x7f06020c
int dimen m3_sys_motion_easing_linear_control_x2 0x7f06020d
int dimen m3_sys_motion_easing_linear_control_y1 0x7f06020e
int dimen m3_sys_motion_easing_linear_control_y2 0x7f06020f
int dimen m3_sys_motion_easing_standard_accelerate_control_x1 0x7f060210
int dimen m3_sys_motion_easing_standard_accelerate_control_x2 0x7f060211
int dimen m3_sys_motion_easing_standard_accelerate_control_y1 0x7f060212
int dimen m3_sys_motion_easing_standard_accelerate_control_y2 0x7f060213
int dimen m3_sys_motion_easing_standard_control_x1 0x7f060214
int dimen m3_sys_motion_easing_standard_control_x2 0x7f060215
int dimen m3_sys_motion_easing_standard_control_y1 0x7f060216
int dimen m3_sys_motion_easing_standard_control_y2 0x7f060217
int dimen m3_sys_motion_easing_standard_decelerate_control_x1 0x7f060218
int dimen m3_sys_motion_easing_standard_decelerate_control_x2 0x7f060219
int dimen m3_sys_motion_easing_standard_decelerate_control_y1 0x7f06021a
int dimen m3_sys_motion_easing_standard_decelerate_control_y2 0x7f06021b
int dimen m3_sys_state_dragged_state_layer_opacity 0x7f06021c
int dimen m3_sys_state_focus_state_layer_opacity 0x7f06021d
int dimen m3_sys_state_hover_state_layer_opacity 0x7f06021e
int dimen m3_sys_state_pressed_state_layer_opacity 0x7f06021f
int dimen m3_timepicker_display_stroke_width 0x7f060220
int dimen m3_timepicker_window_elevation 0x7f060221
int dimen m3_toolbar_text_size_title 0x7f060222
int dimen material_bottom_sheet_max_width 0x7f060223
int dimen material_clock_display_height 0x7f060224
int dimen material_clock_display_padding 0x7f060225
int dimen material_clock_display_width 0x7f060226
int dimen material_clock_face_margin_bottom 0x7f060227
int dimen material_clock_face_margin_top 0x7f060228
int dimen material_clock_hand_center_dot_radius 0x7f060229
int dimen material_clock_hand_padding 0x7f06022a
int dimen material_clock_hand_stroke_width 0x7f06022b
int dimen material_clock_number_text_size 0x7f06022c
int dimen material_clock_period_toggle_height 0x7f06022d
int dimen material_clock_period_toggle_horizontal_gap 0x7f06022e
int dimen material_clock_period_toggle_vertical_gap 0x7f06022f
int dimen material_clock_period_toggle_width 0x7f060230
int dimen material_clock_size 0x7f060231
int dimen material_cursor_inset 0x7f060232
int dimen material_cursor_width 0x7f060233
int dimen material_divider_thickness 0x7f060234
int dimen material_emphasis_disabled 0x7f060235
int dimen material_emphasis_disabled_background 0x7f060236
int dimen material_emphasis_high_type 0x7f060237
int dimen material_emphasis_medium 0x7f060238
int dimen material_filled_edittext_font_1_3_padding_bottom 0x7f060239
int dimen material_filled_edittext_font_1_3_padding_top 0x7f06023a
int dimen material_filled_edittext_font_2_0_padding_bottom 0x7f06023b
int dimen material_filled_edittext_font_2_0_padding_top 0x7f06023c
int dimen material_font_1_3_box_collapsed_padding_top 0x7f06023d
int dimen material_font_2_0_box_collapsed_padding_top 0x7f06023e
int dimen material_helper_text_default_padding_top 0x7f06023f
int dimen material_helper_text_font_1_3_padding_horizontal 0x7f060240
int dimen material_helper_text_font_1_3_padding_top 0x7f060241
int dimen material_input_text_to_prefix_suffix_padding 0x7f060242
int dimen material_textinput_default_width 0x7f060243
int dimen material_textinput_max_width 0x7f060244
int dimen material_textinput_min_width 0x7f060245
int dimen material_time_picker_minimum_screen_height 0x7f060246
int dimen material_time_picker_minimum_screen_width 0x7f060247
int dimen mtrl_alert_dialog_background_inset_bottom 0x7f060248
int dimen mtrl_alert_dialog_background_inset_end 0x7f060249
int dimen mtrl_alert_dialog_background_inset_start 0x7f06024a
int dimen mtrl_alert_dialog_background_inset_top 0x7f06024b
int dimen mtrl_alert_dialog_picker_background_inset 0x7f06024c
int dimen mtrl_badge_horizontal_edge_offset 0x7f06024d
int dimen mtrl_badge_long_text_horizontal_padding 0x7f06024e
int dimen mtrl_badge_size 0x7f06024f
int dimen mtrl_badge_text_horizontal_edge_offset 0x7f060250
int dimen mtrl_badge_text_size 0x7f060251
int dimen mtrl_badge_toolbar_action_menu_item_horizontal_offset 0x7f060252
int dimen mtrl_badge_toolbar_action_menu_item_vertical_offset 0x7f060253
int dimen mtrl_badge_with_text_size 0x7f060254
int dimen mtrl_bottomappbar_fabOffsetEndMode 0x7f060255
int dimen mtrl_bottomappbar_fab_bottom_margin 0x7f060256
int dimen mtrl_bottomappbar_fab_cradle_margin 0x7f060257
int dimen mtrl_bottomappbar_fab_cradle_rounded_corner_radius 0x7f060258
int dimen mtrl_bottomappbar_fab_cradle_vertical_offset 0x7f060259
int dimen mtrl_bottomappbar_height 0x7f06025a
int dimen mtrl_btn_corner_radius 0x7f06025b
int dimen mtrl_btn_dialog_btn_min_width 0x7f06025c
int dimen mtrl_btn_disabled_elevation 0x7f06025d
int dimen mtrl_btn_disabled_z 0x7f06025e
int dimen mtrl_btn_elevation 0x7f06025f
int dimen mtrl_btn_focused_z 0x7f060260
int dimen mtrl_btn_hovered_z 0x7f060261
int dimen mtrl_btn_icon_btn_padding_left 0x7f060262
int dimen mtrl_btn_icon_padding 0x7f060263
int dimen mtrl_btn_inset 0x7f060264
int dimen mtrl_btn_letter_spacing 0x7f060265
int dimen mtrl_btn_max_width 0x7f060266
int dimen mtrl_btn_padding_bottom 0x7f060267
int dimen mtrl_btn_padding_left 0x7f060268
int dimen mtrl_btn_padding_right 0x7f060269
int dimen mtrl_btn_padding_top 0x7f06026a
int dimen mtrl_btn_pressed_z 0x7f06026b
int dimen mtrl_btn_snackbar_margin_horizontal 0x7f06026c
int dimen mtrl_btn_stroke_size 0x7f06026d
int dimen mtrl_btn_text_btn_icon_padding 0x7f06026e
int dimen mtrl_btn_text_btn_padding_left 0x7f06026f
int dimen mtrl_btn_text_btn_padding_right 0x7f060270
int dimen mtrl_btn_text_size 0x7f060271
int dimen mtrl_btn_z 0x7f060272
int dimen mtrl_calendar_action_confirm_button_min_width 0x7f060273
int dimen mtrl_calendar_action_height 0x7f060274
int dimen mtrl_calendar_action_padding 0x7f060275
int dimen mtrl_calendar_bottom_padding 0x7f060276
int dimen mtrl_calendar_content_padding 0x7f060277
int dimen mtrl_calendar_day_corner 0x7f060278
int dimen mtrl_calendar_day_height 0x7f060279
int dimen mtrl_calendar_day_horizontal_padding 0x7f06027a
int dimen mtrl_calendar_day_today_stroke 0x7f06027b
int dimen mtrl_calendar_day_vertical_padding 0x7f06027c
int dimen mtrl_calendar_day_width 0x7f06027d
int dimen mtrl_calendar_days_of_week_height 0x7f06027e
int dimen mtrl_calendar_dialog_background_inset 0x7f06027f
int dimen mtrl_calendar_header_content_padding 0x7f060280
int dimen mtrl_calendar_header_content_padding_fullscreen 0x7f060281
int dimen mtrl_calendar_header_divider_thickness 0x7f060282
int dimen mtrl_calendar_header_height 0x7f060283
int dimen mtrl_calendar_header_height_fullscreen 0x7f060284
int dimen mtrl_calendar_header_selection_line_height 0x7f060285
int dimen mtrl_calendar_header_text_padding 0x7f060286
int dimen mtrl_calendar_header_toggle_margin_bottom 0x7f060287
int dimen mtrl_calendar_header_toggle_margin_top 0x7f060288
int dimen mtrl_calendar_landscape_header_width 0x7f060289
int dimen mtrl_calendar_maximum_default_fullscreen_minor_axis 0x7f06028a
int dimen mtrl_calendar_month_horizontal_padding 0x7f06028b
int dimen mtrl_calendar_month_vertical_padding 0x7f06028c
int dimen mtrl_calendar_navigation_bottom_padding 0x7f06028d
int dimen mtrl_calendar_navigation_height 0x7f06028e
int dimen mtrl_calendar_navigation_top_padding 0x7f06028f
int dimen mtrl_calendar_pre_l_text_clip_padding 0x7f060290
int dimen mtrl_calendar_selection_baseline_to_top_fullscreen 0x7f060291
int dimen mtrl_calendar_selection_text_baseline_to_bottom 0x7f060292
int dimen mtrl_calendar_selection_text_baseline_to_bottom_fullscreen 0x7f060293
int dimen mtrl_calendar_selection_text_baseline_to_top 0x7f060294
int dimen mtrl_calendar_text_input_padding_top 0x7f060295
int dimen mtrl_calendar_title_baseline_to_top 0x7f060296
int dimen mtrl_calendar_title_baseline_to_top_fullscreen 0x7f060297
int dimen mtrl_calendar_year_corner 0x7f060298
int dimen mtrl_calendar_year_height 0x7f060299
int dimen mtrl_calendar_year_horizontal_padding 0x7f06029a
int dimen mtrl_calendar_year_vertical_padding 0x7f06029b
int dimen mtrl_calendar_year_width 0x7f06029c
int dimen mtrl_card_checked_icon_margin 0x7f06029d
int dimen mtrl_card_checked_icon_size 0x7f06029e
int dimen mtrl_card_corner_radius 0x7f06029f
int dimen mtrl_card_dragged_z 0x7f0602a0
int dimen mtrl_card_elevation 0x7f0602a1
int dimen mtrl_card_spacing 0x7f0602a2
int dimen mtrl_chip_pressed_translation_z 0x7f0602a3
int dimen mtrl_chip_text_size 0x7f0602a4
int dimen mtrl_exposed_dropdown_menu_popup_elevation 0x7f0602a5
int dimen mtrl_exposed_dropdown_menu_popup_vertical_offset 0x7f0602a6
int dimen mtrl_exposed_dropdown_menu_popup_vertical_padding 0x7f0602a7
int dimen mtrl_extended_fab_bottom_padding 0x7f0602a8
int dimen mtrl_extended_fab_disabled_elevation 0x7f0602a9
int dimen mtrl_extended_fab_disabled_translation_z 0x7f0602aa
int dimen mtrl_extended_fab_elevation 0x7f0602ab
int dimen mtrl_extended_fab_end_padding 0x7f0602ac
int dimen mtrl_extended_fab_end_padding_icon 0x7f0602ad
int dimen mtrl_extended_fab_icon_size 0x7f0602ae
int dimen mtrl_extended_fab_icon_text_spacing 0x7f0602af
int dimen mtrl_extended_fab_min_height 0x7f0602b0
int dimen mtrl_extended_fab_min_width 0x7f0602b1
int dimen mtrl_extended_fab_start_padding 0x7f0602b2
int dimen mtrl_extended_fab_start_padding_icon 0x7f0602b3
int dimen mtrl_extended_fab_top_padding 0x7f0602b4
int dimen mtrl_extended_fab_translation_z_base 0x7f0602b5
int dimen mtrl_extended_fab_translation_z_hovered_focused 0x7f0602b6
int dimen mtrl_extended_fab_translation_z_pressed 0x7f0602b7
int dimen mtrl_fab_elevation 0x7f0602b8
int dimen mtrl_fab_min_touch_target 0x7f0602b9
int dimen mtrl_fab_translation_z_hovered_focused 0x7f0602ba
int dimen mtrl_fab_translation_z_pressed 0x7f0602bb
int dimen mtrl_high_ripple_default_alpha 0x7f0602bc
int dimen mtrl_high_ripple_focused_alpha 0x7f0602bd
int dimen mtrl_high_ripple_hovered_alpha 0x7f0602be
int dimen mtrl_high_ripple_pressed_alpha 0x7f0602bf
int dimen mtrl_low_ripple_default_alpha 0x7f0602c0
int dimen mtrl_low_ripple_focused_alpha 0x7f0602c1
int dimen mtrl_low_ripple_hovered_alpha 0x7f0602c2
int dimen mtrl_low_ripple_pressed_alpha 0x7f0602c3
int dimen mtrl_min_touch_target_size 0x7f0602c4
int dimen mtrl_navigation_bar_item_default_icon_size 0x7f0602c5
int dimen mtrl_navigation_bar_item_default_margin 0x7f0602c6
int dimen mtrl_navigation_elevation 0x7f0602c7
int dimen mtrl_navigation_item_horizontal_padding 0x7f0602c8
int dimen mtrl_navigation_item_icon_padding 0x7f0602c9
int dimen mtrl_navigation_item_icon_size 0x7f0602ca
int dimen mtrl_navigation_item_shape_horizontal_margin 0x7f0602cb
int dimen mtrl_navigation_item_shape_vertical_margin 0x7f0602cc
int dimen mtrl_navigation_rail_active_text_size 0x7f0602cd
int dimen mtrl_navigation_rail_compact_width 0x7f0602ce
int dimen mtrl_navigation_rail_default_width 0x7f0602cf
int dimen mtrl_navigation_rail_elevation 0x7f0602d0
int dimen mtrl_navigation_rail_icon_margin 0x7f0602d1
int dimen mtrl_navigation_rail_icon_size 0x7f0602d2
int dimen mtrl_navigation_rail_margin 0x7f0602d3
int dimen mtrl_navigation_rail_text_bottom_margin 0x7f0602d4
int dimen mtrl_navigation_rail_text_size 0x7f0602d5
int dimen mtrl_progress_circular_inset 0x7f0602d6
int dimen mtrl_progress_circular_inset_extra_small 0x7f0602d7
int dimen mtrl_progress_circular_inset_medium 0x7f0602d8
int dimen mtrl_progress_circular_inset_small 0x7f0602d9
int dimen mtrl_progress_circular_radius 0x7f0602da
int dimen mtrl_progress_circular_size 0x7f0602db
int dimen mtrl_progress_circular_size_extra_small 0x7f0602dc
int dimen mtrl_progress_circular_size_medium 0x7f0602dd
int dimen mtrl_progress_circular_size_small 0x7f0602de
int dimen mtrl_progress_circular_track_thickness_extra_small 0x7f0602df
int dimen mtrl_progress_circular_track_thickness_medium 0x7f0602e0
int dimen mtrl_progress_circular_track_thickness_small 0x7f0602e1
int dimen mtrl_progress_indicator_full_rounded_corner_radius 0x7f0602e2
int dimen mtrl_progress_track_thickness 0x7f0602e3
int dimen mtrl_shape_corner_size_large_component 0x7f0602e4
int dimen mtrl_shape_corner_size_medium_component 0x7f0602e5
int dimen mtrl_shape_corner_size_small_component 0x7f0602e6
int dimen mtrl_slider_halo_radius 0x7f0602e7
int dimen mtrl_slider_label_padding 0x7f0602e8
int dimen mtrl_slider_label_radius 0x7f0602e9
int dimen mtrl_slider_label_square_side 0x7f0602ea
int dimen mtrl_slider_thumb_elevation 0x7f0602eb
int dimen mtrl_slider_thumb_radius 0x7f0602ec
int dimen mtrl_slider_tick_min_spacing 0x7f0602ed
int dimen mtrl_slider_tick_radius 0x7f0602ee
int dimen mtrl_slider_track_height 0x7f0602ef
int dimen mtrl_slider_track_side_padding 0x7f0602f0
int dimen mtrl_slider_widget_height 0x7f0602f1
int dimen mtrl_snackbar_action_text_color_alpha 0x7f0602f2
int dimen mtrl_snackbar_background_corner_radius 0x7f0602f3
int dimen mtrl_snackbar_background_overlay_color_alpha 0x7f0602f4
int dimen mtrl_snackbar_margin 0x7f0602f5
int dimen mtrl_snackbar_message_margin_horizontal 0x7f0602f6
int dimen mtrl_snackbar_padding_horizontal 0x7f0602f7
int dimen mtrl_switch_text_padding 0x7f0602f8
int dimen mtrl_switch_thumb_elevation 0x7f0602f9
int dimen mtrl_switch_thumb_icon_size 0x7f0602fa
int dimen mtrl_switch_thumb_size 0x7f0602fb
int dimen mtrl_switch_track_height 0x7f0602fc
int dimen mtrl_switch_track_width 0x7f0602fd
int dimen mtrl_textinput_box_corner_radius_medium 0x7f0602fe
int dimen mtrl_textinput_box_corner_radius_small 0x7f0602ff
int dimen mtrl_textinput_box_label_cutout_padding 0x7f060300
int dimen mtrl_textinput_box_stroke_width_default 0x7f060301
int dimen mtrl_textinput_box_stroke_width_focused 0x7f060302
int dimen mtrl_textinput_counter_margin_start 0x7f060303
int dimen mtrl_textinput_end_icon_margin_start 0x7f060304
int dimen mtrl_textinput_outline_box_expanded_padding 0x7f060305
int dimen mtrl_textinput_start_icon_margin_end 0x7f060306
int dimen mtrl_toolbar_default_height 0x7f060307
int dimen mtrl_tooltip_arrowSize 0x7f060308
int dimen mtrl_tooltip_cornerSize 0x7f060309
int dimen mtrl_tooltip_minHeight 0x7f06030a
int dimen mtrl_tooltip_minWidth 0x7f06030b
int dimen mtrl_tooltip_padding 0x7f06030c
int dimen mtrl_transition_shared_axis_slide_distance 0x7f06030d
int dimen notification_action_icon_size 0x7f06030e
int dimen notification_action_text_size 0x7f06030f
int dimen notification_big_circle_margin 0x7f060310
int dimen notification_content_margin_start 0x7f060311
int dimen notification_large_icon_height 0x7f060312
int dimen notification_large_icon_width 0x7f060313
int dimen notification_main_column_padding_top 0x7f060314
int dimen notification_media_narrow_margin 0x7f060315
int dimen notification_right_icon_size 0x7f060316
int dimen notification_right_side_padding_top 0x7f060317
int dimen notification_small_icon_background_padding 0x7f060318
int dimen notification_small_icon_size_as_large 0x7f060319
int dimen notification_subtext_size 0x7f06031a
int dimen notification_top_pad 0x7f06031b
int dimen notification_top_pad_large_text 0x7f06031c
int dimen pay_button_generic_min_height 0x7f06031d
int dimen pay_button_generic_min_width 0x7f06031e
int dimen pay_image_generic_height 0x7f06031f
int dimen pay_image_generic_width 0x7f060320
int dimen preference_dropdown_padding_start 0x7f060321
int dimen preference_icon_minWidth 0x7f060322
int dimen preference_seekbar_padding_horizontal 0x7f060323
int dimen preference_seekbar_padding_vertical 0x7f060324
int dimen preference_seekbar_value_minWidth 0x7f060325
int dimen preferences_detail_width 0x7f060326
int dimen preferences_header_width 0x7f060327
int dimen stripe_3ds2_brand_zone_horizontal_margin 0x7f060328
int dimen stripe_3ds2_brand_zone_max_height 0x7f060329
int dimen stripe_3ds2_challenge_activity_padding 0x7f06032a
int dimen stripe_3ds2_challenge_zone_select_button_label_padding 0x7f06032b
int dimen stripe_3ds2_challenge_zone_select_button_min_height 0x7f06032c
int dimen stripe_3ds2_challenge_zone_select_button_offset_margin 0x7f06032d
int dimen stripe_3ds2_challenge_zone_select_button_vertical_margin 0x7f06032e
int dimen stripe_3ds2_challenge_zone_text_indicator_padding 0x7f06032f
int dimen stripe_3ds2_challenge_zone_vertical_padding 0x7f060330
int dimen stripe_3ds2_divider 0x7f060331
int dimen stripe_3ds2_information_zone_label_padding 0x7f060332
int dimen stripe_3ds2_information_zone_vertical_padding 0x7f060333
int dimen stripe_activity_total_margin 0x7f060334
int dimen stripe_add_address_vertical_margin 0x7f060335
int dimen stripe_add_card_element_vertical_margin 0x7f060336
int dimen stripe_add_card_expiry_middle_margin 0x7f060337
int dimen stripe_add_card_total_margin 0x7f060338
int dimen stripe_add_payment_method_vertical_padding 0x7f060339
int dimen stripe_becs_debit_widget_edit_text_size 0x7f06033a
int dimen stripe_becs_debit_widget_mandate_acceptance_padding_top 0x7f06033b
int dimen stripe_card_brand_spinner_dropdown_drawable_padding 0x7f06033c
int dimen stripe_card_brand_spinner_dropdown_padding 0x7f06033d
int dimen stripe_card_brand_spinner_dropdown_width 0x7f06033e
int dimen stripe_card_brand_spinner_image_height 0x7f06033f
int dimen stripe_card_brand_spinner_image_width 0x7f060340
int dimen stripe_card_brand_view_height 0x7f060341
int dimen stripe_card_brand_view_width 0x7f060342
int dimen stripe_card_cvc_initial_margin 0x7f060343
int dimen stripe_card_expiry_initial_margin 0x7f060344
int dimen stripe_card_form_view_card_elevation 0x7f060345
int dimen stripe_card_form_view_error_text_margin_horizontal 0x7f060346
int dimen stripe_card_form_view_error_textsize 0x7f060347
int dimen stripe_card_form_view_text_input_layout_padding_horizontal 0x7f060348
int dimen stripe_card_form_view_text_input_layout_padding_vertical 0x7f060349
int dimen stripe_card_form_view_text_margin_horizontal 0x7f06034a
int dimen stripe_card_form_view_text_margin_vertical 0x7f06034b
int dimen stripe_card_form_view_text_minheight 0x7f06034c
int dimen stripe_card_form_view_textsize 0x7f06034d
int dimen stripe_card_icon_multiline_padding 0x7f06034e
int dimen stripe_card_icon_padding 0x7f06034f
int dimen stripe_card_number_text_input_layout_progress_end_margin 0x7f060350
int dimen stripe_card_number_text_input_layout_progress_top_margin 0x7f060351
int dimen stripe_card_widget_min_width 0x7f060352
int dimen stripe_card_widget_progress_size 0x7f060353
int dimen stripe_ciw_stripe_edit_text_size 0x7f060354
int dimen stripe_cmw_edit_text_minheight 0x7f060355
int dimen stripe_list_row_end_padding 0x7f060356
int dimen stripe_list_row_height 0x7f060357
int dimen stripe_list_row_start_padding 0x7f060358
int dimen stripe_list_top_margin 0x7f060359
int dimen stripe_masked_card_icon_height 0x7f06035a
int dimen stripe_masked_card_icon_width 0x7f06035b
int dimen stripe_paymentsheet_add_payment_method_form_stroke_width 0x7f06035c
int dimen stripe_paymentsheet_add_pm_card_elevation 0x7f06035d
int dimen stripe_paymentsheet_add_pm_card_elevation_selected 0x7f06035e
int dimen stripe_paymentsheet_add_pm_card_height 0x7f06035f
int dimen stripe_paymentsheet_add_pm_card_stroke_width 0x7f060360
int dimen stripe_paymentsheet_add_pm_card_stroke_width_selected 0x7f060361
int dimen stripe_paymentsheet_add_pm_card_width 0x7f060362
int dimen stripe_paymentsheet_button_container_spacing 0x7f060363
int dimen stripe_paymentsheet_button_container_spacing_bottom 0x7f060364
int dimen stripe_paymentsheet_card_elevation 0x7f060365
int dimen stripe_paymentsheet_card_stroke_width 0x7f060366
int dimen stripe_paymentsheet_card_stroke_width_selected 0x7f060367
int dimen stripe_paymentsheet_cardwidget_margin_horizontal 0x7f060368
int dimen stripe_paymentsheet_cardwidget_margin_vertical 0x7f060369
int dimen stripe_paymentsheet_error_textsize 0x7f06036a
int dimen stripe_paymentsheet_form_textsize 0x7f06036b
int dimen stripe_paymentsheet_googlepay_button_height 0x7f06036c
int dimen stripe_paymentsheet_googlepay_button_margin 0x7f06036d
int dimen stripe_paymentsheet_loading_container_height 0x7f06036e
int dimen stripe_paymentsheet_loading_indicator_size 0x7f06036f
int dimen stripe_paymentsheet_loading_indicator_stroke_width 0x7f060370
int dimen stripe_paymentsheet_max_primary_button_height 0x7f060371
int dimen stripe_paymentsheet_minimum_tap_size 0x7f060372
int dimen stripe_paymentsheet_outer_spacing_horizontal 0x7f060373
int dimen stripe_paymentsheet_outer_spacing_top 0x7f060374
int dimen stripe_paymentsheet_paymentmethod_icon_height 0x7f060375
int dimen stripe_paymentsheet_paymentmethod_icon_width 0x7f060376
int dimen stripe_paymentsheet_paymentoption_card_height 0x7f060377
int dimen stripe_paymentsheet_paymentoption_card_width 0x7f060378
int dimen stripe_paymentsheet_paymentoptions_margin_bottom 0x7f060379
int dimen stripe_paymentsheet_paymentoptions_margin_top 0x7f06037a
int dimen stripe_paymentsheet_primary_button_height 0x7f06037b
int dimen stripe_paymentsheet_primary_button_icon_padding 0x7f06037c
int dimen stripe_paymentsheet_primary_button_icon_size 0x7f06037d
int dimen stripe_paymentsheet_primary_button_padding 0x7f06037e
int dimen stripe_paymentsheet_toolbar_elevation 0x7f06037f
int dimen stripe_shipping_check_icon_width 0x7f060380
int dimen stripe_shipping_widget_horizontal_margin 0x7f060381
int dimen stripe_shipping_widget_outer_margin 0x7f060382
int dimen stripe_shipping_widget_vertical_margin 0x7f060383
int dimen stripe_toolbar_elevation 0x7f060384
int dimen subtitle_corner_radius 0x7f060385
int dimen subtitle_outline_width 0x7f060386
int dimen subtitle_shadow_offset 0x7f060387
int dimen subtitle_shadow_radius 0x7f060388
int dimen tooltip_corner_radius 0x7f060389
int dimen tooltip_horizontal_padding 0x7f06038a
int dimen tooltip_margin 0x7f06038b
int dimen tooltip_precise_anchor_extra_offset 0x7f06038c
int dimen tooltip_precise_anchor_threshold 0x7f06038d
int dimen tooltip_vertical_padding 0x7f06038e
int dimen tooltip_y_offset_non_touch 0x7f06038f
int dimen tooltip_y_offset_touch 0x7f060390
int drawable abc_ab_share_pack_mtrl_alpha 0x7f070039
int drawable abc_action_bar_item_background_material 0x7f07003a
int drawable abc_btn_borderless_material 0x7f07003b
int drawable abc_btn_check_material 0x7f07003c
int drawable abc_btn_check_material_anim 0x7f07003d
int drawable abc_btn_check_to_on_mtrl_000 0x7f07003e
int drawable abc_btn_check_to_on_mtrl_015 0x7f07003f
int drawable abc_btn_colored_material 0x7f070040
int drawable abc_btn_default_mtrl_shape 0x7f070041
int drawable abc_btn_radio_material 0x7f070042
int drawable abc_btn_radio_material_anim 0x7f070043
int drawable abc_btn_radio_to_on_mtrl_000 0x7f070044
int drawable abc_btn_radio_to_on_mtrl_015 0x7f070045
int drawable abc_btn_switch_to_on_mtrl_00001 0x7f070046
int drawable abc_btn_switch_to_on_mtrl_00012 0x7f070047
int drawable abc_cab_background_internal_bg 0x7f070048
int drawable abc_cab_background_top_material 0x7f070049
int drawable abc_cab_background_top_mtrl_alpha 0x7f07004a
int drawable abc_control_background_material 0x7f07004b
int drawable abc_dialog_material_background 0x7f07004c
int drawable abc_edit_text_material 0x7f07004d
int drawable abc_ic_ab_back_material 0x7f07004e
int drawable abc_ic_arrow_drop_right_black_24dp 0x7f07004f
int drawable abc_ic_clear_material 0x7f070050
int drawable abc_ic_commit_search_api_mtrl_alpha 0x7f070051
int drawable abc_ic_go_search_api_material 0x7f070052
int drawable abc_ic_menu_copy_mtrl_am_alpha 0x7f070053
int drawable abc_ic_menu_cut_mtrl_alpha 0x7f070054
int drawable abc_ic_menu_overflow_material 0x7f070055
int drawable abc_ic_menu_paste_mtrl_am_alpha 0x7f070056
int drawable abc_ic_menu_selectall_mtrl_alpha 0x7f070057
int drawable abc_ic_menu_share_mtrl_alpha 0x7f070058
int drawable abc_ic_search_api_material 0x7f070059
int drawable abc_ic_voice_search_api_material 0x7f07005a
int drawable abc_item_background_holo_dark 0x7f07005b
int drawable abc_item_background_holo_light 0x7f07005c
int drawable abc_list_divider_material 0x7f07005d
int drawable abc_list_divider_mtrl_alpha 0x7f07005e
int drawable abc_list_focused_holo 0x7f07005f
int drawable abc_list_longpressed_holo 0x7f070060
int drawable abc_list_pressed_holo_dark 0x7f070061
int drawable abc_list_pressed_holo_light 0x7f070062
int drawable abc_list_selector_background_transition_holo_dark 0x7f070063
int drawable abc_list_selector_background_transition_holo_light 0x7f070064
int drawable abc_list_selector_disabled_holo_dark 0x7f070065
int drawable abc_list_selector_disabled_holo_light 0x7f070066
int drawable abc_list_selector_holo_dark 0x7f070067
int drawable abc_list_selector_holo_light 0x7f070068
int drawable abc_menu_hardkey_panel_mtrl_mult 0x7f070069
int drawable abc_popup_background_mtrl_mult 0x7f07006a
int drawable abc_ratingbar_indicator_material 0x7f07006b
int drawable abc_ratingbar_material 0x7f07006c
int drawable abc_ratingbar_small_material 0x7f07006d
int drawable abc_scrubber_control_off_mtrl_alpha 0x7f07006e
int drawable abc_scrubber_control_to_pressed_mtrl_000 0x7f07006f
int drawable abc_scrubber_control_to_pressed_mtrl_005 0x7f070070
int drawable abc_scrubber_primary_mtrl_alpha 0x7f070071
int drawable abc_scrubber_track_mtrl_alpha 0x7f070072
int drawable abc_seekbar_thumb_material 0x7f070073
int drawable abc_seekbar_tick_mark_material 0x7f070074
int drawable abc_seekbar_track_material 0x7f070075
int drawable abc_spinner_mtrl_am_alpha 0x7f070076
int drawable abc_spinner_textfield_background_material 0x7f070077
int drawable abc_star_black_48dp 0x7f070078
int drawable abc_star_half_black_48dp 0x7f070079
int drawable abc_switch_thumb_material 0x7f07007a
int drawable abc_switch_track_mtrl_alpha 0x7f07007b
int drawable abc_tab_indicator_material 0x7f07007c
int drawable abc_tab_indicator_mtrl_alpha 0x7f07007d
int drawable abc_text_cursor_material 0x7f07007e
int drawable abc_text_select_handle_left_mtrl 0x7f07007f
int drawable abc_text_select_handle_middle_mtrl 0x7f070080
int drawable abc_text_select_handle_right_mtrl 0x7f070081
int drawable abc_textfield_activated_mtrl_alpha 0x7f070082
int drawable abc_textfield_default_mtrl_alpha 0x7f070083
int drawable abc_textfield_search_activated_mtrl_alpha 0x7f070084
int drawable abc_textfield_search_default_mtrl_alpha 0x7f070085
int drawable abc_textfield_search_material 0x7f070086
int drawable abc_vector_test 0x7f070087
int drawable avd_hide_password 0x7f070088
int drawable avd_show_password 0x7f070089
int drawable btn_checkbox_checked_mtrl 0x7f07008a
int drawable btn_checkbox_checked_to_unchecked_mtrl_animation 0x7f07008b
int drawable btn_checkbox_unchecked_mtrl 0x7f07008c
int drawable btn_checkbox_unchecked_to_checked_mtrl_animation 0x7f07008d
int drawable btn_radio_off_mtrl 0x7f07008e
int drawable btn_radio_off_to_on_mtrl_animation 0x7f07008f
int drawable btn_radio_on_mtrl 0x7f070090
int drawable btn_radio_on_to_off_mtrl_animation 0x7f070091
int drawable common_full_open_on_phone 0x7f070092
int drawable common_google_signin_btn_icon_dark 0x7f070093
int drawable common_google_signin_btn_icon_dark_focused 0x7f070094
int drawable common_google_signin_btn_icon_dark_normal 0x7f070095
int drawable common_google_signin_btn_icon_dark_normal_background 0x7f070096
int drawable common_google_signin_btn_icon_disabled 0x7f070097
int drawable common_google_signin_btn_icon_light 0x7f070098
int drawable common_google_signin_btn_icon_light_focused 0x7f070099
int drawable common_google_signin_btn_icon_light_normal 0x7f07009a
int drawable common_google_signin_btn_icon_light_normal_background 0x7f07009b
int drawable common_google_signin_btn_text_dark 0x7f07009c
int drawable common_google_signin_btn_text_dark_focused 0x7f07009d
int drawable common_google_signin_btn_text_dark_normal 0x7f07009e
int drawable common_google_signin_btn_text_dark_normal_background 0x7f07009f
int drawable common_google_signin_btn_text_disabled 0x7f0700a0
int drawable common_google_signin_btn_text_light 0x7f0700a1
int drawable common_google_signin_btn_text_light_focused 0x7f0700a2
int drawable common_google_signin_btn_text_light_normal 0x7f0700a3
int drawable common_google_signin_btn_text_light_normal_background 0x7f0700a4
int drawable corner 0x7f0700a5
int drawable design_fab_background 0x7f0700a6
int drawable design_ic_visibility 0x7f0700a7
int drawable design_ic_visibility_off 0x7f0700a8
int drawable design_password_eye 0x7f0700a9
int drawable design_snackbar_background 0x7f0700aa
int drawable floating_action_mode_shape 0x7f0700ab
int drawable googleg_disabled_color_18 0x7f0700ac
int drawable googleg_standard_color_18 0x7f0700ad
int drawable gpay_logo_generic_dark 0x7f0700ae
int drawable gpay_logo_generic_light 0x7f0700af
int drawable ic_alert 0x7f0700b0
int drawable ic_arrow_back_black_24 0x7f0700b1
int drawable ic_arrow_down_24dp 0x7f0700b2
int drawable ic_call_answer 0x7f0700b3
int drawable ic_call_answer_low 0x7f0700b4
int drawable ic_call_answer_video 0x7f0700b5
int drawable ic_call_answer_video_low 0x7f0700b6
int drawable ic_call_decline 0x7f0700b7
int drawable ic_call_decline_low 0x7f0700b8
int drawable ic_clear_black_24 0x7f0700b9
int drawable ic_clock_black_24dp 0x7f0700ba
int drawable ic_keyboard_black_24dp 0x7f0700bb
int drawable ic_m3_chip_check 0x7f0700bc
int drawable ic_m3_chip_checked_circle 0x7f0700bd
int drawable ic_m3_chip_close 0x7f0700be
int drawable ic_mtrl_checked_circle 0x7f0700bf
int drawable ic_mtrl_chip_checked_black 0x7f0700c0
int drawable ic_mtrl_chip_checked_circle 0x7f0700c1
int drawable ic_mtrl_chip_close_circle 0x7f0700c2
int drawable ic_search_black_24 0x7f0700c3
int drawable ic_tick_mark 0x7f0700c4
int drawable indeterminate_static 0x7f0700c5
int drawable launch_background 0x7f0700c6
int drawable m3_avd_hide_password 0x7f0700c7
int drawable m3_avd_show_password 0x7f0700c8
int drawable m3_bottom_sheet_drag_handle 0x7f0700c9
int drawable m3_password_eye 0x7f0700ca
int drawable m3_popupmenu_background_overlay 0x7f0700cb
int drawable m3_radiobutton_ripple 0x7f0700cc
int drawable m3_selection_control_ripple 0x7f0700cd
int drawable m3_tabs_background 0x7f0700ce
int drawable m3_tabs_line_indicator 0x7f0700cf
int drawable m3_tabs_rounded_line_indicator 0x7f0700d0
int drawable m3_tabs_transparent_background 0x7f0700d1
int drawable material_cursor_drawable 0x7f0700d2
int drawable material_ic_calendar_black_24dp 0x7f0700d3
int drawable material_ic_clear_black_24dp 0x7f0700d4
int drawable material_ic_edit_black_24dp 0x7f0700d5
int drawable material_ic_keyboard_arrow_left_black_24dp 0x7f0700d6
int drawable material_ic_keyboard_arrow_next_black_24dp 0x7f0700d7
int drawable material_ic_keyboard_arrow_previous_black_24dp 0x7f0700d8
int drawable material_ic_keyboard_arrow_right_black_24dp 0x7f0700d9
int drawable material_ic_menu_arrow_down_black_24dp 0x7f0700da
int drawable material_ic_menu_arrow_up_black_24dp 0x7f0700db
int drawable mtrl_bottomsheet_drag_handle 0x7f0700dc
int drawable mtrl_checkbox_button 0x7f0700dd
int drawable mtrl_checkbox_button_checked_unchecked 0x7f0700de
int drawable mtrl_checkbox_button_icon 0x7f0700df
int drawable mtrl_checkbox_button_icon_checked_indeterminate 0x7f0700e0
int drawable mtrl_checkbox_button_icon_checked_unchecked 0x7f0700e1
int drawable mtrl_checkbox_button_icon_indeterminate_checked 0x7f0700e2
int drawable mtrl_checkbox_button_icon_indeterminate_unchecked 0x7f0700e3
int drawable mtrl_checkbox_button_icon_unchecked_checked 0x7f0700e4
int drawable mtrl_checkbox_button_icon_unchecked_indeterminate 0x7f0700e5
int drawable mtrl_checkbox_button_unchecked_checked 0x7f0700e6
int drawable mtrl_dialog_background 0x7f0700e7
int drawable mtrl_dropdown_arrow 0x7f0700e8
int drawable mtrl_ic_arrow_drop_down 0x7f0700e9
int drawable mtrl_ic_arrow_drop_up 0x7f0700ea
int drawable mtrl_ic_cancel 0x7f0700eb
int drawable mtrl_ic_check_mark 0x7f0700ec
int drawable mtrl_ic_checkbox_checked 0x7f0700ed
int drawable mtrl_ic_checkbox_unchecked 0x7f0700ee
int drawable mtrl_ic_error 0x7f0700ef
int drawable mtrl_ic_indeterminate 0x7f0700f0
int drawable mtrl_navigation_bar_item_background 0x7f0700f1
int drawable mtrl_popupmenu_background 0x7f0700f2
int drawable mtrl_popupmenu_background_overlay 0x7f0700f3
int drawable mtrl_switch_thumb 0x7f0700f4
int drawable mtrl_switch_thumb_checked 0x7f0700f5
int drawable mtrl_switch_thumb_checked_pressed 0x7f0700f6
int drawable mtrl_switch_thumb_checked_unchecked 0x7f0700f7
int drawable mtrl_switch_thumb_pressed 0x7f0700f8
int drawable mtrl_switch_thumb_pressed_checked 0x7f0700f9
int drawable mtrl_switch_thumb_pressed_unchecked 0x7f0700fa
int drawable mtrl_switch_thumb_unchecked 0x7f0700fb
int drawable mtrl_switch_thumb_unchecked_checked 0x7f0700fc
int drawable mtrl_switch_thumb_unchecked_pressed 0x7f0700fd
int drawable mtrl_switch_track 0x7f0700fe
int drawable mtrl_switch_track_decoration 0x7f0700ff
int drawable mtrl_tabs_default_indicator 0x7f070100
int drawable navigation_empty_icon 0x7f070101
int drawable notification_action_background 0x7f070102
int drawable notification_bg 0x7f070103
int drawable notification_bg_low 0x7f070104
int drawable notification_bg_low_normal 0x7f070105
int drawable notification_bg_low_pressed 0x7f070106
int drawable notification_bg_normal 0x7f070107
int drawable notification_bg_normal_pressed 0x7f070108
int drawable notification_icon_background 0x7f070109
int drawable notification_oversize_large_icon_bg 0x7f07010a
int drawable notification_template_icon_bg 0x7f07010b
int drawable notification_template_icon_low_bg 0x7f07010c
int drawable notification_tile_bg 0x7f07010d
int drawable notify_panel_notification_icon_bg 0x7f07010e
int drawable pay_button_generic_background_dark 0x7f07010f
int drawable pay_button_generic_background_light 0x7f070110
int drawable pay_button_generic_background_mask_dark 0x7f070111
int drawable pay_button_generic_background_mask_light 0x7f070112
int drawable preference_list_divider_material 0x7f070113
int drawable rzp_border 0x7f070114
int drawable rzp_border_bottom 0x7f070115
int drawable rzp_green_button 0x7f070116
int drawable rzp_loader_circle 0x7f070117
int drawable rzp_logo 0x7f070118
int drawable rzp_name_logo 0x7f070119
int drawable rzp_poweredby 0x7f07011a
int drawable rzp_secured_by_bg 0x7f07011b
int drawable rzp_white_border_black_bg 0x7f07011c
int drawable stripe 0x7f07011d
int drawable stripe_3ds2_ic_amex 0x7f07011e
int drawable stripe_3ds2_ic_arrow 0x7f07011f
int drawable stripe_3ds2_ic_cartesbancaires 0x7f070120
int drawable stripe_3ds2_ic_discover 0x7f070121
int drawable stripe_3ds2_ic_indicator 0x7f070122
int drawable stripe_3ds2_ic_mastercard 0x7f070123
int drawable stripe_3ds2_ic_unionpay 0x7f070124
int drawable stripe_3ds2_ic_unknown 0x7f070125
int drawable stripe_3ds2_ic_visa 0x7f070126
int drawable stripe_bacs_direct_debit_mark 0x7f070127
int drawable stripe_card_form_view_text_input_layout_background 0x7f070128
int drawable stripe_card_progress_background 0x7f070129
int drawable stripe_google_pay_mark 0x7f07012a
int drawable stripe_ic_add 0x7f07012b
int drawable stripe_ic_add_black_32dp 0x7f07012c
int drawable stripe_ic_affirm_logo 0x7f07012d
int drawable stripe_ic_afterpay_logo 0x7f07012e
int drawable stripe_ic_amex 0x7f07012f
int drawable stripe_ic_amex_template_32 0x7f070130
int drawable stripe_ic_amex_unpadded 0x7f070131
int drawable stripe_ic_arrow_down 0x7f070132
int drawable stripe_ic_bank 0x7f070133
int drawable stripe_ic_bank_becs 0x7f070134
int drawable stripe_ic_bank_boa 0x7f070135
int drawable stripe_ic_bank_capitalone 0x7f070136
int drawable stripe_ic_bank_citi 0x7f070137
int drawable stripe_ic_bank_compass 0x7f070138
int drawable stripe_ic_bank_error 0x7f070139
int drawable stripe_ic_bank_generic 0x7f07013a
int drawable stripe_ic_bank_morganchase 0x7f07013b
int drawable stripe_ic_bank_nfcu 0x7f07013c
int drawable stripe_ic_bank_pnc 0x7f07013d
int drawable stripe_ic_bank_stripe 0x7f07013e
int drawable stripe_ic_bank_suntrust 0x7f07013f
int drawable stripe_ic_bank_svb 0x7f070140
int drawable stripe_ic_bank_td 0x7f070141
int drawable stripe_ic_bank_usaa 0x7f070142
int drawable stripe_ic_bank_usbank 0x7f070143
int drawable stripe_ic_bank_wellsfargo 0x7f070144
int drawable stripe_ic_brandicon_institution 0x7f070145
int drawable stripe_ic_card_visa 0x7f070146
int drawable stripe_ic_cartebancaire_template_32 0x7f070147
int drawable stripe_ic_cartes_bancaires 0x7f070148
int drawable stripe_ic_cartes_bancaires_unpadded 0x7f070149
int drawable stripe_ic_check_circle 0x7f07014a
int drawable stripe_ic_checkmark 0x7f07014b
int drawable stripe_ic_checkmark_tinted 0x7f07014c
int drawable stripe_ic_chevron_down 0x7f07014d
int drawable stripe_ic_clear 0x7f07014e
int drawable stripe_ic_clearpay_logo 0x7f07014f
int drawable stripe_ic_cvc 0x7f070150
int drawable stripe_ic_cvc_amex 0x7f070151
int drawable stripe_ic_delete_symbol 0x7f070152
int drawable stripe_ic_diners 0x7f070153
int drawable stripe_ic_diners_template_32 0x7f070154
int drawable stripe_ic_diners_unpadded 0x7f070155
int drawable stripe_ic_discover 0x7f070156
int drawable stripe_ic_discover_template_32 0x7f070157
int drawable stripe_ic_discover_unpadded 0x7f070158
int drawable stripe_ic_edit 0x7f070159
int drawable stripe_ic_edit_symbol 0x7f07015a
int drawable stripe_ic_error 0x7f07015b
int drawable stripe_ic_error_amex 0x7f07015c
int drawable stripe_ic_hcaptcha_logo 0x7f07015d
int drawable stripe_ic_info 0x7f07015e
int drawable stripe_ic_jcb 0x7f07015f
int drawable stripe_ic_jcb_template_32 0x7f070160
int drawable stripe_ic_jcb_unpadded 0x7f070161
int drawable stripe_ic_loading_spinner 0x7f070162
int drawable stripe_ic_lock 0x7f070163
int drawable stripe_ic_mail 0x7f070164
int drawable stripe_ic_mastercard 0x7f070165
int drawable stripe_ic_mastercard_template_32 0x7f070166
int drawable stripe_ic_mastercard_unpadded 0x7f070167
int drawable stripe_ic_panel_arrow_right 0x7f070168
int drawable stripe_ic_paymentsheet_add_dark 0x7f070169
int drawable stripe_ic_paymentsheet_add_light 0x7f07016a
int drawable stripe_ic_paymentsheet_back 0x7f07016b
int drawable stripe_ic_paymentsheet_bank 0x7f07016c
int drawable stripe_ic_paymentsheet_card_amex_day 0x7f07016d
int drawable stripe_ic_paymentsheet_card_amex_night 0x7f07016e
int drawable stripe_ic_paymentsheet_card_amex_ref 0x7f07016f
int drawable stripe_ic_paymentsheet_card_cartes_bancaires_day 0x7f070170
int drawable stripe_ic_paymentsheet_card_cartes_bancaires_night 0x7f070171
int drawable stripe_ic_paymentsheet_card_cartes_bancaires_ref 0x7f070172
int drawable stripe_ic_paymentsheet_card_dinersclub_day 0x7f070173
int drawable stripe_ic_paymentsheet_card_dinersclub_night 0x7f070174
int drawable stripe_ic_paymentsheet_card_dinersclub_ref 0x7f070175
int drawable stripe_ic_paymentsheet_card_discover_day 0x7f070176
int drawable stripe_ic_paymentsheet_card_discover_night 0x7f070177
int drawable stripe_ic_paymentsheet_card_discover_ref 0x7f070178
int drawable stripe_ic_paymentsheet_card_jcb_day 0x7f070179
int drawable stripe_ic_paymentsheet_card_jcb_night 0x7f07017a
int drawable stripe_ic_paymentsheet_card_jcb_ref 0x7f07017b
int drawable stripe_ic_paymentsheet_card_mastercard_day 0x7f07017c
int drawable stripe_ic_paymentsheet_card_mastercard_night 0x7f07017d
int drawable stripe_ic_paymentsheet_card_mastercard_ref 0x7f07017e
int drawable stripe_ic_paymentsheet_card_unionpay_day 0x7f07017f
int drawable stripe_ic_paymentsheet_card_unionpay_night 0x7f070180
int drawable stripe_ic_paymentsheet_card_unionpay_ref 0x7f070181
int drawable stripe_ic_paymentsheet_card_unknown_day 0x7f070182
int drawable stripe_ic_paymentsheet_card_unknown_night 0x7f070183
int drawable stripe_ic_paymentsheet_card_unknown_ref 0x7f070184
int drawable stripe_ic_paymentsheet_card_visa_day 0x7f070185
int drawable stripe_ic_paymentsheet_card_visa_night 0x7f070186
int drawable stripe_ic_paymentsheet_card_visa_ref 0x7f070187
int drawable stripe_ic_paymentsheet_close 0x7f070188
int drawable stripe_ic_paymentsheet_ctil_chevron 0x7f070189
int drawable stripe_ic_paymentsheet_ctil_chevron_down 0x7f07018a
int drawable stripe_ic_paymentsheet_ctil_chevron_up 0x7f07018b
int drawable stripe_ic_paymentsheet_googlepay_primary_button_checkmark 0x7f07018c
int drawable stripe_ic_paymentsheet_googlepay_primary_button_lock 0x7f07018d
int drawable stripe_ic_paymentsheet_link_arrow 0x7f07018e
int drawable stripe_ic_paymentsheet_link_day 0x7f07018f
int drawable stripe_ic_paymentsheet_link_night 0x7f070190
int drawable stripe_ic_paymentsheet_link_ref 0x7f070191
int drawable stripe_ic_paymentsheet_pm_affirm 0x7f070192
int drawable stripe_ic_paymentsheet_pm_afterpay_clearpay 0x7f070193
int drawable stripe_ic_paymentsheet_pm_alipay 0x7f070194
int drawable stripe_ic_paymentsheet_pm_alma 0x7f070195
int drawable stripe_ic_paymentsheet_pm_amazon_pay 0x7f070196
int drawable stripe_ic_paymentsheet_pm_bancontact 0x7f070197
int drawable stripe_ic_paymentsheet_pm_bank 0x7f070198
int drawable stripe_ic_paymentsheet_pm_billie 0x7f070199
int drawable stripe_ic_paymentsheet_pm_blik 0x7f07019a
int drawable stripe_ic_paymentsheet_pm_boleto 0x7f07019b
int drawable stripe_ic_paymentsheet_pm_card 0x7f07019c
int drawable stripe_ic_paymentsheet_pm_cash_app_pay 0x7f07019d
int drawable stripe_ic_paymentsheet_pm_crypto 0x7f07019e
int drawable stripe_ic_paymentsheet_pm_eps 0x7f07019f
int drawable stripe_ic_paymentsheet_pm_fpx 0x7f0701a0
int drawable stripe_ic_paymentsheet_pm_giropay 0x7f0701a1
int drawable stripe_ic_paymentsheet_pm_grabpay 0x7f0701a2
int drawable stripe_ic_paymentsheet_pm_ideal 0x7f0701a3
int drawable stripe_ic_paymentsheet_pm_klarna 0x7f0701a4
int drawable stripe_ic_paymentsheet_pm_konbini 0x7f0701a5
int drawable stripe_ic_paymentsheet_pm_mobile_pay 0x7f0701a6
int drawable stripe_ic_paymentsheet_pm_multibanco 0x7f0701a7
int drawable stripe_ic_paymentsheet_pm_oxxo 0x7f0701a8
int drawable stripe_ic_paymentsheet_pm_p24 0x7f0701a9
int drawable stripe_ic_paymentsheet_pm_paypal 0x7f0701aa
int drawable stripe_ic_paymentsheet_pm_revolut_pay 0x7f0701ab
int drawable stripe_ic_paymentsheet_pm_satispay 0x7f0701ac
int drawable stripe_ic_paymentsheet_pm_sepa_debit 0x7f0701ad
int drawable stripe_ic_paymentsheet_pm_sunbit 0x7f0701ae
int drawable stripe_ic_paymentsheet_pm_swish 0x7f0701af
int drawable stripe_ic_paymentsheet_pm_twint 0x7f0701b0
int drawable stripe_ic_paymentsheet_pm_upi 0x7f0701b1
int drawable stripe_ic_paymentsheet_pm_wechat_pay 0x7f0701b2
int drawable stripe_ic_paymentsheet_pm_zip 0x7f0701b3
int drawable stripe_ic_paymentsheet_polling_failure 0x7f0701b4
int drawable stripe_ic_paymentsheet_sepa_day 0x7f0701b5
int drawable stripe_ic_paymentsheet_sepa_night 0x7f0701b6
int drawable stripe_ic_paymentsheet_sepa_ref 0x7f0701b7
int drawable stripe_ic_person 0x7f0701b8
int drawable stripe_ic_photo_camera 0x7f0701b9
int drawable stripe_ic_remove_symbol 0x7f0701ba
int drawable stripe_ic_search 0x7f0701bb
int drawable stripe_ic_selected_symbol 0x7f0701bc
int drawable stripe_ic_trash 0x7f0701bd
int drawable stripe_ic_unionpay 0x7f0701be
int drawable stripe_ic_unionpay_template_32 0x7f0701bf
int drawable stripe_ic_unionpay_unpadded 0x7f0701c0
int drawable stripe_ic_unknown 0x7f0701c1
int drawable stripe_ic_visa 0x7f0701c2
int drawable stripe_ic_visa_template_32 0x7f0701c3
int drawable stripe_ic_visa_unpadded 0x7f0701c4
int drawable stripe_ic_warning 0x7f0701c5
int drawable stripe_ic_warning_circle 0x7f0701c6
int drawable stripe_link_add_green 0x7f0701c7
int drawable stripe_link_back 0x7f0701c8
int drawable stripe_link_bank 0x7f0701c9
int drawable stripe_link_chevron 0x7f0701ca
int drawable stripe_link_close 0x7f0701cb
int drawable stripe_link_complete 0x7f0701cc
int drawable stripe_link_error 0x7f0701cd
int drawable stripe_link_logo 0x7f0701ce
int drawable stripe_link_logo_bw 0x7f0701cf
int drawable stripe_link_logo_knockout_black 0x7f0701d0
int drawable stripe_link_logo_knockout_white 0x7f0701d1
int drawable stripe_logo 0x7f0701d2
int drawable stripe_paymentsheet_testmode_background 0x7f0701d3
int drawable stripe_simple_button_background 0x7f0701d4
int drawable test_level_drawable 0x7f0701d5
int drawable toast_bg 0x7f0701d6
int drawable tooltip_frame_dark 0x7f0701d7
int drawable tooltip_frame_light 0x7f0701d8
int font cursive 0x7f080000
int id ALT 0x7f090000
int id BOTTOM_END 0x7f090001
int id BOTTOM_START 0x7f090002
int id CTRL 0x7f090003
int id FUNCTION 0x7f090004
int id META 0x7f090005
int id NO_DEBUG 0x7f090006
int id SHIFT 0x7f090007
int id SHOW_ALL 0x7f090008
int id SHOW_PATH 0x7f090009
int id SHOW_PROGRESS 0x7f09000a
int id SYM 0x7f09000b
int id TOP_END 0x7f09000c
int id TOP_START 0x7f09000d
int id above 0x7f09000e
int id accelerate 0x7f09000f
int id accessibility_action_clickable_span 0x7f090010
int id accessibility_custom_action_0 0x7f090011
int id accessibility_custom_action_1 0x7f090012
int id accessibility_custom_action_10 0x7f090013
int id accessibility_custom_action_11 0x7f090014
int id accessibility_custom_action_12 0x7f090015
int id accessibility_custom_action_13 0x7f090016
int id accessibility_custom_action_14 0x7f090017
int id accessibility_custom_action_15 0x7f090018
int id accessibility_custom_action_16 0x7f090019
int id accessibility_custom_action_17 0x7f09001a
int id accessibility_custom_action_18 0x7f09001b
int id accessibility_custom_action_19 0x7f09001c
int id accessibility_custom_action_2 0x7f09001d
int id accessibility_custom_action_20 0x7f09001e
int id accessibility_custom_action_21 0x7f09001f
int id accessibility_custom_action_22 0x7f090020
int id accessibility_custom_action_23 0x7f090021
int id accessibility_custom_action_24 0x7f090022
int id accessibility_custom_action_25 0x7f090023
int id accessibility_custom_action_26 0x7f090024
int id accessibility_custom_action_27 0x7f090025
int id accessibility_custom_action_28 0x7f090026
int id accessibility_custom_action_29 0x7f090027
int id accessibility_custom_action_3 0x7f090028
int id accessibility_custom_action_30 0x7f090029
int id accessibility_custom_action_31 0x7f09002a
int id accessibility_custom_action_4 0x7f09002b
int id accessibility_custom_action_5 0x7f09002c
int id accessibility_custom_action_6 0x7f09002d
int id accessibility_custom_action_7 0x7f09002e
int id accessibility_custom_action_8 0x7f09002f
int id accessibility_custom_action_9 0x7f090030
int id account_number_edit_text 0x7f090031
int id account_number_text_input_layout 0x7f090032
int id action0 0x7f090033
int id actionDown 0x7f090034
int id actionDownUp 0x7f090035
int id actionUp 0x7f090036
int id action_bar 0x7f090037
int id action_bar_activity_content 0x7f090038
int id action_bar_container 0x7f090039
int id action_bar_root 0x7f09003a
int id action_bar_spinner 0x7f09003b
int id action_bar_subtitle 0x7f09003c
int id action_bar_title 0x7f09003d
int id action_close 0x7f09003e
int id action_container 0x7f09003f
int id action_context_bar 0x7f090040
int id action_divider 0x7f090041
int id action_go_back 0x7f090042
int id action_go_forward 0x7f090043
int id action_image 0x7f090044
int id action_menu_divider 0x7f090045
int id action_menu_presenter 0x7f090046
int id action_mode_bar 0x7f090047
int id action_mode_bar_stub 0x7f090048
int id action_mode_close_button 0x7f090049
int id action_reload 0x7f09004a
int id action_save 0x7f09004b
int id action_share 0x7f09004c
int id action_text 0x7f09004d
int id actions 0x7f09004e
int id activity_chooser_view_content 0x7f09004f
int id add 0x7f090050
int id add_payment_method_card 0x7f090051
int id adjacent 0x7f090052
int id adjust_height 0x7f090053
int id adjust_width 0x7f090054
int id alertTitle 0x7f090055
int id aligned 0x7f090056
int id all 0x7f090057
int id allStates 0x7f090058
int id always 0x7f090059
int id alwaysAllow 0x7f09005a
int id alwaysDisallow 0x7f09005b
int id android_pay 0x7f09005c
int id android_pay_dark 0x7f09005d
int id android_pay_light 0x7f09005e
int id android_pay_light_with_border 0x7f09005f
int id androidx_compose_ui_view_composition_context 0x7f090060
int id androidx_window_activity_scope 0x7f090061
int id animateToEnd 0x7f090062
int id animateToStart 0x7f090063
int id antiClockwise 0x7f090064
int id anticipate 0x7f090065
int id arc 0x7f090066
int id asConfigured 0x7f090067
int id async 0x7f090068
int id auto 0x7f090069
int id autoComplete 0x7f09006a
int id autoCompleteToEnd 0x7f09006b
int id autoCompleteToStart 0x7f09006c
int id axisRelative 0x7f09006d
int id bank_list 0x7f09006e
int id barrier 0x7f09006f
int id baseline 0x7f090070
int id beginOnFirstDraw 0x7f090071
int id beginning 0x7f090072
int id below 0x7f090073
int id bestChoice 0x7f090074
int id billing_address_widget 0x7f090075
int id binding_reference 0x7f090076
int id blocking 0x7f090077
int id book_now 0x7f090078
int id borderless 0x7f090079
int id bottom 0x7f09007a
int id bottomToTop 0x7f09007b
int id bounce 0x7f09007c
int id bounceBoth 0x7f09007d
int id bounceEnd 0x7f09007e
int id bounceStart 0x7f09007f
int id brand_check 0x7f090080
int id brand_icon 0x7f090081
int id brand_logo 0x7f090082
int id brand_text 0x7f090083
int id browser_actions_header_text 0x7f090084
int id browser_actions_menu_item_icon 0x7f090085
int id browser_actions_menu_item_text 0x7f090086
int id browser_actions_menu_items 0x7f090087
int id browser_actions_menu_view 0x7f090088
int id bsb_edit_text 0x7f090089
int id bsb_text_input_layout 0x7f09008a
int id buttonPanel 0x7f09008b
int id buyButton 0x7f09008c
int id buy_now 0x7f09008d
int id buy_with 0x7f09008e
int id buy_with_google 0x7f09008f
int id ca_brand_zone 0x7f090090
int id ca_challenge_zone 0x7f090091
int id ca_information_zone 0x7f090092
int id cache_measures 0x7f090093
int id callMeasure 0x7f090094
int id cancel_action 0x7f090095
int id cancel_button 0x7f090096
int id card_brand_view 0x7f090097
int id card_loading 0x7f090098
int id card_multiline_widget 0x7f090099
int id card_multiline_widget_container 0x7f09009a
int id card_number_edit_text 0x7f09009b
int id card_number_input_container 0x7f09009c
int id card_number_text_input_layout 0x7f09009d
int id carryVelocity 0x7f09009e
int id center 0x7f09009f
int id centerCrop 0x7f0900a0
int id centerInside 0x7f0900a1
int id center_horizontal 0x7f0900a2
int id center_vertical 0x7f0900a3
int id chain 0x7f0900a4
int id chain2 0x7f0900a5
int id chains 0x7f0900a6
int id check_icon 0x7f0900a7
int id check_list 0x7f0900a8
int id checkbox 0x7f0900a9
int id checked 0x7f0900aa
int id chevron 0x7f0900ab
int id chronometer 0x7f0900ac
int id circle_center 0x7f0900ad
int id classic 0x7f0900ae
int id clear_text 0x7f0900af
int id clip_horizontal 0x7f0900b0
int id clip_vertical 0x7f0900b1
int id clockwise 0x7f0900b2
int id closest 0x7f0900b3
int id collapseActionView 0x7f0900b4
int id compose_view_saveable_id_tag 0x7f0900b5
int id compress 0x7f0900b6
int id confirm_button 0x7f0900b7
int id confirmed_icon 0x7f0900b8
int id confirming_icon 0x7f0900b9
int id constraint 0x7f0900ba
int id consume_window_insets_tag 0x7f0900bb
int id container 0x7f0900bc
int id content 0x7f0900bd
int id contentPanel 0x7f0900be
int id contiguous 0x7f0900bf
int id continuousVelocity 0x7f0900c0
int id coordinator 0x7f0900c1
int id cos 0x7f0900c2
int id counterclockwise 0x7f0900c3
int id country_autocomplete_aaw 0x7f0900c4
int id country_layout 0x7f0900c5
int id country_postal_divider 0x7f0900c6
int id cradle 0x7f0900c7
int id currentState 0x7f0900c8
int id custom 0x7f0900c9
int id customPanel 0x7f0900ca
int id cut 0x7f0900cb
int id cvc_edit_text 0x7f0900cc
int id cvc_text_input_layout 0x7f0900cd
int id czv_entry_view 0x7f0900ce
int id czv_header 0x7f0900cf
int id czv_info 0x7f0900d0
int id czv_info_label 0x7f0900d1
int id czv_resend_button 0x7f0900d2
int id czv_submit_button 0x7f0900d3
int id czv_whitelist_no_button 0x7f0900d4
int id czv_whitelist_radio_group 0x7f0900d5
int id czv_whitelist_yes_button 0x7f0900d6
int id czv_whitelisting_label 0x7f0900d7
int id dark 0x7f0900d8
int id date_picker_actions 0x7f0900d9
int id decelerate 0x7f0900da
int id decelerateAndComplete 0x7f0900db
int id decor_content_parent 0x7f0900dc
int id default_activity_button 0x7f0900dd
int id deltaRelative 0x7f0900de
int id dependency_ordering 0x7f0900df
int id description 0x7f0900e0
int id design_bottom_sheet 0x7f0900e1
int id design_menu_item_action_area 0x7f0900e2
int id design_menu_item_action_area_stub 0x7f0900e3
int id design_menu_item_text 0x7f0900e4
int id design_navigation_view 0x7f0900e5
int id details 0x7f0900e6
int id dialog_button 0x7f0900e7
int id dimensions 0x7f0900e8
int id direct 0x7f0900e9
int id disableHome 0x7f0900ea
int id disableIntraAutoTransition 0x7f0900eb
int id disablePostScroll 0x7f0900ec
int id disableScroll 0x7f0900ed
int id disjoint 0x7f0900ee
int id donate_with 0x7f0900ef
int id donate_with_google 0x7f0900f0
int id dragAnticlockwise 0x7f0900f1
int id dragClockwise 0x7f0900f2
int id dragDown 0x7f0900f3
int id dragEnd 0x7f0900f4
int id dragLeft 0x7f0900f5
int id dragRight 0x7f0900f6
int id dragStart 0x7f0900f7
int id dragUp 0x7f0900f8
int id dropdown_menu 0x7f0900f9
int id easeIn 0x7f0900fa
int id easeInOut 0x7f0900fb
int id easeOut 0x7f0900fc
int id east 0x7f0900fd
int id edge 0x7f0900fe
int id edit_query 0x7f0900ff
int id edit_text_id 0x7f090100
int id elastic 0x7f090101
int id email_edit_text 0x7f090102
int id email_text_input_layout 0x7f090103
int id embed 0x7f090104
int id end 0x7f090105
int id endToStart 0x7f090106
int id end_padder 0x7f090107
int id enterAlways 0x7f090108
int id enterAlwaysCollapsed 0x7f090109
int id errors 0x7f09010a
int id escape 0x7f09010b
int id et_address_line_one_aaw 0x7f09010c
int id et_address_line_two_aaw 0x7f09010d
int id et_card_number 0x7f09010e
int id et_city_aaw 0x7f09010f
int id et_cvc 0x7f090110
int id et_expiry 0x7f090111
int id et_name_aaw 0x7f090112
int id et_phone_number_aaw 0x7f090113
int id et_postal_code 0x7f090114
int id et_postal_code_aaw 0x7f090115
int id et_state_aaw 0x7f090116
int id exitUntilCollapsed 0x7f090117
int id expand_activities_button 0x7f090118
int id expand_arrow 0x7f090119
int id expand_container 0x7f09011a
int id expand_label 0x7f09011b
int id expand_text 0x7f09011c
int id expanded_menu 0x7f09011d
int id expiry_date_edit_text 0x7f09011e
int id expiry_date_text_input_layout 0x7f09011f
int id fade 0x7f090120
int id fill 0x7f090121
int id fill_horizontal 0x7f090122
int id fill_vertical 0x7f090123
int id filled 0x7f090124
int id fitCenter 0x7f090125
int id fitEnd 0x7f090126
int id fitStart 0x7f090127
int id fitToContents 0x7f090128
int id fitXY 0x7f090129
int id fixed 0x7f09012a
int id flip 0x7f09012b
int id floating 0x7f09012c
int id forever 0x7f09012d
int id fragment_container 0x7f09012e
int id fragment_container_view_tag 0x7f09012f
int id frost 0x7f090130
int id fullscreen_header 0x7f090131
int id ghost_view 0x7f090132
int id ghost_view_holder 0x7f090133
int id glide_custom_view_target_tag 0x7f090134
int id gone 0x7f090135
int id googleMaterial2 0x7f090136
int id googlePayButton 0x7f090137
int id google_pay_button_layout 0x7f090138
int id google_pay_payment_button 0x7f090139
int id google_pay_primary_button 0x7f09013a
int id google_wallet_classic 0x7f09013b
int id google_wallet_grayscale 0x7f09013c
int id google_wallet_monochrome 0x7f09013d
int id graph 0x7f09013e
int id graph_wrap 0x7f09013f
int id grayscale 0x7f090140
int id group_divider 0x7f090141
int id grouping 0x7f090142
int id groups 0x7f090143
int id header_title 0x7f090144
int id hide_ime_id 0x7f090145
int id hide_in_inspector_tag 0x7f090146
int id hideable 0x7f090147
int id holo_dark 0x7f090148
int id holo_light 0x7f090149
int id home 0x7f09014a
int id homeAsUp 0x7f09014b
int id honorRequest 0x7f09014c
int id horizontal 0x7f09014d
int id horizontal_only 0x7f09014e
int id hybrid 0x7f09014f
int id icon 0x7f090150
int id icon_frame 0x7f090151
int id icon_group 0x7f090152
int id icon_only 0x7f090153
int id ifRoom 0x7f090154
int id ignore 0x7f090155
int id ignoreRequest 0x7f090156
int id image 0x7f090157
int id immediateStop 0x7f090158
int id included 0x7f090159
int id indeterminate 0x7f09015a
int id info 0x7f09015b
int id inspection_slot_table_set 0x7f09015c
int id invisible 0x7f09015d
int id inward 0x7f09015e
int id is_pooling_container_tag 0x7f09015f
int id issuer_image 0x7f090160
int id italic 0x7f090161
int id item_touch_helper_previous_elevation 0x7f090162
int id iv_check_mark 0x7f090163
int id jumpToEnd 0x7f090164
int id jumpToStart 0x7f090165
int id label 0x7f090166
int id labeled 0x7f090167
int id layout 0x7f090168
int id left 0x7f090169
int id leftToRight 0x7f09016a
int id legacy 0x7f09016b
int id light 0x7f09016c
int id line1 0x7f09016d
int id line3 0x7f09016e
int id linear 0x7f09016f
int id listMode 0x7f090170
int id list_item 0x7f090171
int id ll_loader 0x7f090172
int id loadingContainer 0x7f090173
int id locale 0x7f090174
int id lock_icon 0x7f090175
int id logo_only 0x7f090176
int id ltr 0x7f090177
int id m3_side_sheet 0x7f090178
int id mandate_acceptance_text_view 0x7f090179
int id marquee 0x7f09017a
int id masked 0x7f09017b
int id masked_card_item 0x7f09017c
int id match_constraint 0x7f09017d
int id match_parent 0x7f09017e
int id material 0x7f09017f
int id material_clock_display 0x7f090180
int id material_clock_display_and_toggle 0x7f090181
int id material_clock_face 0x7f090182
int id material_clock_hand 0x7f090183
int id material_clock_level 0x7f090184
int id material_clock_period_am_button 0x7f090185
int id material_clock_period_pm_button 0x7f090186
int id material_clock_period_toggle 0x7f090187
int id material_hour_text_input 0x7f090188
int id material_hour_tv 0x7f090189
int id material_label 0x7f09018a
int id material_minute_text_input 0x7f09018b
int id material_minute_tv 0x7f09018c
int id material_textinput_timepicker 0x7f09018d
int id material_timepicker_cancel_button 0x7f09018e
int id material_timepicker_container 0x7f09018f
int id material_timepicker_mode_button 0x7f090190
int id material_timepicker_ok_button 0x7f090191
int id material_timepicker_view 0x7f090192
int id material_value_index 0x7f090193
int id matrix 0x7f090194
int id media_actions 0x7f090195
int id menu_search 0x7f090196
int id message 0x7f090197
int id middle 0x7f090198
int id mini 0x7f090199
int id monochrome 0x7f09019a
int id month_grid 0x7f09019b
int id month_navigation_bar 0x7f09019c
int id month_navigation_fragment_toggle 0x7f09019d
int id month_navigation_next 0x7f09019e
int id month_navigation_previous 0x7f09019f
int id month_title 0x7f0901a0
int id motion_base 0x7f0901a1
int id mtrl_anchor_parent 0x7f0901a2
int id mtrl_calendar_day_selector_frame 0x7f0901a3
int id mtrl_calendar_days_of_week 0x7f0901a4
int id mtrl_calendar_frame 0x7f0901a5
int id mtrl_calendar_main_pane 0x7f0901a6
int id mtrl_calendar_months 0x7f0901a7
int id mtrl_calendar_selection_frame 0x7f0901a8
int id mtrl_calendar_text_input_frame 0x7f0901a9
int id mtrl_calendar_year_selector_frame 0x7f0901aa
int id mtrl_card_checked_layer_id 0x7f0901ab
int id mtrl_child_content_container 0x7f0901ac
int id mtrl_internal_children_alpha_tag 0x7f0901ad
int id mtrl_motion_snapshot_view 0x7f0901ae
int id mtrl_picker_fullscreen 0x7f0901af
int id mtrl_picker_header 0x7f0901b0
int id mtrl_picker_header_selection_text 0x7f0901b1
int id mtrl_picker_header_title_and_selection 0x7f0901b2
int id mtrl_picker_header_toggle 0x7f0901b3
int id mtrl_picker_text_input_date 0x7f0901b4
int id mtrl_picker_text_input_range_end 0x7f0901b5
int id mtrl_picker_text_input_range_start 0x7f0901b6
int id mtrl_picker_title_text 0x7f0901b7
int id mtrl_view_tag_bottom_padding 0x7f0901b8
int id multiply 0x7f0901b9
int id name 0x7f0901ba
int id name_edit_text 0x7f0901bb
int id name_text_input_layout 0x7f0901bc
int id nav_controller_view_tag 0x7f0901bd
int id navigation_bar_item_active_indicator_view 0x7f0901be
int id navigation_bar_item_icon_container 0x7f0901bf
int id navigation_bar_item_icon_view 0x7f0901c0
int id navigation_bar_item_labels_group 0x7f0901c1
int id navigation_bar_item_large_label_view 0x7f0901c2
int id navigation_bar_item_small_label_view 0x7f0901c3
int id navigation_header_container 0x7f0901c4
int id never 0x7f0901c5
int id neverCompleteToEnd 0x7f0901c6
int id neverCompleteToStart 0x7f0901c7
int id noScroll 0x7f0901c8
int id noState 0x7f0901c9
int id none 0x7f0901ca
int id normal 0x7f0901cb
int id north 0x7f0901cc
int id notification_background 0x7f0901cd
int id notification_main_column 0x7f0901ce
int id notification_main_column_container 0x7f0901cf
int id off 0x7f0901d0
int id on 0x7f0901d1
int id onInterceptTouchReturnSwipe 0x7f0901d2
int id open_search_bar_text_view 0x7f0901d3
int id open_search_view_background 0x7f0901d4
int id open_search_view_clear_button 0x7f0901d5
int id open_search_view_content_container 0x7f0901d6
int id open_search_view_divider 0x7f0901d7
int id open_search_view_dummy_toolbar 0x7f0901d8
int id open_search_view_edit_text 0x7f0901d9
int id open_search_view_header_container 0x7f0901da
int id open_search_view_root 0x7f0901db
int id open_search_view_scrim 0x7f0901dc
int id open_search_view_search_prefix 0x7f0901dd
int id open_search_view_status_bar_spacer 0x7f0901de
int id open_search_view_toolbar 0x7f0901df
int id open_search_view_toolbar_container 0x7f0901e0
int id outline 0x7f0901e1
int id outward 0x7f0901e2
int id overshoot 0x7f0901e3
int id packed 0x7f0901e4
int id parallax 0x7f0901e5
int id parent 0x7f0901e6
int id parentPanel 0x7f0901e7
int id parentRelative 0x7f0901e8
int id parent_matrix 0x7f0901e9
int id password_toggle 0x7f0901ea
int id path 0x7f0901eb
int id pathRelative 0x7f0901ec
int id pay_button_logo 0x7f0901ed
int id pay_button_view 0x7f0901ee
int id payment_system_image 0x7f0901ef
int id peekHeight 0x7f0901f0
int id percent 0x7f0901f1
int id pin 0x7f0901f2
int id pooling_container_listener_holder_tag 0x7f0901f3
int id position 0x7f0901f4
int id postLayout 0x7f0901f5
int id postal_code 0x7f0901f6
int id postal_code_container 0x7f0901f7
int id postal_code_edit_text 0x7f0901f8
int id postal_code_text_input_layout 0x7f0901f9
int id preferences_detail 0x7f0901fa
int id preferences_header 0x7f0901fb
int id preferences_sliding_pane_layout 0x7f0901fc
int id pressed 0x7f0901fd
int id price 0x7f0901fe
int id primary_button 0x7f0901ff
int id production 0x7f090200
int id progressBar 0x7f090201
int id progress_bar 0x7f090202
int id progress_circular 0x7f090203
int id progress_horizontal 0x7f090204
int id pullToRefresh 0x7f090205
int id radio 0x7f090206
int id ratio 0x7f090207
int id rectangles 0x7f090208
int id recycler_view 0x7f090209
int id report_drawn 0x7f09020a
int id reverseSawtooth 0x7f09020b
int id right 0x7f09020c
int id rightToLeft 0x7f09020d
int id right_icon 0x7f09020e
int id right_side 0x7f09020f
int id root 0x7f090210
int id rounded 0x7f090211
int id row_index_key 0x7f090212
int id rtl 0x7f090213
int id rzp_innerbox 0x7f090214
int id rzp_outerbox 0x7f090215
int id rzp_securedpayments 0x7f090216
int id rzp_theMainMagicView 0x7f090217
int id sandbox 0x7f090218
int id satellite 0x7f090219
int id save_non_transition_alpha 0x7f09021a
int id save_overlay_view 0x7f09021b
int id sawtooth 0x7f09021c
int id scale 0x7f09021d
int id screen 0x7f09021e
int id scroll 0x7f09021f
int id scrollIndicatorDown 0x7f090220
int id scrollIndicatorUp 0x7f090221
int id scrollView 0x7f090222
int id scrollable 0x7f090223
int id search_badge 0x7f090224
int id search_bar 0x7f090225
int id search_button 0x7f090226
int id search_close_btn 0x7f090227
int id search_edit_frame 0x7f090228
int id search_go_btn 0x7f090229
int id search_mag_icon 0x7f09022a
int id search_plate 0x7f09022b
int id search_src_text 0x7f09022c
int id search_voice_btn 0x7f09022d
int id second_row_layout 0x7f09022e
int id seekbar 0x7f09022f
int id seekbar_value 0x7f090230
int id select_dialog_listview 0x7f090231
int id select_group 0x7f090232
int id select_shipping_method_widget 0x7f090233
int id selected 0x7f090234
int id selected_icon 0x7f090235
int id selectionDetails 0x7f090236
int id selection_type 0x7f090237
int id sharedValueSet 0x7f090238
int id sharedValueUnset 0x7f090239
int id shipping_info_widget 0x7f09023a
int id shipping_methods 0x7f09023b
int id shortcut 0x7f09023c
int id showCustom 0x7f09023d
int id showHome 0x7f09023e
int id showTitle 0x7f09023f
int id sin 0x7f090240
int id skipCollapsed 0x7f090241
int id skipped 0x7f090242
int id slide 0x7f090243
int id snackbar_action 0x7f090244
int id snackbar_text 0x7f090245
int id snap 0x7f090246
int id snapMargins 0x7f090247
int id south 0x7f090248
int id spacer 0x7f090249
int id special_effects_controller_view_tag 0x7f09024a
int id spinner 0x7f09024b
int id spline 0x7f09024c
int id split_action_bar 0x7f09024d
int id spread 0x7f09024e
int id spread_inside 0x7f09024f
int id spring 0x7f090250
int id square 0x7f090251
int id src_atop 0x7f090252
int id src_in 0x7f090253
int id src_over 0x7f090254
int id standard 0x7f090255
int id start 0x7f090256
int id startHorizontal 0x7f090257
int id startToEnd 0x7f090258
int id startVertical 0x7f090259
int id staticLayout 0x7f09025a
int id staticPostLayout 0x7f09025b
int id status_bar_latest_event_content 0x7f09025c
int id stop 0x7f09025d
int id stretch 0x7f09025e
int id strict_sandbox 0x7f09025f
int id stripe_3ds2_default_challenge_zone_select_view_id 0x7f090260
int id stripe_add_payment_method_footer 0x7f090261
int id stripe_add_payment_method_form 0x7f090262
int id stripe_default_reader_id 0x7f090263
int id stripe_payment_methods_add_card 0x7f090264
int id stripe_payment_methods_add_fpx 0x7f090265
int id stripe_payment_methods_add_netbanking 0x7f090266
int id stripe_payment_methods_footer 0x7f090267
int id submenuarrow 0x7f090268
int id submit_area 0x7f090269
int id supportScrollUp 0x7f09026a
int id switchWidget 0x7f09026b
int id tabMode 0x7f09026c
int id tag_accessibility_actions 0x7f09026d
int id tag_accessibility_clickable_spans 0x7f09026e
int id tag_accessibility_heading 0x7f09026f
int id tag_accessibility_pane_title 0x7f090270
int id tag_on_apply_window_listener 0x7f090271
int id tag_on_receive_content_listener 0x7f090272
int id tag_on_receive_content_mime_types 0x7f090273
int id tag_screen_reader_focusable 0x7f090274
int id tag_state_description 0x7f090275
int id tag_transition_group 0x7f090276
int id tag_unhandled_key_event_manager 0x7f090277
int id tag_unhandled_key_listeners 0x7f090278
int id tag_window_insets_animation_callback 0x7f090279
int id terrain 0x7f09027a
int id test 0x7f09027b
int id text 0x7f09027c
int id text2 0x7f09027d
int id textEnd 0x7f09027e
int id textSpacerNoButtons 0x7f09027f
int id textSpacerNoTitle 0x7f090280
int id textStart 0x7f090281
int id textTop 0x7f090282
int id textView 0x7f090283
int id text_entry 0x7f090284
int id text_input_card_number 0x7f090285
int id text_input_cvc 0x7f090286
int id text_input_end_icon 0x7f090287
int id text_input_error_icon 0x7f090288
int id text_input_expiry_date 0x7f090289
int id text_input_start_icon 0x7f09028a
int id textinput_counter 0x7f09028b
int id textinput_error 0x7f09028c
int id textinput_helper_text 0x7f09028d
int id textinput_placeholder 0x7f09028e
int id textinput_prefix_text 0x7f09028f
int id textinput_suffix_text 0x7f090290
int id time 0x7f090291
int id title 0x7f090292
int id titleDividerNoCustom 0x7f090293
int id title_template 0x7f090294
int id tl_address_line1_aaw 0x7f090295
int id tl_address_line2_aaw 0x7f090296
int id tl_card_number 0x7f090297
int id tl_city_aaw 0x7f090298
int id tl_cvc 0x7f090299
int id tl_expiry 0x7f09029a
int id tl_name_aaw 0x7f09029b
int id tl_phone_number_aaw 0x7f09029c
int id tl_postal_code 0x7f09029d
int id tl_postal_code_aaw 0x7f09029e
int id tl_state_aaw 0x7f09029f
int id toggle 0x7f0902a0
int id toolbar 0x7f0902a1
int id top 0x7f0902a2
int id topPanel 0x7f0902a3
int id topToBottom 0x7f0902a4
int id touch_outside 0x7f0902a5
int id transitionToEnd 0x7f0902a6
int id transitionToStart 0x7f0902a7
int id transition_clip 0x7f0902a8
int id transition_current_scene 0x7f0902a9
int id transition_image_transform 0x7f0902aa
int id transition_layout_save 0x7f0902ab
int id transition_pause_alpha 0x7f0902ac
int id transition_position 0x7f0902ad
int id transition_scene_layoutid_cache 0x7f0902ae
int id transition_transform 0x7f0902af
int id triangle 0x7f0902b0
int id tv_sub_item 0x7f0902b1
int id tv_title 0x7f0902b2
int id unchecked 0x7f0902b3
int id uniform 0x7f0902b4
int id unlabeled 0x7f0902b5
int id up 0x7f0902b6
int id useLogo 0x7f0902b7
int id vertical 0x7f0902b8
int id vertical_only 0x7f0902b9
int id view_offset_helper 0x7f0902ba
int id view_stub 0x7f0902bb
int id view_transition 0x7f0902bc
int id view_tree_lifecycle_owner 0x7f0902bd
int id view_tree_on_back_pressed_dispatcher_owner 0x7f0902be
int id view_tree_saved_state_registry_owner 0x7f0902bf
int id view_tree_view_model_store_owner 0x7f0902c0
int id visible 0x7f0902c1
int id visible_removing_fragment_view_tag 0x7f0902c2
int id webView 0x7f0902c3
int id web_view 0x7f0902c4
int id web_view_container 0x7f0902c5
int id west 0x7f0902c6
int id why_arrow 0x7f0902c7
int id why_container 0x7f0902c8
int id why_label 0x7f0902c9
int id why_text 0x7f0902ca
int id wide 0x7f0902cb
int id withText 0x7f0902cc
int id with_icon 0x7f0902cd
int id withinBounds 0x7f0902ce
int id wrap 0x7f0902cf
int id wrap_content 0x7f0902d0
int id wrap_content_constrained 0x7f0902d1
int id wrapped_composition_tag 0x7f0902d2
int id x_left 0x7f0902d3
int id x_right 0x7f0902d4
int integer abc_config_activityDefaultDur 0x7f0a0000
int integer abc_config_activityShortDur 0x7f0a0001
int integer app_bar_elevation_anim_duration 0x7f0a0002
int integer bottom_sheet_slide_duration 0x7f0a0003
int integer cancel_button_image_alpha 0x7f0a0004
int integer config_tooltipAnimTime 0x7f0a0005
int integer design_snackbar_text_max_lines 0x7f0a0006
int integer design_tab_indicator_anim_duration_ms 0x7f0a0007
int integer google_play_services_version 0x7f0a0008
int integer hide_password_duration 0x7f0a0009
int integer m3_badge_max_number 0x7f0a000a
int integer m3_btn_anim_delay_ms 0x7f0a000b
int integer m3_btn_anim_duration_ms 0x7f0a000c
int integer m3_card_anim_delay_ms 0x7f0a000d
int integer m3_card_anim_duration_ms 0x7f0a000e
int integer m3_chip_anim_duration 0x7f0a000f
int integer m3_sys_motion_duration_extra_long1 0x7f0a0010
int integer m3_sys_motion_duration_extra_long2 0x7f0a0011
int integer m3_sys_motion_duration_extra_long3 0x7f0a0012
int integer m3_sys_motion_duration_extra_long4 0x7f0a0013
int integer m3_sys_motion_duration_long1 0x7f0a0014
int integer m3_sys_motion_duration_long2 0x7f0a0015
int integer m3_sys_motion_duration_long3 0x7f0a0016
int integer m3_sys_motion_duration_long4 0x7f0a0017
int integer m3_sys_motion_duration_medium1 0x7f0a0018
int integer m3_sys_motion_duration_medium2 0x7f0a0019
int integer m3_sys_motion_duration_medium3 0x7f0a001a
int integer m3_sys_motion_duration_medium4 0x7f0a001b
int integer m3_sys_motion_duration_short1 0x7f0a001c
int integer m3_sys_motion_duration_short2 0x7f0a001d
int integer m3_sys_motion_duration_short3 0x7f0a001e
int integer m3_sys_motion_duration_short4 0x7f0a001f
int integer m3_sys_motion_path 0x7f0a0020
int integer m3_sys_shape_corner_extra_large_corner_family 0x7f0a0021
int integer m3_sys_shape_corner_extra_small_corner_family 0x7f0a0022
int integer m3_sys_shape_corner_full_corner_family 0x7f0a0023
int integer m3_sys_shape_corner_large_corner_family 0x7f0a0024
int integer m3_sys_shape_corner_medium_corner_family 0x7f0a0025
int integer m3_sys_shape_corner_small_corner_family 0x7f0a0026
int integer material_motion_duration_long_1 0x7f0a0027
int integer material_motion_duration_long_2 0x7f0a0028
int integer material_motion_duration_medium_1 0x7f0a0029
int integer material_motion_duration_medium_2 0x7f0a002a
int integer material_motion_duration_short_1 0x7f0a002b
int integer material_motion_duration_short_2 0x7f0a002c
int integer material_motion_path 0x7f0a002d
int integer mtrl_badge_max_character_count 0x7f0a002e
int integer mtrl_btn_anim_delay_ms 0x7f0a002f
int integer mtrl_btn_anim_duration_ms 0x7f0a0030
int integer mtrl_calendar_header_orientation 0x7f0a0031
int integer mtrl_calendar_selection_text_lines 0x7f0a0032
int integer mtrl_calendar_year_selector_span 0x7f0a0033
int integer mtrl_card_anim_delay_ms 0x7f0a0034
int integer mtrl_card_anim_duration_ms 0x7f0a0035
int integer mtrl_chip_anim_duration 0x7f0a0036
int integer mtrl_switch_thumb_motion_duration 0x7f0a0037
int integer mtrl_switch_thumb_post_morphing_duration 0x7f0a0038
int integer mtrl_switch_thumb_pre_morphing_duration 0x7f0a0039
int integer mtrl_switch_thumb_pressed_duration 0x7f0a003a
int integer mtrl_switch_thumb_viewport_center_coordinate 0x7f0a003b
int integer mtrl_switch_thumb_viewport_size 0x7f0a003c
int integer mtrl_switch_track_viewport_height 0x7f0a003d
int integer mtrl_switch_track_viewport_width 0x7f0a003e
int integer mtrl_tab_indicator_anim_duration_ms 0x7f0a003f
int integer mtrl_view_gone 0x7f0a0040
int integer mtrl_view_invisible 0x7f0a0041
int integer mtrl_view_visible 0x7f0a0042
int integer preferences_detail_pane_weight 0x7f0a0043
int integer preferences_header_pane_weight 0x7f0a0044
int integer show_password_duration 0x7f0a0045
int integer status_bar_notification_info_maxnum 0x7f0a0046
int integer stripe_card_widget_progress_fade_in_duration 0x7f0a0047
int integer stripe_card_widget_progress_fade_out_duration 0x7f0a0048
int integer stripe_date_digits_length 0x7f0a0049
int integer stripe_light_text_alpha_hex 0x7f0a004a
int interpolator btn_checkbox_checked_mtrl_animation_interpolator_0 0x7f0b0000
int interpolator btn_checkbox_checked_mtrl_animation_interpolator_1 0x7f0b0001
int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_0 0x7f0b0002
int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_1 0x7f0b0003
int interpolator btn_radio_to_off_mtrl_animation_interpolator_0 0x7f0b0004
int interpolator btn_radio_to_on_mtrl_animation_interpolator_0 0x7f0b0005
int interpolator fast_out_slow_in 0x7f0b0006
int interpolator m3_sys_motion_easing_emphasized 0x7f0b0007
int interpolator m3_sys_motion_easing_emphasized_accelerate 0x7f0b0008
int interpolator m3_sys_motion_easing_emphasized_decelerate 0x7f0b0009
int interpolator m3_sys_motion_easing_linear 0x7f0b000a
int interpolator m3_sys_motion_easing_standard 0x7f0b000b
int interpolator m3_sys_motion_easing_standard_accelerate 0x7f0b000c
int interpolator m3_sys_motion_easing_standard_decelerate 0x7f0b000d
int interpolator mtrl_fast_out_linear_in 0x7f0b000e
int interpolator mtrl_fast_out_slow_in 0x7f0b000f
int interpolator mtrl_linear 0x7f0b0010
int interpolator mtrl_linear_out_slow_in 0x7f0b0011
int layout abc_action_bar_title_item 0x7f0c0000
int layout abc_action_bar_up_container 0x7f0c0001
int layout abc_action_menu_item_layout 0x7f0c0002
int layout abc_action_menu_layout 0x7f0c0003
int layout abc_action_mode_bar 0x7f0c0004
int layout abc_action_mode_close_item_material 0x7f0c0005
int layout abc_activity_chooser_view 0x7f0c0006
int layout abc_activity_chooser_view_list_item 0x7f0c0007
int layout abc_alert_dialog_button_bar_material 0x7f0c0008
int layout abc_alert_dialog_material 0x7f0c0009
int layout abc_alert_dialog_title_material 0x7f0c000a
int layout abc_cascading_menu_item_layout 0x7f0c000b
int layout abc_dialog_title_material 0x7f0c000c
int layout abc_expanded_menu_layout 0x7f0c000d
int layout abc_list_menu_item_checkbox 0x7f0c000e
int layout abc_list_menu_item_icon 0x7f0c000f
int layout abc_list_menu_item_layout 0x7f0c0010
int layout abc_list_menu_item_radio 0x7f0c0011
int layout abc_popup_menu_header_item_layout 0x7f0c0012
int layout abc_popup_menu_item_layout 0x7f0c0013
int layout abc_screen_content_include 0x7f0c0014
int layout abc_screen_simple 0x7f0c0015
int layout abc_screen_simple_overlay_action_mode 0x7f0c0016
int layout abc_screen_toolbar 0x7f0c0017
int layout abc_search_dropdown_item_icons_2line 0x7f0c0018
int layout abc_search_view 0x7f0c0019
int layout abc_select_dialog_material 0x7f0c001a
int layout abc_tooltip 0x7f0c001b
int layout activity_web_view 0x7f0c001c
int layout browser_actions_context_menu_page 0x7f0c001d
int layout browser_actions_context_menu_row 0x7f0c001e
int layout chrome_custom_tabs_layout 0x7f0c001f
int layout custom_dialog 0x7f0c0020
int layout design_bottom_navigation_item 0x7f0c0021
int layout design_bottom_sheet_dialog 0x7f0c0022
int layout design_layout_snackbar 0x7f0c0023
int layout design_layout_snackbar_include 0x7f0c0024
int layout design_layout_tab_icon 0x7f0c0025
int layout design_layout_tab_text 0x7f0c0026
int layout design_menu_item_action_area 0x7f0c0027
int layout design_navigation_item 0x7f0c0028
int layout design_navigation_item_header 0x7f0c0029
int layout design_navigation_item_separator 0x7f0c002a
int layout design_navigation_item_subheader 0x7f0c002b
int layout design_navigation_menu 0x7f0c002c
int layout design_navigation_menu_item 0x7f0c002d
int layout design_text_input_end_icon 0x7f0c002e
int layout design_text_input_start_icon 0x7f0c002f
int layout expand_button 0x7f0c0030
int layout floating_action_mode 0x7f0c0031
int layout floating_action_mode_item 0x7f0c0032
int layout image_frame 0x7f0c0033
int layout ime_base_split_test_activity 0x7f0c0034
int layout ime_secondary_split_test_activity 0x7f0c0035
int layout m3_alert_dialog 0x7f0c0036
int layout m3_alert_dialog_actions 0x7f0c0037
int layout m3_alert_dialog_title 0x7f0c0038
int layout m3_auto_complete_simple_item 0x7f0c0039
int layout m3_side_sheet_dialog 0x7f0c003a
int layout material_chip_input_combo 0x7f0c003b
int layout material_clock_display 0x7f0c003c
int layout material_clock_display_divider 0x7f0c003d
int layout material_clock_period_toggle 0x7f0c003e
int layout material_clock_period_toggle_land 0x7f0c003f
int layout material_clockface_textview 0x7f0c0040
int layout material_clockface_view 0x7f0c0041
int layout material_radial_view_group 0x7f0c0042
int layout material_textinput_timepicker 0x7f0c0043
int layout material_time_chip 0x7f0c0044
int layout material_time_input 0x7f0c0045
int layout material_timepicker 0x7f0c0046
int layout material_timepicker_dialog 0x7f0c0047
int layout material_timepicker_textinput_display 0x7f0c0048
int layout mtrl_alert_dialog 0x7f0c0049
int layout mtrl_alert_dialog_actions 0x7f0c004a
int layout mtrl_alert_dialog_title 0x7f0c004b
int layout mtrl_alert_select_dialog_item 0x7f0c004c
int layout mtrl_alert_select_dialog_multichoice 0x7f0c004d
int layout mtrl_alert_select_dialog_singlechoice 0x7f0c004e
int layout mtrl_auto_complete_simple_item 0x7f0c004f
int layout mtrl_calendar_day 0x7f0c0050
int layout mtrl_calendar_day_of_week 0x7f0c0051
int layout mtrl_calendar_days_of_week 0x7f0c0052
int layout mtrl_calendar_horizontal 0x7f0c0053
int layout mtrl_calendar_month 0x7f0c0054
int layout mtrl_calendar_month_labeled 0x7f0c0055
int layout mtrl_calendar_month_navigation 0x7f0c0056
int layout mtrl_calendar_months 0x7f0c0057
int layout mtrl_calendar_vertical 0x7f0c0058
int layout mtrl_calendar_year 0x7f0c0059
int layout mtrl_layout_snackbar 0x7f0c005a
int layout mtrl_layout_snackbar_include 0x7f0c005b
int layout mtrl_navigation_rail_item 0x7f0c005c
int layout mtrl_picker_actions 0x7f0c005d
int layout mtrl_picker_dialog 0x7f0c005e
int layout mtrl_picker_fullscreen 0x7f0c005f
int layout mtrl_picker_header_dialog 0x7f0c0060
int layout mtrl_picker_header_fullscreen 0x7f0c0061
int layout mtrl_picker_header_selection_text 0x7f0c0062
int layout mtrl_picker_header_title_text 0x7f0c0063
int layout mtrl_picker_header_toggle 0x7f0c0064
int layout mtrl_picker_text_input_date 0x7f0c0065
int layout mtrl_picker_text_input_date_range 0x7f0c0066
int layout mtrl_search_bar 0x7f0c0067
int layout mtrl_search_view 0x7f0c0068
int layout notification_action 0x7f0c0069
int layout notification_action_tombstone 0x7f0c006a
int layout notification_media_action 0x7f0c006b
int layout notification_media_cancel_action 0x7f0c006c
int layout notification_template_big_media 0x7f0c006d
int layout notification_template_big_media_custom 0x7f0c006e
int layout notification_template_big_media_narrow 0x7f0c006f
int layout notification_template_big_media_narrow_custom 0x7f0c0070
int layout notification_template_custom_big 0x7f0c0071
int layout notification_template_icon_group 0x7f0c0072
int layout notification_template_lines_media 0x7f0c0073
int layout notification_template_media 0x7f0c0074
int layout notification_template_media_custom 0x7f0c0075
int layout notification_template_part_chronometer 0x7f0c0076
int layout notification_template_part_time 0x7f0c0077
int layout paybutton_generic 0x7f0c0078
int layout preference 0x7f0c0079
int layout preference_category 0x7f0c007a
int layout preference_category_material 0x7f0c007b
int layout preference_dialog_edittext 0x7f0c007c
int layout preference_dropdown 0x7f0c007d
int layout preference_dropdown_material 0x7f0c007e
int layout preference_information 0x7f0c007f
int layout preference_information_material 0x7f0c0080
int layout preference_list_fragment 0x7f0c0081
int layout preference_material 0x7f0c0082
int layout preference_recyclerview 0x7f0c0083
int layout preference_widget_checkbox 0x7f0c0084
int layout preference_widget_seekbar 0x7f0c0085
int layout preference_widget_seekbar_material 0x7f0c0086
int layout preference_widget_switch 0x7f0c0087
int layout preference_widget_switch_compat 0x7f0c0088
int layout rzp_loader 0x7f0c0089
int layout rzp_magic_base 0x7f0c008a
int layout sdk_integration_status 0x7f0c008b
int layout select_dialog_item_material 0x7f0c008c
int layout select_dialog_multichoice_material 0x7f0c008d
int layout select_dialog_singlechoice_material 0x7f0c008e
int layout single_item 0x7f0c008f
int layout stripe_3ds2_transaction_layout 0x7f0c0090
int layout stripe_activity 0x7f0c0091
int layout stripe_activity_card_scan 0x7f0c0092
int layout stripe_add_payment_method_activity 0x7f0c0093
int layout stripe_add_payment_method_card_view 0x7f0c0094
int layout stripe_add_payment_method_row 0x7f0c0095
int layout stripe_address_widget 0x7f0c0096
int layout stripe_bank_item 0x7f0c0097
int layout stripe_bank_list_payment_method 0x7f0c0098
int layout stripe_becs_debit_widget 0x7f0c0099
int layout stripe_brand_zone_view 0x7f0c009a
int layout stripe_card_brand_choice_list_view 0x7f0c009b
int layout stripe_card_brand_spinner_dropdown 0x7f0c009c
int layout stripe_card_brand_spinner_main 0x7f0c009d
int layout stripe_card_brand_view 0x7f0c009e
int layout stripe_card_form_view 0x7f0c009f
int layout stripe_card_input_widget 0x7f0c00a0
int layout stripe_card_multiline_widget 0x7f0c00a1
int layout stripe_card_widget_progress_view 0x7f0c00a2
int layout stripe_challenge_activity 0x7f0c00a3
int layout stripe_challenge_fragment 0x7f0c00a4
int layout stripe_challenge_submit_dialog 0x7f0c00a5
int layout stripe_challenge_zone_multi_select_view 0x7f0c00a6
int layout stripe_challenge_zone_single_select_view 0x7f0c00a7
int layout stripe_challenge_zone_text_view 0x7f0c00a8
int layout stripe_challenge_zone_view 0x7f0c00a9
int layout stripe_challenge_zone_web_view 0x7f0c00aa
int layout stripe_country_dropdown_item 0x7f0c00ab
int layout stripe_country_text_view 0x7f0c00ac
int layout stripe_fragment_primary_button_container 0x7f0c00ad
int layout stripe_google_pay_button 0x7f0c00ae
int layout stripe_google_pay_row 0x7f0c00af
int layout stripe_hcaptcha_fragment 0x7f0c00b0
int layout stripe_horizontal_divider 0x7f0c00b1
int layout stripe_information_zone_view 0x7f0c00b2
int layout stripe_masked_card_row 0x7f0c00b3
int layout stripe_masked_card_view 0x7f0c00b4
int layout stripe_payment_auth_web_view_activity 0x7f0c00b5
int layout stripe_primary_button 0x7f0c00b6
int layout stripe_progress_view_layout 0x7f0c00b7
int layout stripe_react_native_google_pay_button 0x7f0c00b8
int layout stripe_select_card_brand_view 0x7f0c00b9
int layout stripe_shipping_info_page 0x7f0c00ba
int layout stripe_shipping_method_page 0x7f0c00bb
int layout stripe_shipping_method_view 0x7f0c00bc
int layout stripe_shipping_method_widget 0x7f0c00bd
int layout stripe_vertical_divider 0x7f0c00be
int layout support_simple_spinner_dropdown_item 0x7f0c00bf
int layout toast_custom 0x7f0c00c0
int layout wallet_test_layout 0x7f0c00c1
int menu menu_main 0x7f0e0000
int menu stripe_add_payment_method 0x7f0e0001
int menu stripe_payment_auth_web_view_menu 0x7f0e0002
int mipmap ic_launcher 0x7f0f0000
int mipmap launcher_icon 0x7f0f0001
int plurals mtrl_badge_content_description 0x7f100000
int plurals stripe_account_picker_cta_link 0x7f100001
int plurals stripe_account_picker_error_no_payment_method_desc 0x7f100002
int plurals stripe_success_pane_desc 0x7f100003
int plurals stripe_success_pane_desc_link_error 0x7f100004
int plurals stripe_success_pane_desc_link_success 0x7f100005
int raw firebase_common_keep 0x7f110000
int raw otpelf 0x7f110001
int raw rzp_config_checkout 0x7f110002
int string abc_action_bar_home_description 0x7f120000
int string abc_action_bar_up_description 0x7f120001
int string abc_action_menu_overflow_description 0x7f120002
int string abc_action_mode_done 0x7f120003
int string abc_activity_chooser_view_see_all 0x7f120004
int string abc_activitychooserview_choose_application 0x7f120005
int string abc_capital_off 0x7f120006
int string abc_capital_on 0x7f120007
int string abc_menu_alt_shortcut_label 0x7f120008
int string abc_menu_ctrl_shortcut_label 0x7f120009
int string abc_menu_delete_shortcut_label 0x7f12000a
int string abc_menu_enter_shortcut_label 0x7f12000b
int string abc_menu_function_shortcut_label 0x7f12000c
int string abc_menu_meta_shortcut_label 0x7f12000d
int string abc_menu_shift_shortcut_label 0x7f12000e
int string abc_menu_space_shortcut_label 0x7f12000f
int string abc_menu_sym_shortcut_label 0x7f120010
int string abc_prepend_shortcut_label 0x7f120011
int string abc_search_hint 0x7f120012
int string abc_searchview_description_clear 0x7f120013
int string abc_searchview_description_query 0x7f120014
int string abc_searchview_description_search 0x7f120015
int string abc_searchview_description_submit 0x7f120016
int string abc_searchview_description_voice 0x7f120017
int string abc_shareactionprovider_share_with 0x7f120018
int string abc_shareactionprovider_share_with_application 0x7f120019
int string abc_toolbar_collapse_description 0x7f12001a
int string action_close 0x7f12001b
int string action_go_back 0x7f12001c
int string action_go_forward 0x7f12001d
int string action_reload 0x7f12001e
int string action_share 0x7f12001f
int string androidx_startup 0x7f120020
int string appbar_scrolling_view_behavior 0x7f120021
int string bottom_sheet_behavior 0x7f120022
int string bottomsheet_action_collapse 0x7f120023
int string bottomsheet_action_expand 0x7f120024
int string bottomsheet_action_expand_halfway 0x7f120025
int string bottomsheet_drag_handle_clicked 0x7f120026
int string bottomsheet_drag_handle_content_description 0x7f120027
int string call_notification_answer_action 0x7f120028
int string call_notification_answer_video_action 0x7f120029
int string call_notification_decline_action 0x7f12002a
int string call_notification_hang_up_action 0x7f12002b
int string call_notification_incoming_text 0x7f12002c
int string call_notification_ongoing_text 0x7f12002d
int string call_notification_screening_text 0x7f12002e
int string character_counter_content_description 0x7f12002f
int string character_counter_overflowed_content_description 0x7f120030
int string character_counter_pattern 0x7f120031
int string clear_text_end_icon_content_description 0x7f120032
int string close_drawer 0x7f120033
int string close_sheet 0x7f120034
int string collapsed 0x7f120035
int string common_google_play_services_enable_button 0x7f120036
int string common_google_play_services_enable_text 0x7f120037
int string common_google_play_services_enable_title 0x7f120038
int string common_google_play_services_install_button 0x7f120039
int string common_google_play_services_install_text 0x7f12003a
int string common_google_play_services_install_title 0x7f12003b
int string common_google_play_services_notification_channel_name 0x7f12003c
int string common_google_play_services_notification_ticker 0x7f12003d
int string common_google_play_services_unknown_issue 0x7f12003e
int string common_google_play_services_unsupported_text 0x7f12003f
int string common_google_play_services_update_button 0x7f120040
int string common_google_play_services_update_text 0x7f120041
int string common_google_play_services_update_title 0x7f120042
int string common_google_play_services_updating_text 0x7f120043
int string common_google_play_services_wear_update_text 0x7f120044
int string common_open_on_phone 0x7f120045
int string common_signin_button_text 0x7f120046
int string common_signin_button_text_long 0x7f120047
int string copy 0x7f120048
int string copy_toast_msg 0x7f120049
int string default_error_message 0x7f12004a
int string default_popup_window_title 0x7f12004b
int string dialog 0x7f12004c
int string dropdown_menu 0x7f12004d
int string error_a11y_label 0x7f12004e
int string error_icon_content_description 0x7f12004f
int string expand_button_title 0x7f120050
int string expanded 0x7f120051
int string exposed_dropdown_menu_content_description 0x7f120052
int string fab_transformation_scrim_behavior 0x7f120053
int string fab_transformation_sheet_behavior 0x7f120054
int string fallback_menu_item_copy_link 0x7f120055
int string fallback_menu_item_open_in_browser 0x7f120056
int string fallback_menu_item_share_link 0x7f120057
int string fcm_fallback_notification_channel_label 0x7f120058
int string gpay_logo_description 0x7f120059
int string hide_bottom_view_on_scroll_behavior 0x7f12005a
int string icon_content_description 0x7f12005b
int string in_progress 0x7f12005c
int string indeterminate 0x7f12005d
int string item_view_role_description 0x7f12005e
int string m3_exceed_max_badge_text_suffix 0x7f12005f
int string m3_ref_typeface_brand_medium 0x7f120060
int string m3_ref_typeface_brand_regular 0x7f120061
int string m3_ref_typeface_plain_medium 0x7f120062
int string m3_ref_typeface_plain_regular 0x7f120063
int string m3_sys_motion_easing_emphasized 0x7f120064
int string m3_sys_motion_easing_emphasized_accelerate 0x7f120065
int string m3_sys_motion_easing_emphasized_decelerate 0x7f120066
int string m3_sys_motion_easing_emphasized_path_data 0x7f120067
int string m3_sys_motion_easing_legacy 0x7f120068
int string m3_sys_motion_easing_legacy_accelerate 0x7f120069
int string m3_sys_motion_easing_legacy_decelerate 0x7f12006a
int string m3_sys_motion_easing_linear 0x7f12006b
int string m3_sys_motion_easing_standard 0x7f12006c
int string m3_sys_motion_easing_standard_accelerate 0x7f12006d
int string m3_sys_motion_easing_standard_decelerate 0x7f12006e
int string material_clock_display_divider 0x7f12006f
int string material_clock_toggle_content_description 0x7f120070
int string material_hour_24h_suffix 0x7f120071
int string material_hour_selection 0x7f120072
int string material_hour_suffix 0x7f120073
int string material_minute_selection 0x7f120074
int string material_minute_suffix 0x7f120075
int string material_motion_easing_accelerated 0x7f120076
int string material_motion_easing_decelerated 0x7f120077
int string material_motion_easing_emphasized 0x7f120078
int string material_motion_easing_linear 0x7f120079
int string material_motion_easing_standard 0x7f12007a
int string material_slider_range_end 0x7f12007b
int string material_slider_range_start 0x7f12007c
int string material_slider_value 0x7f12007d
int string material_timepicker_am 0x7f12007e
int string material_timepicker_clock_mode_description 0x7f12007f
int string material_timepicker_hour 0x7f120080
int string material_timepicker_minute 0x7f120081
int string material_timepicker_pm 0x7f120082
int string material_timepicker_select_time 0x7f120083
int string material_timepicker_text_input_mode_description 0x7f120084
int string menu_search 0x7f120085
int string mtrl_badge_numberless_content_description 0x7f120086
int string mtrl_checkbox_button_icon_path_checked 0x7f120087
int string mtrl_checkbox_button_icon_path_group_name 0x7f120088
int string mtrl_checkbox_button_icon_path_indeterminate 0x7f120089
int string mtrl_checkbox_button_icon_path_name 0x7f12008a
int string mtrl_checkbox_button_path_checked 0x7f12008b
int string mtrl_checkbox_button_path_group_name 0x7f12008c
int string mtrl_checkbox_button_path_name 0x7f12008d
int string mtrl_checkbox_button_path_unchecked 0x7f12008e
int string mtrl_checkbox_state_description_checked 0x7f12008f
int string mtrl_checkbox_state_description_indeterminate 0x7f120090
int string mtrl_checkbox_state_description_unchecked 0x7f120091
int string mtrl_chip_close_icon_content_description 0x7f120092
int string mtrl_exceed_max_badge_number_content_description 0x7f120093
int string mtrl_exceed_max_badge_number_suffix 0x7f120094
int string mtrl_picker_a11y_next_month 0x7f120095
int string mtrl_picker_a11y_prev_month 0x7f120096
int string mtrl_picker_announce_current_range_selection 0x7f120097
int string mtrl_picker_announce_current_selection 0x7f120098
int string mtrl_picker_announce_current_selection_none 0x7f120099
int string mtrl_picker_cancel 0x7f12009a
int string mtrl_picker_confirm 0x7f12009b
int string mtrl_picker_date_header_selected 0x7f12009c
int string mtrl_picker_date_header_title 0x7f12009d
int string mtrl_picker_date_header_unselected 0x7f12009e
int string mtrl_picker_day_of_week_column_header 0x7f12009f
int string mtrl_picker_end_date_description 0x7f1200a0
int string mtrl_picker_invalid_format 0x7f1200a1
int string mtrl_picker_invalid_format_example 0x7f1200a2
int string mtrl_picker_invalid_format_use 0x7f1200a3
int string mtrl_picker_invalid_range 0x7f1200a4
int string mtrl_picker_navigate_to_current_year_description 0x7f1200a5
int string mtrl_picker_navigate_to_year_description 0x7f1200a6
int string mtrl_picker_out_of_range 0x7f1200a7
int string mtrl_picker_range_header_only_end_selected 0x7f1200a8
int string mtrl_picker_range_header_only_start_selected 0x7f1200a9
int string mtrl_picker_range_header_selected 0x7f1200aa
int string mtrl_picker_range_header_title 0x7f1200ab
int string mtrl_picker_range_header_unselected 0x7f1200ac
int string mtrl_picker_save 0x7f1200ad
int string mtrl_picker_start_date_description 0x7f1200ae
int string mtrl_picker_text_input_date_hint 0x7f1200af
int string mtrl_picker_text_input_date_range_end_hint 0x7f1200b0
int string mtrl_picker_text_input_date_range_start_hint 0x7f1200b1
int string mtrl_picker_text_input_day_abbr 0x7f1200b2
int string mtrl_picker_text_input_month_abbr 0x7f1200b3
int string mtrl_picker_text_input_year_abbr 0x7f1200b4
int string mtrl_picker_today_description 0x7f1200b5
int string mtrl_picker_toggle_to_calendar_input_mode 0x7f1200b6
int string mtrl_picker_toggle_to_day_selection 0x7f1200b7
int string mtrl_picker_toggle_to_text_input_mode 0x7f1200b8
int string mtrl_picker_toggle_to_year_selection 0x7f1200b9
int string mtrl_switch_thumb_group_name 0x7f1200ba
int string mtrl_switch_thumb_path_checked 0x7f1200bb
int string mtrl_switch_thumb_path_morphing 0x7f1200bc
int string mtrl_switch_thumb_path_name 0x7f1200bd
int string mtrl_switch_thumb_path_pressed 0x7f1200be
int string mtrl_switch_thumb_path_unchecked 0x7f1200bf
int string mtrl_switch_track_decoration_path 0x7f1200c0
int string mtrl_switch_track_path 0x7f1200c1
int string mtrl_timepicker_cancel 0x7f1200c2
int string mtrl_timepicker_confirm 0x7f1200c3
int string navigation_menu 0x7f1200c4
int string not_selected 0x7f1200c5
int string not_set 0x7f1200c6
int string off 0x7f1200c7
int string on 0x7f1200c8
int string password_toggle_content_description 0x7f1200c9
int string path_password_eye 0x7f1200ca
int string path_password_eye_mask_strike_through 0x7f1200cb
int string path_password_eye_mask_visible 0x7f1200cc
int string path_password_strike_through 0x7f1200cd
int string preference_copied 0x7f1200ce
int string range_end 0x7f1200cf
int string range_start 0x7f1200d0
int string search_menu_title 0x7f1200d1
int string searchbar_scrolling_view_behavior 0x7f1200d2
int string searchview_clear_text_content_description 0x7f1200d3
int string searchview_navigation_content_description 0x7f1200d4
int string selected 0x7f1200d5
int string side_sheet_accessibility_pane_title 0x7f1200d6
int string side_sheet_behavior 0x7f1200d7
int string status_bar_notification_info_overflow 0x7f1200d8
int string stripe_3ds2_brand_amex 0x7f1200d9
int string stripe_3ds2_brand_cartesbancaires 0x7f1200da
int string stripe_3ds2_brand_discover 0x7f1200db
int string stripe_3ds2_brand_mastercard 0x7f1200dc
int string stripe_3ds2_brand_unionpay 0x7f1200dd
int string stripe_3ds2_brand_visa 0x7f1200de
int string stripe_3ds2_bzv_issuer_image_description 0x7f1200df
int string stripe_3ds2_bzv_payment_system_image_description 0x7f1200e0
int string stripe_3ds2_czv_whitelist_no_label 0x7f1200e1
int string stripe_3ds2_czv_whitelist_yes_label 0x7f1200e2
int string stripe_3ds2_hzv_cancel_label 0x7f1200e3
int string stripe_3ds2_hzv_header_label 0x7f1200e4
int string stripe_3ds2_processing 0x7f1200e5
int string stripe_acc_label_card_number 0x7f1200e6
int string stripe_acc_label_card_number_node 0x7f1200e7
int string stripe_acc_label_cvc_node 0x7f1200e8
int string stripe_acc_label_expiry_date 0x7f1200e9
int string stripe_acc_label_expiry_date_node 0x7f1200ea
int string stripe_acc_label_zip 0x7f1200eb
int string stripe_acc_label_zip_short 0x7f1200ec
int string stripe_account_picker_error_no_account_available_title 0x7f1200ed
int string stripe_account_picker_error_no_payment_method_title 0x7f1200ee
int string stripe_account_picker_multiselect_account 0x7f1200ef
int string stripe_account_picker_singleselect_account 0x7f1200f0
int string stripe_accounts_error_desc_manualentry 0x7f1200f1
int string stripe_accounts_error_desc_no_retry 0x7f1200f2
int string stripe_accounts_error_desc_retry 0x7f1200f3
int string stripe_add_bank_account 0x7f1200f4
int string stripe_add_new_payment_method 0x7f1200f5
int string stripe_add_payment_method 0x7f1200f6
int string stripe_added 0x7f1200f7
int string stripe_address_city_required 0x7f1200f8
int string stripe_address_country_invalid 0x7f1200f9
int string stripe_address_county_required 0x7f1200fa
int string stripe_address_label_address 0x7f1200fb
int string stripe_address_label_address_line1 0x7f1200fc
int string stripe_address_label_address_line1_optional 0x7f1200fd
int string stripe_address_label_address_line2 0x7f1200fe
int string stripe_address_label_address_line2_optional 0x7f1200ff
int string stripe_address_label_address_optional 0x7f120100
int string stripe_address_label_ae_emirate 0x7f120101
int string stripe_address_label_apt_optional 0x7f120102
int string stripe_address_label_au_suburb_or_city 0x7f120103
int string stripe_address_label_bb_jm_parish 0x7f120104
int string stripe_address_label_cedex 0x7f120105
int string stripe_address_label_city 0x7f120106
int string stripe_address_label_city_optional 0x7f120107
int string stripe_address_label_country 0x7f120108
int string stripe_address_label_country_or_region 0x7f120109
int string stripe_address_label_county 0x7f12010a
int string stripe_address_label_county_optional 0x7f12010b
int string stripe_address_label_department 0x7f12010c
int string stripe_address_label_district 0x7f12010d
int string stripe_address_label_full_name 0x7f12010e
int string stripe_address_label_hk_area 0x7f12010f
int string stripe_address_label_ie_eircode 0x7f120110
int string stripe_address_label_ie_townland 0x7f120111
int string stripe_address_label_in_pin 0x7f120112
int string stripe_address_label_island 0x7f120113
int string stripe_address_label_jp_prefecture 0x7f120114
int string stripe_address_label_kr_do_si 0x7f120115
int string stripe_address_label_name 0x7f120116
int string stripe_address_label_neighborhood 0x7f120117
int string stripe_address_label_oblast 0x7f120118
int string stripe_address_label_phone_number 0x7f120119
int string stripe_address_label_phone_number_optional 0x7f12011a
int string stripe_address_label_post_town 0x7f12011b
int string stripe_address_label_postal_code 0x7f12011c
int string stripe_address_label_postal_code_optional 0x7f12011d
int string stripe_address_label_postcode 0x7f12011e
int string stripe_address_label_postcode_optional 0x7f12011f
int string stripe_address_label_province 0x7f120120
int string stripe_address_label_province_optional 0x7f120121
int string stripe_address_label_region_generic 0x7f120122
int string stripe_address_label_region_generic_optional 0x7f120123
int string stripe_address_label_state 0x7f120124
int string stripe_address_label_state_optional 0x7f120125
int string stripe_address_label_suburb 0x7f120126
int string stripe_address_label_village_township 0x7f120127
int string stripe_address_label_zip_code 0x7f120128
int string stripe_address_label_zip_code_optional 0x7f120129
int string stripe_address_label_zip_postal_code 0x7f12012a
int string stripe_address_label_zip_postal_code_optional 0x7f12012b
int string stripe_address_name_required 0x7f12012c
int string stripe_address_phone_number_required 0x7f12012d
int string stripe_address_postal_code_invalid 0x7f12012e
int string stripe_address_postcode_invalid 0x7f12012f
int string stripe_address_province_required 0x7f120130
int string stripe_address_region_generic_required 0x7f120131
int string stripe_address_required 0x7f120132
int string stripe_address_search_content_description 0x7f120133
int string stripe_address_state_required 0x7f120134
int string stripe_address_zip_invalid 0x7f120135
int string stripe_address_zip_postal_invalid 0x7f120136
int string stripe_affirm_buy_now_pay_later 0x7f120137
int string stripe_affirm_buy_now_pay_later_plaintext 0x7f120138
int string stripe_afterpay_clearpay_marketing 0x7f120139
int string stripe_afterpay_clearpay_message 0x7f12013a
int string stripe_afterpay_clearpay_subtitle 0x7f12013b
int string stripe_afterpay_subtitle 0x7f12013c
int string stripe_amazon_pay_mandate 0x7f12013d
int string stripe_attachlinkedpaymentaccount_error_desc 0x7f12013e
int string stripe_attachlinkedpaymentaccount_error_desc_manual_entry 0x7f12013f
int string stripe_attachlinkedpaymentaccount_error_title 0x7f120140
int string stripe_au_becs_account_name 0x7f120141
int string stripe_au_becs_bsb_number 0x7f120142
int string stripe_au_becs_mandate 0x7f120143
int string stripe_back 0x7f120144
int string stripe_bacs_account_number 0x7f120145
int string stripe_bacs_account_number_incomplete 0x7f120146
int string stripe_bacs_bank_account_title 0x7f120147
int string stripe_bacs_confirm_mandate_label 0x7f120148
int string stripe_bacs_sort_code 0x7f120149
int string stripe_bacs_sort_code_incomplete 0x7f12014a
int string stripe_bank_account_ending_in 0x7f12014b
int string stripe_bank_account_plus_last_4 0x7f12014c
int string stripe_bank_account_with_last_4 0x7f12014d
int string stripe_becs_mandate_acceptance 0x7f12014e
int string stripe_becs_widget_account_number 0x7f12014f
int string stripe_becs_widget_account_number_incomplete 0x7f120150
int string stripe_becs_widget_account_number_required 0x7f120151
int string stripe_becs_widget_bsb 0x7f120152
int string stripe_becs_widget_bsb_incomplete 0x7f120153
int string stripe_becs_widget_bsb_invalid 0x7f120154
int string stripe_becs_widget_email 0x7f120155
int string stripe_becs_widget_email_invalid 0x7f120156
int string stripe_becs_widget_email_required 0x7f120157
int string stripe_becs_widget_name 0x7f120158
int string stripe_becs_widget_name_required 0x7f120159
int string stripe_billing_details 0x7f12015a
int string stripe_billing_same_as_shipping 0x7f12015b
int string stripe_blank_and_required 0x7f12015c
int string stripe_blik_code 0x7f12015d
int string stripe_blik_confirm_payment 0x7f12015e
int string stripe_boleto_tax_id_label 0x7f12015f
int string stripe_cancel 0x7f120160
int string stripe_card_brand_choice_no_selection 0x7f120161
int string stripe_card_brand_choice_selection_header 0x7f120162
int string stripe_card_brand_not_accepted 0x7f120163
int string stripe_card_brand_not_accepted_with_brand 0x7f120164
int string stripe_card_declined 0x7f120165
int string stripe_card_ending_in 0x7f120166
int string stripe_card_number_hint 0x7f120167
int string stripe_card_plus_last_4 0x7f120168
int string stripe_card_with_last_4 0x7f120169
int string stripe_cash_app_pay_mandate 0x7f12016a
int string stripe_change 0x7f12016b
int string stripe_clearpay_subtitle 0x7f12016c
int string stripe_close 0x7f12016d
int string stripe_close_dialog_networking_desc 0x7f12016e
int string stripe_close_dialog_networking_desc_no_business 0x7f12016f
int string stripe_confirm_close_form_body 0x7f120170
int string stripe_confirm_close_form_title 0x7f120171
int string stripe_contact_information 0x7f120172
int string stripe_continue_button_label 0x7f120173
int string stripe_cvc_amex_hint 0x7f120174
int string stripe_cvc_multiline_helper 0x7f120175
int string stripe_cvc_multiline_helper_amex 0x7f120176
int string stripe_cvc_number_hint 0x7f120177
int string stripe_delete_payment_method 0x7f120178
int string stripe_delete_payment_method_prompt_title 0x7f120179
int string stripe_disallowed_card_brand 0x7f12017a
int string stripe_done 0x7f12017b
int string stripe_edit 0x7f12017c
int string stripe_email 0x7f12017d
int string stripe_email_is_invalid 0x7f12017e
int string stripe_eps_bank 0x7f12017f
int string stripe_error_cta_close 0x7f120180
int string stripe_error_cta_manual_entry 0x7f120181
int string stripe_error_cta_retry 0x7f120182
int string stripe_error_cta_select_another_bank 0x7f120183
int string stripe_error_generic_desc 0x7f120184
int string stripe_error_generic_title 0x7f120185
int string stripe_error_planned_downtime_desc 0x7f120186
int string stripe_error_planned_downtime_title 0x7f120187
int string stripe_error_unplanned_downtime_desc 0x7f120188
int string stripe_error_unplanned_downtime_title 0x7f120189
int string stripe_exit_modal_cta_accept 0x7f12018a
int string stripe_exit_modal_cta_cancel 0x7f12018b
int string stripe_exit_modal_desc 0x7f12018c
int string stripe_exit_modal_desc_no_business 0x7f12018d
int string stripe_exit_modal_title 0x7f12018e
int string stripe_expiration_date_allowlist 0x7f12018f
int string stripe_expiration_date_content_description 0x7f120190
int string stripe_expiration_date_empty_content_description 0x7f120191
int string stripe_expiration_date_hint 0x7f120192
int string stripe_expiration_date_month_complete_content_description 0x7f120193
int string stripe_expiration_date_year_incomplete_content_description 0x7f120194
int string stripe_expired_card 0x7f120195
int string stripe_expiry_date_hint 0x7f120196
int string stripe_expiry_label_short 0x7f120197
int string stripe_failure_connection_error 0x7f120198
int string stripe_failure_reason_authentication 0x7f120199
int string stripe_failure_reason_timed_out 0x7f12019a
int string stripe_field_required 0x7f12019b
int string stripe_form_label_optional 0x7f12019c
int string stripe_fpx_bank 0x7f12019d
int string stripe_fpx_bank_offline 0x7f12019e
int string stripe_generic_decline 0x7f12019f
int string stripe_google_pay 0x7f1201a0
int string stripe_hcaptcha_loading_text 0x7f1201a1
int string stripe_hcaptcha_logo_description 0x7f1201a2
int string stripe_iban 0x7f1201a3
int string stripe_iban_incomplete 0x7f1201a4
int string stripe_iban_invalid_country 0x7f1201a5
int string stripe_iban_invalid_start 0x7f1201a6
int string stripe_ideal_bank 0x7f1201a7
int string stripe_incomplete_blik_code 0x7f1201a8
int string stripe_incomplete_expiry_date 0x7f1201a9
int string stripe_incomplete_phone_number 0x7f1201aa
int string stripe_inline_sign_up_header 0x7f1201ab
int string stripe_institutionpicker_manual_entry_desc 0x7f1201ac
int string stripe_institutionpicker_manual_entry_title 0x7f1201ad
int string stripe_institutionpicker_pane_error_desc 0x7f1201ae
int string stripe_institutionpicker_pane_error_desc_manual_entry 0x7f1201af
int string stripe_institutionpicker_pane_error_title 0x7f1201b0
int string stripe_institutionpicker_pane_select_bank 0x7f1201b1
int string stripe_institutionpicker_search_more_title 0x7f1201b2
int string stripe_internal_error 0x7f1201b3
int string stripe_invalid_bank_account_iban 0x7f1201b4
int string stripe_invalid_blik_code 0x7f1201b5
int string stripe_invalid_card_number 0x7f1201b6
int string stripe_invalid_cvc 0x7f1201b7
int string stripe_invalid_email_address 0x7f1201b8
int string stripe_invalid_expiry_month 0x7f1201b9
int string stripe_invalid_expiry_year 0x7f1201ba
int string stripe_invalid_owner_name 0x7f1201bb
int string stripe_invalid_shipping_information 0x7f1201bc
int string stripe_invalid_upi_id 0x7f1201bd
int string stripe_invalid_zip 0x7f1201be
int string stripe_klarna_buy_now_pay_later 0x7f1201bf
int string stripe_klarna_mandate 0x7f1201c0
int string stripe_klarna_pay_later 0x7f1201c1
int string stripe_konbini_confirmation_number_label 0x7f1201c2
int string stripe_link 0x7f1201c3
int string stripe_link_simple_secure_payments 0x7f1201c4
int string stripe_link_stepup_verification_desc 0x7f1201c5
int string stripe_link_stepup_verification_resend_code 0x7f1201c6
int string stripe_link_stepup_verification_title 0x7f1201c7
int string stripe_loading_pill_label 0x7f1201c8
int string stripe_log_out 0x7f1201c9
int string stripe_manualentry_account 0x7f1201ca
int string stripe_manualentry_accountconfirm 0x7f1201cb
int string stripe_manualentry_cta 0x7f1201cc
int string stripe_manualentry_microdeposits_desc 0x7f1201cd
int string stripe_manualentry_routing 0x7f1201ce
int string stripe_manualentry_test_banner 0x7f1201cf
int string stripe_manualentry_title 0x7f1201d0
int string stripe_name_on_card 0x7f1201d1
int string stripe_network_error_message 0x7f1201d2
int string stripe_networking_link_login_warmup_cta_cancel 0x7f1201d3
int string stripe_networking_link_login_warmup_cta_continue 0x7f1201d4
int string stripe_networking_link_login_warmup_cta_skip 0x7f1201d5
int string stripe_networking_link_login_warmup_description 0x7f1201d6
int string stripe_networking_link_login_warmup_title 0x7f1201d7
int string stripe_networking_save_to_link_verification_cta_negative 0x7f1201d8
int string stripe_networking_save_to_link_verification_title 0x7f1201d9
int string stripe_networking_signup_email_label 0x7f1201da
int string stripe_networking_verification_desc 0x7f1201db
int string stripe_networking_verification_title 0x7f1201dc
int string stripe_p24_bank 0x7f1201dd
int string stripe_pay_button_amount 0x7f1201de
int string stripe_pay_with_link 0x7f1201df
int string stripe_payment_method_add_new_card 0x7f1201e0
int string stripe_payment_method_add_new_fpx 0x7f1201e1
int string stripe_payment_method_bank 0x7f1201e2
int string stripe_paymentsheet_ach_continue_mandate 0x7f1201e3
int string stripe_paymentsheet_ach_save_mandate 0x7f1201e4
int string stripe_paymentsheet_ach_something_went_wrong 0x7f1201e5
int string stripe_paymentsheet_add_card 0x7f1201e6
int string stripe_paymentsheet_add_new_card 0x7f1201e7
int string stripe_paymentsheet_add_payment_method_button_label 0x7f1201e8
int string stripe_paymentsheet_add_payment_method_card_information 0x7f1201e9
int string stripe_paymentsheet_add_payment_method_title 0x7f1201ea
int string stripe_paymentsheet_add_us_bank_account 0x7f1201eb
int string stripe_paymentsheet_address_element_primary_button 0x7f1201ec
int string stripe_paymentsheet_address_element_shipping_address 0x7f1201ed
int string stripe_paymentsheet_bacs_email_mandate 0x7f1201ee
int string stripe_paymentsheet_bacs_guarantee 0x7f1201ef
int string stripe_paymentsheet_bacs_guarantee_format 0x7f1201f0
int string stripe_paymentsheet_bacs_guarantee_url 0x7f1201f1
int string stripe_paymentsheet_bacs_mandate_title 0x7f1201f2
int string stripe_paymentsheet_bacs_modify_details_button_label 0x7f1201f3
int string stripe_paymentsheet_bacs_notice_default_payer 0x7f1201f4
int string stripe_paymentsheet_bacs_notice_mandate 0x7f1201f5
int string stripe_paymentsheet_bacs_protection_mandate 0x7f1201f6
int string stripe_paymentsheet_bacs_support_address_format 0x7f1201f7
int string stripe_paymentsheet_bacs_support_default_address_line_one 0x7f1201f8
int string stripe_paymentsheet_bacs_support_default_address_line_two 0x7f1201f9
int string stripe_paymentsheet_bacs_support_default_email 0x7f1201fa
int string stripe_paymentsheet_bank_account_details_cannot_be_changed 0x7f1201fb
int string stripe_paymentsheet_bank_account_info 0x7f1201fc
int string stripe_paymentsheet_bank_account_last_4 0x7f1201fd
int string stripe_paymentsheet_bank_payment_promo_for_payment 0x7f1201fe
int string stripe_paymentsheet_bank_payment_promo_for_setup 0x7f1201ff
int string stripe_paymentsheet_bank_payment_promo_ineligible 0x7f120200
int string stripe_paymentsheet_buy_using_upi_id 0x7f120201
int string stripe_paymentsheet_card_details_cannot_be_changed 0x7f120202
int string stripe_paymentsheet_card_mandate 0x7f120203
int string stripe_paymentsheet_card_updates_failed_error_message 0x7f120204
int string stripe_paymentsheet_choose_payment_method 0x7f120205
int string stripe_paymentsheet_close 0x7f120206
int string stripe_paymentsheet_confirm 0x7f120207
int string stripe_paymentsheet_confirm_your_cvc 0x7f120208
int string stripe_paymentsheet_enter_address_manually 0x7f120209
int string stripe_paymentsheet_iban 0x7f12020a
int string stripe_paymentsheet_invalid_deferred_intent_usage 0x7f12020b
int string stripe_paymentsheet_manage_bank_account 0x7f12020c
int string stripe_paymentsheet_manage_card 0x7f12020d
int string stripe_paymentsheet_manage_payment_methods 0x7f12020e
int string stripe_paymentsheet_manage_sepa_debit 0x7f12020f
int string stripe_paymentsheet_manage_your_payment_methods 0x7f120210
int string stripe_paymentsheet_microdeposit 0x7f120211
int string stripe_paymentsheet_modify_pm 0x7f120212
int string stripe_paymentsheet_new_card 0x7f120213
int string stripe_paymentsheet_new_pm 0x7f120214
int string stripe_paymentsheet_only_card_brand_can_be_changed 0x7f120215
int string stripe_paymentsheet_or_pay_using 0x7f120216
int string stripe_paymentsheet_or_pay_with_card 0x7f120217
int string stripe_paymentsheet_or_use 0x7f120218
int string stripe_paymentsheet_or_use_a_card 0x7f120219
int string stripe_paymentsheet_pay_button_label 0x7f12021a
int string stripe_paymentsheet_pay_using 0x7f12021b
int string stripe_paymentsheet_pay_with_bank_title 0x7f12021c
int string stripe_paymentsheet_payment_method_affirm 0x7f12021d
int string stripe_paymentsheet_payment_method_afterpay 0x7f12021e
int string stripe_paymentsheet_payment_method_alipay 0x7f12021f
int string stripe_paymentsheet_payment_method_alma 0x7f120220
int string stripe_paymentsheet_payment_method_amazon_pay 0x7f120221
int string stripe_paymentsheet_payment_method_au_becs_debit 0x7f120222
int string stripe_paymentsheet_payment_method_bacs_debit 0x7f120223
int string stripe_paymentsheet_payment_method_bancontact 0x7f120224
int string stripe_paymentsheet_payment_method_billie 0x7f120225
int string stripe_paymentsheet_payment_method_blik 0x7f120226
int string stripe_paymentsheet_payment_method_boleto 0x7f120227
int string stripe_paymentsheet_payment_method_card 0x7f120228
int string stripe_paymentsheet_payment_method_cashapp 0x7f120229
int string stripe_paymentsheet_payment_method_clearpay 0x7f12022a
int string stripe_paymentsheet_payment_method_crypto 0x7f12022b
int string stripe_paymentsheet_payment_method_eps 0x7f12022c
int string stripe_paymentsheet_payment_method_fpx 0x7f12022d
int string stripe_paymentsheet_payment_method_giropay 0x7f12022e
int string stripe_paymentsheet_payment_method_grabpay 0x7f12022f
int string stripe_paymentsheet_payment_method_ideal 0x7f120230
int string stripe_paymentsheet_payment_method_instant_debits 0x7f120231
int string stripe_paymentsheet_payment_method_item_card_number 0x7f120232
int string stripe_paymentsheet_payment_method_klarna 0x7f120233
int string stripe_paymentsheet_payment_method_konbini 0x7f120234
int string stripe_paymentsheet_payment_method_mobile_pay 0x7f120235
int string stripe_paymentsheet_payment_method_multibanco 0x7f120236
int string stripe_paymentsheet_payment_method_oxxo 0x7f120237
int string stripe_paymentsheet_payment_method_p24 0x7f120238
int string stripe_paymentsheet_payment_method_paypal 0x7f120239
int string stripe_paymentsheet_payment_method_revolut_pay 0x7f12023a
int string stripe_paymentsheet_payment_method_satispay 0x7f12023b
int string stripe_paymentsheet_payment_method_sepa_debit 0x7f12023c
int string stripe_paymentsheet_payment_method_sofort 0x7f12023d
int string stripe_paymentsheet_payment_method_sunbit 0x7f12023e
int string stripe_paymentsheet_payment_method_swish 0x7f12023f
int string stripe_paymentsheet_payment_method_twint 0x7f120240
int string stripe_paymentsheet_payment_method_upi 0x7f120241
int string stripe_paymentsheet_payment_method_us_bank_account 0x7f120242
int string stripe_paymentsheet_payment_method_wechat 0x7f120243
int string stripe_paymentsheet_payment_method_zip 0x7f120244
int string stripe_paymentsheet_primary_button_processing 0x7f120245
int string stripe_paymentsheet_remove_bank_account_question_title 0x7f120246
int string stripe_paymentsheet_remove_bank_account_title 0x7f120247
int string stripe_paymentsheet_remove_card 0x7f120248
int string stripe_paymentsheet_remove_card_title 0x7f120249
int string stripe_paymentsheet_remove_pm 0x7f12024a
int string stripe_paymentsheet_remove_pm_title 0x7f12024b
int string stripe_paymentsheet_save 0x7f12024c
int string stripe_paymentsheet_save_a_new_payment_method 0x7f12024d
int string stripe_paymentsheet_save_bank_title 0x7f12024e
int string stripe_paymentsheet_save_for_future_payments 0x7f12024f
int string stripe_paymentsheet_save_this_card_with_merchant_name 0x7f120250
int string stripe_paymentsheet_saved 0x7f120251
int string stripe_paymentsheet_select_payment_method 0x7f120252
int string stripe_paymentsheet_select_your_payment_method 0x7f120253
int string stripe_paymentsheet_sepa_debit_details_cannot_be_changed 0x7f120254
int string stripe_paymentsheet_set_default_payment_method_failed_error_message 0x7f120255
int string stripe_paymentsheet_test_mode_indicator 0x7f120256
int string stripe_paymentsheet_total_amount 0x7f120257
int string stripe_paymentsheet_update_card_brand_failed_error_message 0x7f120258
int string stripe_paypal_mandate 0x7f120259
int string stripe_pm_set_as_default 0x7f12025a
int string stripe_postalcode_placeholder 0x7f12025b
int string stripe_prepane_cancel_cta 0x7f12025c
int string stripe_prepane_choose_different_bank_cta 0x7f12025d
int string stripe_price_free 0x7f12025e
int string stripe_processing_error 0x7f12025f
int string stripe_remove 0x7f120260
int string stripe_remove_bank_account_ending_in 0x7f120261
int string stripe_removed 0x7f120262
int string stripe_revolut_mandate 0x7f120263
int string stripe_save_for_future_payments_with_merchant_name 0x7f120264
int string stripe_save_payment_details_to_merchant_name 0x7f120265
int string stripe_scan_card 0x7f120266
int string stripe_search 0x7f120267
int string stripe_secure_checkout 0x7f120268
int string stripe_sepa_mandate 0x7f120269
int string stripe_set_as_default_payment_method 0x7f12026a
int string stripe_setup_button_label 0x7f12026b
int string stripe_show_menu 0x7f12026c
int string stripe_sign_up 0x7f12026d
int string stripe_sign_up_header 0x7f12026e
int string stripe_sign_up_message 0x7f12026f
int string stripe_sign_up_terms 0x7f120270
int string stripe_sign_up_terms_alternative 0x7f120271
int string stripe_sign_up_terms_alternative_with_phone_number 0x7f120272
int string stripe_signup_deactivated_account_message 0x7f120273
int string stripe_something_went_wrong 0x7f120274
int string stripe_success_pane_desc_microdeposits 0x7f120275
int string stripe_success_pane_done 0x7f120276
int string stripe_success_pane_done_with_merchant 0x7f120277
int string stripe_success_pane_title 0x7f120278
int string stripe_success_pane_title_microdeposits 0x7f120279
int string stripe_title_add_a_card 0x7f12027a
int string stripe_title_add_an_address 0x7f12027b
int string stripe_title_bank_account 0x7f12027c
int string stripe_title_payment_method 0x7f12027d
int string stripe_title_select_shipping_method 0x7f12027e
int string stripe_title_update_card 0x7f12027f
int string stripe_update 0x7f120280
int string stripe_upi_id_label 0x7f120281
int string stripe_upi_polling_cancel 0x7f120282
int string stripe_upi_polling_header 0x7f120283
int string stripe_upi_polling_message 0x7f120284
int string stripe_upi_polling_payment_failed_message 0x7f120285
int string stripe_upi_polling_payment_failed_title 0x7f120286
int string stripe_validation_account_confirm_mismatch 0x7f120287
int string stripe_validation_account_required 0x7f120288
int string stripe_validation_account_too_long 0x7f120289
int string stripe_validation_no_us_routing 0x7f12028a
int string stripe_validation_routing_required 0x7f12028b
int string stripe_validation_routing_too_short 0x7f12028c
int string stripe_verification_codeExpiredEmail 0x7f12028d
int string stripe_verification_codeExpiredSms 0x7f12028e
int string stripe_verification_codeInvalid 0x7f12028f
int string stripe_verification_inTestMode 0x7f120290
int string stripe_verification_useTestCode 0x7f120291
int string stripe_verification_change_email 0x7f120292
int string stripe_verification_code_sent 0x7f120293
int string stripe_verification_dialog_header 0x7f120294
int string stripe_verification_header 0x7f120295
int string stripe_verification_header_inline 0x7f120296
int string stripe_verification_header_prefilled 0x7f120297
int string stripe_verification_message 0x7f120298
int string stripe_verification_not_email 0x7f120299
int string stripe_verification_resend 0x7f12029a
int string stripe_verify_your_payment 0x7f12029b
int string stripe_view_more 0x7f12029c
int string stripe_wallet_bank_account_terms 0x7f12029d
int string stripe_wallet_collapsed_payment 0x7f12029e
int string stripe_wallet_default 0x7f12029f
int string stripe_wallet_expand_accessibility 0x7f1202a0
int string stripe_wallet_expanded_title 0x7f1202a1
int string stripe_wallet_last4_prefix 0x7f1202a2
int string stripe_wallet_passthrough_description 0x7f1202a3
int string stripe_wallet_pay_another_way 0x7f1202a4
int string stripe_wallet_recollect_cvc_error 0x7f1202a5
int string stripe_wallet_remove_account_confirmation 0x7f1202a6
int string stripe_wallet_remove_card_confirmation 0x7f1202a7
int string stripe_wallet_remove_linked_account 0x7f1202a8
int string stripe_wallet_set_as_default 0x7f1202a9
int string stripe_wallet_unavailable 0x7f1202aa
int string stripe_wallet_update_card 0x7f1202ab
int string stripe_wallet_update_expired_card_error 0x7f1202ac
int string summary_collapsed_preference_list 0x7f1202ad
int string switch_role 0x7f1202ae
int string tab 0x7f1202af
int string template_percent 0x7f1202b0
int string tooltip_description 0x7f1202b1
int string tooltip_label 0x7f1202b2
int string v7_preference_off 0x7f1202b3
int string v7_preference_on 0x7f1202b4
int string wallet_buy_button_place_holder 0x7f1202b5
int style AlertDialog_AppCompat 0x7f130000
int style AlertDialog_AppCompat_Light 0x7f130001
int style Animation_AppCompat_Dialog 0x7f130002
int style Animation_AppCompat_DropDownUp 0x7f130003
int style Animation_AppCompat_Tooltip 0x7f130004
int style Animation_Design_BottomSheetDialog 0x7f130005
int style Animation_Material3_BottomSheetDialog 0x7f130006
int style Animation_Material3_SideSheetDialog 0x7f130007
int style Animation_Material3_SideSheetDialog_Left 0x7f130008
int style Animation_Material3_SideSheetDialog_Right 0x7f130009
int style Animation_MaterialComponents_BottomSheetDialog 0x7f13000a
int style AppTheme 0x7f13000b
int style Base_AlertDialog_AppCompat 0x7f13000c
int style Base_AlertDialog_AppCompat_Light 0x7f13000d
int style Base_Animation_AppCompat_Dialog 0x7f13000e
int style Base_Animation_AppCompat_DropDownUp 0x7f13000f
int style Base_Animation_AppCompat_Tooltip 0x7f130010
int style Base_CardView 0x7f130011
int style Base_DialogWindowTitle_AppCompat 0x7f130012
int style Base_DialogWindowTitleBackground_AppCompat 0x7f130013
int style Base_MaterialAlertDialog_MaterialComponents_Title_Icon 0x7f130014
int style Base_MaterialAlertDialog_MaterialComponents_Title_Panel 0x7f130015
int style Base_MaterialAlertDialog_MaterialComponents_Title_Text 0x7f130016
int style Base_TextAppearance_AppCompat 0x7f130017
int style Base_TextAppearance_AppCompat_Body1 0x7f130018
int style Base_TextAppearance_AppCompat_Body2 0x7f130019
int style Base_TextAppearance_AppCompat_Button 0x7f13001a
int style Base_TextAppearance_AppCompat_Caption 0x7f13001b
int style Base_TextAppearance_AppCompat_Display1 0x7f13001c
int style Base_TextAppearance_AppCompat_Display2 0x7f13001d
int style Base_TextAppearance_AppCompat_Display3 0x7f13001e
int style Base_TextAppearance_AppCompat_Display4 0x7f13001f
int style Base_TextAppearance_AppCompat_Headline 0x7f130020
int style Base_TextAppearance_AppCompat_Inverse 0x7f130021
int style Base_TextAppearance_AppCompat_Large 0x7f130022
int style Base_TextAppearance_AppCompat_Large_Inverse 0x7f130023
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f130024
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f130025
int style Base_TextAppearance_AppCompat_Medium 0x7f130026
int style Base_TextAppearance_AppCompat_Medium_Inverse 0x7f130027
int style Base_TextAppearance_AppCompat_Menu 0x7f130028
int style Base_TextAppearance_AppCompat_SearchResult 0x7f130029
int style Base_TextAppearance_AppCompat_SearchResult_Subtitle 0x7f13002a
int style Base_TextAppearance_AppCompat_SearchResult_Title 0x7f13002b
int style Base_TextAppearance_AppCompat_Small 0x7f13002c
int style Base_TextAppearance_AppCompat_Small_Inverse 0x7f13002d
int style Base_TextAppearance_AppCompat_Subhead 0x7f13002e
int style Base_TextAppearance_AppCompat_Subhead_Inverse 0x7f13002f
int style Base_TextAppearance_AppCompat_Title 0x7f130030
int style Base_TextAppearance_AppCompat_Title_Inverse 0x7f130031
int style Base_TextAppearance_AppCompat_Tooltip 0x7f130032
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f130033
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f130034
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f130035
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f130036
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f130037
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f130038
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f130039
int style Base_TextAppearance_AppCompat_Widget_Button 0x7f13003a
int style Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f13003b
int style Base_TextAppearance_AppCompat_Widget_Button_Colored 0x7f13003c
int style Base_TextAppearance_AppCompat_Widget_Button_Inverse 0x7f13003d
int style Base_TextAppearance_AppCompat_Widget_DropDownItem 0x7f13003e
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f13003f
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f130040
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f130041
int style Base_TextAppearance_AppCompat_Widget_Switch 0x7f130042
int style Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f130043
int style Base_TextAppearance_Material3_Search 0x7f130044
int style Base_TextAppearance_MaterialComponents_Badge 0x7f130045
int style Base_TextAppearance_MaterialComponents_Button 0x7f130046
int style Base_TextAppearance_MaterialComponents_Headline6 0x7f130047
int style Base_TextAppearance_MaterialComponents_Subtitle2 0x7f130048
int style Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f130049
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f13004a
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f13004b
int style Base_Theme_AppCompat 0x7f13004c
int style Base_Theme_AppCompat_CompactMenu 0x7f13004d
int style Base_Theme_AppCompat_Dialog 0x7f13004e
int style Base_Theme_AppCompat_Dialog_Alert 0x7f13004f
int style Base_Theme_AppCompat_Dialog_FixedSize 0x7f130050
int style Base_Theme_AppCompat_Dialog_MinWidth 0x7f130051
int style Base_Theme_AppCompat_DialogWhenLarge 0x7f130052
int style Base_Theme_AppCompat_Light 0x7f130053
int style Base_Theme_AppCompat_Light_DarkActionBar 0x7f130054
int style Base_Theme_AppCompat_Light_Dialog 0x7f130055
int style Base_Theme_AppCompat_Light_Dialog_Alert 0x7f130056
int style Base_Theme_AppCompat_Light_Dialog_FixedSize 0x7f130057
int style Base_Theme_AppCompat_Light_Dialog_MinWidth 0x7f130058
int style Base_Theme_AppCompat_Light_DialogWhenLarge 0x7f130059
int style Base_Theme_Material3_Dark 0x7f13005a
int style Base_Theme_Material3_Dark_BottomSheetDialog 0x7f13005b
int style Base_Theme_Material3_Dark_Dialog 0x7f13005c
int style Base_Theme_Material3_Dark_Dialog_FixedSize 0x7f13005d
int style Base_Theme_Material3_Dark_DialogWhenLarge 0x7f13005e
int style Base_Theme_Material3_Dark_SideSheetDialog 0x7f13005f
int style Base_Theme_Material3_Light 0x7f130060
int style Base_Theme_Material3_Light_BottomSheetDialog 0x7f130061
int style Base_Theme_Material3_Light_Dialog 0x7f130062
int style Base_Theme_Material3_Light_Dialog_FixedSize 0x7f130063
int style Base_Theme_Material3_Light_DialogWhenLarge 0x7f130064
int style Base_Theme_Material3_Light_SideSheetDialog 0x7f130065
int style Base_Theme_MaterialComponents 0x7f130066
int style Base_Theme_MaterialComponents_Bridge 0x7f130067
int style Base_Theme_MaterialComponents_CompactMenu 0x7f130068
int style Base_Theme_MaterialComponents_Dialog 0x7f130069
int style Base_Theme_MaterialComponents_Dialog_Alert 0x7f13006a
int style Base_Theme_MaterialComponents_Dialog_Bridge 0x7f13006b
int style Base_Theme_MaterialComponents_Dialog_FixedSize 0x7f13006c
int style Base_Theme_MaterialComponents_Dialog_MinWidth 0x7f13006d
int style Base_Theme_MaterialComponents_DialogWhenLarge 0x7f13006e
int style Base_Theme_MaterialComponents_Light 0x7f13006f
int style Base_Theme_MaterialComponents_Light_Bridge 0x7f130070
int style Base_Theme_MaterialComponents_Light_DarkActionBar 0x7f130071
int style Base_Theme_MaterialComponents_Light_DarkActionBar_Bridge 0x7f130072
int style Base_Theme_MaterialComponents_Light_Dialog 0x7f130073
int style Base_Theme_MaterialComponents_Light_Dialog_Alert 0x7f130074
int style Base_Theme_MaterialComponents_Light_Dialog_Bridge 0x7f130075
int style Base_Theme_MaterialComponents_Light_Dialog_FixedSize 0x7f130076
int style Base_Theme_MaterialComponents_Light_Dialog_MinWidth 0x7f130077
int style Base_Theme_MaterialComponents_Light_DialogWhenLarge 0x7f130078
int style Base_ThemeOverlay_AppCompat 0x7f130079
int style Base_ThemeOverlay_AppCompat_ActionBar 0x7f13007a
int style Base_ThemeOverlay_AppCompat_Dark 0x7f13007b
int style Base_ThemeOverlay_AppCompat_Dark_ActionBar 0x7f13007c
int style Base_ThemeOverlay_AppCompat_Dialog 0x7f13007d
int style Base_ThemeOverlay_AppCompat_Dialog_Alert 0x7f13007e
int style Base_ThemeOverlay_AppCompat_Light 0x7f13007f
int style Base_ThemeOverlay_Material3_AutoCompleteTextView 0x7f130080
int style Base_ThemeOverlay_Material3_BottomSheetDialog 0x7f130081
int style Base_ThemeOverlay_Material3_Dialog 0x7f130082
int style Base_ThemeOverlay_Material3_SideSheetDialog 0x7f130083
int style Base_ThemeOverlay_Material3_TextInputEditText 0x7f130084
int style Base_ThemeOverlay_MaterialComponents_Dialog 0x7f130085
int style Base_ThemeOverlay_MaterialComponents_Dialog_Alert 0x7f130086
int style Base_ThemeOverlay_MaterialComponents_Dialog_Alert_Framework 0x7f130087
int style Base_ThemeOverlay_MaterialComponents_Light_Dialog_Alert_Framework 0x7f130088
int style Base_ThemeOverlay_MaterialComponents_MaterialAlertDialog 0x7f130089
int style Base_V14_Theme_Material3_Dark 0x7f13008a
int style Base_V14_Theme_Material3_Dark_BottomSheetDialog 0x7f13008b
int style Base_V14_Theme_Material3_Dark_Dialog 0x7f13008c
int style Base_V14_Theme_Material3_Dark_SideSheetDialog 0x7f13008d
int style Base_V14_Theme_Material3_Light 0x7f13008e
int style Base_V14_Theme_Material3_Light_BottomSheetDialog 0x7f13008f
int style Base_V14_Theme_Material3_Light_Dialog 0x7f130090
int style Base_V14_Theme_Material3_Light_SideSheetDialog 0x7f130091
int style Base_V14_Theme_MaterialComponents 0x7f130092
int style Base_V14_Theme_MaterialComponents_Bridge 0x7f130093
int style Base_V14_Theme_MaterialComponents_Dialog 0x7f130094
int style Base_V14_Theme_MaterialComponents_Dialog_Bridge 0x7f130095
int style Base_V14_Theme_MaterialComponents_Light 0x7f130096
int style Base_V14_Theme_MaterialComponents_Light_Bridge 0x7f130097
int style Base_V14_Theme_MaterialComponents_Light_DarkActionBar_Bridge 0x7f130098
int style Base_V14_Theme_MaterialComponents_Light_Dialog 0x7f130099
int style Base_V14_Theme_MaterialComponents_Light_Dialog_Bridge 0x7f13009a
int style Base_V14_ThemeOverlay_Material3_BottomSheetDialog 0x7f13009b
int style Base_V14_ThemeOverlay_Material3_SideSheetDialog 0x7f13009c
int style Base_V14_ThemeOverlay_MaterialComponents_BottomSheetDialog 0x7f13009d
int style Base_V14_ThemeOverlay_MaterialComponents_Dialog 0x7f13009e
int style Base_V14_ThemeOverlay_MaterialComponents_Dialog_Alert 0x7f13009f
int style Base_V14_ThemeOverlay_MaterialComponents_MaterialAlertDialog 0x7f1300a0
int style Base_V14_Widget_MaterialComponents_AutoCompleteTextView 0x7f1300a1
int style Base_V21_Theme_AppCompat 0x7f1300a2
int style Base_V21_Theme_AppCompat_Dialog 0x7f1300a3
int style Base_V21_Theme_AppCompat_Light 0x7f1300a4
int style Base_V21_Theme_AppCompat_Light_Dialog 0x7f1300a5
int style Base_V21_Theme_MaterialComponents 0x7f1300a6
int style Base_V21_Theme_MaterialComponents_Dialog 0x7f1300a7
int style Base_V21_Theme_MaterialComponents_Light 0x7f1300a8
int style Base_V21_Theme_MaterialComponents_Light_Dialog 0x7f1300a9
int style Base_V21_ThemeOverlay_AppCompat_Dialog 0x7f1300aa
int style Base_V21_ThemeOverlay_Material3_BottomSheetDialog 0x7f1300ab
int style Base_V21_ThemeOverlay_Material3_SideSheetDialog 0x7f1300ac
int style Base_V21_ThemeOverlay_MaterialComponents_BottomSheetDialog 0x7f1300ad
int style Base_V22_Theme_AppCompat 0x7f1300ae
int style Base_V22_Theme_AppCompat_Light 0x7f1300af
int style Base_V23_Theme_AppCompat 0x7f1300b0
int style Base_V23_Theme_AppCompat_Light 0x7f1300b1
int style Base_V24_Theme_Material3_Dark 0x7f1300b2
int style Base_V24_Theme_Material3_Dark_Dialog 0x7f1300b3
int style Base_V24_Theme_Material3_Light 0x7f1300b4
int style Base_V24_Theme_Material3_Light_Dialog 0x7f1300b5
int style Base_V26_Theme_AppCompat 0x7f1300b6
int style Base_V26_Theme_AppCompat_Light 0x7f1300b7
int style Base_V26_Widget_AppCompat_Toolbar 0x7f1300b8
int style Base_V28_Theme_AppCompat 0x7f1300b9
int style Base_V28_Theme_AppCompat_Light 0x7f1300ba
int style Base_V7_Theme_AppCompat 0x7f1300bb
int style Base_V7_Theme_AppCompat_Dialog 0x7f1300bc
int style Base_V7_Theme_AppCompat_Light 0x7f1300bd
int style Base_V7_Theme_AppCompat_Light_Dialog 0x7f1300be
int style Base_V7_ThemeOverlay_AppCompat_Dialog 0x7f1300bf
int style Base_V7_Widget_AppCompat_AutoCompleteTextView 0x7f1300c0
int style Base_V7_Widget_AppCompat_EditText 0x7f1300c1
int style Base_V7_Widget_AppCompat_Toolbar 0x7f1300c2
int style Base_Widget_AppCompat_ActionBar 0x7f1300c3
int style Base_Widget_AppCompat_ActionBar_Solid 0x7f1300c4
int style Base_Widget_AppCompat_ActionBar_TabBar 0x7f1300c5
int style Base_Widget_AppCompat_ActionBar_TabText 0x7f1300c6
int style Base_Widget_AppCompat_ActionBar_TabView 0x7f1300c7
int style Base_Widget_AppCompat_ActionButton 0x7f1300c8
int style Base_Widget_AppCompat_ActionButton_CloseMode 0x7f1300c9
int style Base_Widget_AppCompat_ActionButton_Overflow 0x7f1300ca
int style Base_Widget_AppCompat_ActionMode 0x7f1300cb
int style Base_Widget_AppCompat_ActivityChooserView 0x7f1300cc
int style Base_Widget_AppCompat_AutoCompleteTextView 0x7f1300cd
int style Base_Widget_AppCompat_Button 0x7f1300ce
int style Base_Widget_AppCompat_Button_Borderless 0x7f1300cf
int style Base_Widget_AppCompat_Button_Borderless_Colored 0x7f1300d0
int style Base_Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f1300d1
int style Base_Widget_AppCompat_Button_Colored 0x7f1300d2
int style Base_Widget_AppCompat_Button_Small 0x7f1300d3
int style Base_Widget_AppCompat_ButtonBar 0x7f1300d4
int style Base_Widget_AppCompat_ButtonBar_AlertDialog 0x7f1300d5
int style Base_Widget_AppCompat_CompoundButton_CheckBox 0x7f1300d6
int style Base_Widget_AppCompat_CompoundButton_RadioButton 0x7f1300d7
int style Base_Widget_AppCompat_CompoundButton_Switch 0x7f1300d8
int style Base_Widget_AppCompat_DrawerArrowToggle 0x7f1300d9
int style Base_Widget_AppCompat_DrawerArrowToggle_Common 0x7f1300da
int style Base_Widget_AppCompat_DropDownItem_Spinner 0x7f1300db
int style Base_Widget_AppCompat_EditText 0x7f1300dc
int style Base_Widget_AppCompat_ImageButton 0x7f1300dd
int style Base_Widget_AppCompat_Light_ActionBar 0x7f1300de
int style Base_Widget_AppCompat_Light_ActionBar_Solid 0x7f1300df
int style Base_Widget_AppCompat_Light_ActionBar_TabBar 0x7f1300e0
int style Base_Widget_AppCompat_Light_ActionBar_TabText 0x7f1300e1
int style Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f1300e2
int style Base_Widget_AppCompat_Light_ActionBar_TabView 0x7f1300e3
int style Base_Widget_AppCompat_Light_PopupMenu 0x7f1300e4
int style Base_Widget_AppCompat_Light_PopupMenu_Overflow 0x7f1300e5
int style Base_Widget_AppCompat_ListMenuView 0x7f1300e6
int style Base_Widget_AppCompat_ListPopupWindow 0x7f1300e7
int style Base_Widget_AppCompat_ListView 0x7f1300e8
int style Base_Widget_AppCompat_ListView_DropDown 0x7f1300e9
int style Base_Widget_AppCompat_ListView_Menu 0x7f1300ea
int style Base_Widget_AppCompat_PopupMenu 0x7f1300eb
int style Base_Widget_AppCompat_PopupMenu_Overflow 0x7f1300ec
int style Base_Widget_AppCompat_PopupWindow 0x7f1300ed
int style Base_Widget_AppCompat_ProgressBar 0x7f1300ee
int style Base_Widget_AppCompat_ProgressBar_Horizontal 0x7f1300ef
int style Base_Widget_AppCompat_RatingBar 0x7f1300f0
int style Base_Widget_AppCompat_RatingBar_Indicator 0x7f1300f1
int style Base_Widget_AppCompat_RatingBar_Small 0x7f1300f2
int style Base_Widget_AppCompat_SearchView 0x7f1300f3
int style Base_Widget_AppCompat_SearchView_ActionBar 0x7f1300f4
int style Base_Widget_AppCompat_SeekBar 0x7f1300f5
int style Base_Widget_AppCompat_SeekBar_Discrete 0x7f1300f6
int style Base_Widget_AppCompat_Spinner 0x7f1300f7
int style Base_Widget_AppCompat_Spinner_Underlined 0x7f1300f8
int style Base_Widget_AppCompat_TextView 0x7f1300f9
int style Base_Widget_AppCompat_TextView_SpinnerItem 0x7f1300fa
int style Base_Widget_AppCompat_Toolbar 0x7f1300fb
int style Base_Widget_AppCompat_Toolbar_Button_Navigation 0x7f1300fc
int style Base_Widget_Design_TabLayout 0x7f1300fd
int style Base_Widget_Material3_ActionBar_Solid 0x7f1300fe
int style Base_Widget_Material3_ActionMode 0x7f1300ff
int style Base_Widget_Material3_BottomNavigationView 0x7f130100
int style Base_Widget_Material3_CardView 0x7f130101
int style Base_Widget_Material3_Chip 0x7f130102
int style Base_Widget_Material3_CollapsingToolbar 0x7f130103
int style Base_Widget_Material3_CompoundButton_CheckBox 0x7f130104
int style Base_Widget_Material3_CompoundButton_RadioButton 0x7f130105
int style Base_Widget_Material3_CompoundButton_Switch 0x7f130106
int style Base_Widget_Material3_ExtendedFloatingActionButton 0x7f130107
int style Base_Widget_Material3_ExtendedFloatingActionButton_Icon 0x7f130108
int style Base_Widget_Material3_FloatingActionButton 0x7f130109
int style Base_Widget_Material3_FloatingActionButton_Large 0x7f13010a
int style Base_Widget_Material3_FloatingActionButton_Small 0x7f13010b
int style Base_Widget_Material3_Light_ActionBar_Solid 0x7f13010c
int style Base_Widget_Material3_MaterialCalendar_NavigationButton 0x7f13010d
int style Base_Widget_Material3_Snackbar 0x7f13010e
int style Base_Widget_Material3_TabLayout 0x7f13010f
int style Base_Widget_Material3_TabLayout_OnSurface 0x7f130110
int style Base_Widget_Material3_TabLayout_Secondary 0x7f130111
int style Base_Widget_MaterialComponents_AutoCompleteTextView 0x7f130112
int style Base_Widget_MaterialComponents_CheckedTextView 0x7f130113
int style Base_Widget_MaterialComponents_Chip 0x7f130114
int style Base_Widget_MaterialComponents_MaterialCalendar_HeaderToggleButton 0x7f130115
int style Base_Widget_MaterialComponents_MaterialCalendar_NavigationButton 0x7f130116
int style Base_Widget_MaterialComponents_PopupMenu 0x7f130117
int style Base_Widget_MaterialComponents_PopupMenu_ContextMenu 0x7f130118
int style Base_Widget_MaterialComponents_PopupMenu_ListPopupWindow 0x7f130119
int style Base_Widget_MaterialComponents_PopupMenu_Overflow 0x7f13011a
int style Base_Widget_MaterialComponents_Slider 0x7f13011b
int style Base_Widget_MaterialComponents_Snackbar 0x7f13011c
int style Base_Widget_MaterialComponents_TextInputEditText 0x7f13011d
int style Base_Widget_MaterialComponents_TextInputLayout 0x7f13011e
int style Base_Widget_MaterialComponents_TextView 0x7f13011f
int style BasePreferenceThemeOverlay 0x7f130120
int style BaseStripe3DS2EditText 0x7f130121
int style BaseStripe3DS2TextInputLayout 0x7f130122
int style BaseStripe3DS2Theme 0x7f130123
int style BaseStripe3ds2ProgressTheme 0x7f130124
int style CardView 0x7f130125
int style CardView_Dark 0x7f130126
int style CardView_Light 0x7f130127
int style CheckoutTheme 0x7f130128
int style DialogWindowTheme 0x7f130129
int style FloatingDialogTheme 0x7f13012a
int style FloatingDialogWindowTheme 0x7f13012b
int style InAppWebViewTheme 0x7f13012c
int style LaunchTheme 0x7f13012d
int style MaterialAlertDialog_Material3 0x7f13012e
int style MaterialAlertDialog_Material3_Animation 0x7f13012f
int style MaterialAlertDialog_Material3_Body_Text 0x7f130130
int style MaterialAlertDialog_Material3_Body_Text_CenterStacked 0x7f130131
int style MaterialAlertDialog_Material3_Title_Icon 0x7f130132
int style MaterialAlertDialog_Material3_Title_Icon_CenterStacked 0x7f130133
int style MaterialAlertDialog_Material3_Title_Panel 0x7f130134
int style MaterialAlertDialog_Material3_Title_Panel_CenterStacked 0x7f130135
int style MaterialAlertDialog_Material3_Title_Text 0x7f130136
int style MaterialAlertDialog_Material3_Title_Text_CenterStacked 0x7f130137
int style MaterialAlertDialog_MaterialComponents 0x7f130138
int style MaterialAlertDialog_MaterialComponents_Body_Text 0x7f130139
int style MaterialAlertDialog_MaterialComponents_Picker_Date_Calendar 0x7f13013a
int style MaterialAlertDialog_MaterialComponents_Picker_Date_Spinner 0x7f13013b
int style MaterialAlertDialog_MaterialComponents_Title_Icon 0x7f13013c
int style MaterialAlertDialog_MaterialComponents_Title_Icon_CenterStacked 0x7f13013d
int style MaterialAlertDialog_MaterialComponents_Title_Panel 0x7f13013e
int style MaterialAlertDialog_MaterialComponents_Title_Panel_CenterStacked 0x7f13013f
int style MaterialAlertDialog_MaterialComponents_Title_Text 0x7f130140
int style MaterialAlertDialog_MaterialComponents_Title_Text_CenterStacked 0x7f130141
int style NormalTheme 0x7f130142
int style PayButtonGenericDarkTheme 0x7f130143
int style PayButtonGenericLightTheme 0x7f130144
int style Platform_AppCompat 0x7f130145
int style Platform_AppCompat_Light 0x7f130146
int style Platform_MaterialComponents 0x7f130147
int style Platform_MaterialComponents_Dialog 0x7f130148
int style Platform_MaterialComponents_Light 0x7f130149
int style Platform_MaterialComponents_Light_Dialog 0x7f13014a
int style Platform_ThemeOverlay_AppCompat 0x7f13014b
int style Platform_ThemeOverlay_AppCompat_Dark 0x7f13014c
int style Platform_ThemeOverlay_AppCompat_Light 0x7f13014d
int style Platform_V21_AppCompat 0x7f13014e
int style Platform_V21_AppCompat_Light 0x7f13014f
int style Platform_V25_AppCompat 0x7f130150
int style Platform_V25_AppCompat_Light 0x7f130151
int style Platform_Widget_AppCompat_Spinner 0x7f130152
int style Preference 0x7f130153
int style Preference_Category 0x7f130154
int style Preference_Category_Material 0x7f130155
int style Preference_CheckBoxPreference 0x7f130156
int style Preference_CheckBoxPreference_Material 0x7f130157
int style Preference_DialogPreference 0x7f130158
int style Preference_DialogPreference_EditTextPreference 0x7f130159
int style Preference_DialogPreference_EditTextPreference_Material 0x7f13015a
int style Preference_DialogPreference_Material 0x7f13015b
int style Preference_DropDown 0x7f13015c
int style Preference_DropDown_Material 0x7f13015d
int style Preference_Information 0x7f13015e
int style Preference_Information_Material 0x7f13015f
int style Preference_Material 0x7f130160
int style Preference_PreferenceScreen 0x7f130161
int style Preference_PreferenceScreen_Material 0x7f130162
int style Preference_SeekBarPreference 0x7f130163
int style Preference_SeekBarPreference_Material 0x7f130164
int style Preference_SwitchPreference 0x7f130165
int style Preference_SwitchPreference_Material 0x7f130166
int style Preference_SwitchPreferenceCompat 0x7f130167
int style Preference_SwitchPreferenceCompat_Material 0x7f130168
int style PreferenceCategoryTitleTextStyle 0x7f130169
int style PreferenceFragment 0x7f13016a
int style PreferenceFragment_Material 0x7f13016b
int style PreferenceFragmentList 0x7f13016c
int style PreferenceFragmentList_Material 0x7f13016d
int style PreferenceSummaryTextStyle 0x7f13016e
int style PreferenceThemeOverlay 0x7f13016f
int style PreferenceThemeOverlay_v14 0x7f130170
int style PreferenceThemeOverlay_v14_Material 0x7f130171
int style RtlOverlay_DialogWindowTitle_AppCompat 0x7f130172
int style RtlOverlay_Widget_AppCompat_ActionBar_TitleItem 0x7f130173
int style RtlOverlay_Widget_AppCompat_DialogTitle_Icon 0x7f130174
int style RtlOverlay_Widget_AppCompat_PopupMenuItem 0x7f130175
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup 0x7f130176
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut 0x7f130177
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow 0x7f130178
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Text 0x7f130179
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Title 0x7f13017a
int style RtlOverlay_Widget_AppCompat_Search_DropDown 0x7f13017b
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 0x7f13017c
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 0x7f13017d
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Query 0x7f13017e
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Text 0x7f13017f
int style RtlOverlay_Widget_AppCompat_SearchView_MagIcon 0x7f130180
int style RtlUnderlay_Widget_AppCompat_ActionButton 0x7f130181
int style RtlUnderlay_Widget_AppCompat_ActionButton_Overflow 0x7f130182
int style ShapeAppearance_M3_Comp_Badge_Large_Shape 0x7f130183
int style ShapeAppearance_M3_Comp_Badge_Shape 0x7f130184
int style ShapeAppearance_M3_Comp_BottomAppBar_Container_Shape 0x7f130185
int style ShapeAppearance_M3_Comp_DatePicker_Modal_Date_Container_Shape 0x7f130186
int style ShapeAppearance_M3_Comp_FilledButton_Container_Shape 0x7f130187
int style ShapeAppearance_M3_Comp_NavigationBar_ActiveIndicator_Shape 0x7f130188
int style ShapeAppearance_M3_Comp_NavigationBar_Container_Shape 0x7f130189
int style ShapeAppearance_M3_Comp_NavigationDrawer_ActiveIndicator_Shape 0x7f13018a
int style ShapeAppearance_M3_Comp_NavigationRail_ActiveIndicator_Shape 0x7f13018b
int style ShapeAppearance_M3_Comp_NavigationRail_Container_Shape 0x7f13018c
int style ShapeAppearance_M3_Comp_SearchBar_Avatar_Shape 0x7f13018d
int style ShapeAppearance_M3_Comp_SearchBar_Container_Shape 0x7f13018e
int style ShapeAppearance_M3_Comp_SearchView_FullScreen_Container_Shape 0x7f13018f
int style ShapeAppearance_M3_Comp_Sheet_Side_Docked_Container_Shape 0x7f130190
int style ShapeAppearance_M3_Comp_Switch_Handle_Shape 0x7f130191
int style ShapeAppearance_M3_Comp_Switch_StateLayer_Shape 0x7f130192
int style ShapeAppearance_M3_Comp_Switch_Track_Shape 0x7f130193
int style ShapeAppearance_M3_Comp_TextButton_Container_Shape 0x7f130194
int style ShapeAppearance_M3_Sys_Shape_Corner_ExtraLarge 0x7f130195
int style ShapeAppearance_M3_Sys_Shape_Corner_ExtraSmall 0x7f130196
int style ShapeAppearance_M3_Sys_Shape_Corner_Full 0x7f130197
int style ShapeAppearance_M3_Sys_Shape_Corner_Large 0x7f130198
int style ShapeAppearance_M3_Sys_Shape_Corner_Medium 0x7f130199
int style ShapeAppearance_M3_Sys_Shape_Corner_None 0x7f13019a
int style ShapeAppearance_M3_Sys_Shape_Corner_Small 0x7f13019b
int style ShapeAppearance_Material3_Corner_ExtraLarge 0x7f13019c
int style ShapeAppearance_Material3_Corner_ExtraSmall 0x7f13019d
int style ShapeAppearance_Material3_Corner_Full 0x7f13019e
int style ShapeAppearance_Material3_Corner_Large 0x7f13019f
int style ShapeAppearance_Material3_Corner_Medium 0x7f1301a0
int style ShapeAppearance_Material3_Corner_None 0x7f1301a1
int style ShapeAppearance_Material3_Corner_Small 0x7f1301a2
int style ShapeAppearance_Material3_LargeComponent 0x7f1301a3
int style ShapeAppearance_Material3_MediumComponent 0x7f1301a4
int style ShapeAppearance_Material3_NavigationBarView_ActiveIndicator 0x7f1301a5
int style ShapeAppearance_Material3_SmallComponent 0x7f1301a6
int style ShapeAppearance_Material3_Tooltip 0x7f1301a7
int style ShapeAppearance_MaterialComponents 0x7f1301a8
int style ShapeAppearance_MaterialComponents_Badge 0x7f1301a9
int style ShapeAppearance_MaterialComponents_LargeComponent 0x7f1301aa
int style ShapeAppearance_MaterialComponents_MediumComponent 0x7f1301ab
int style ShapeAppearance_MaterialComponents_SmallComponent 0x7f1301ac
int style ShapeAppearance_MaterialComponents_Tooltip 0x7f1301ad
int style ShapeAppearanceOverlay_Material3_Button 0x7f1301ae
int style ShapeAppearanceOverlay_Material3_Chip 0x7f1301af
int style ShapeAppearanceOverlay_Material3_Corner_Bottom 0x7f1301b0
int style ShapeAppearanceOverlay_Material3_Corner_Left 0x7f1301b1
int style ShapeAppearanceOverlay_Material3_Corner_Right 0x7f1301b2
int style ShapeAppearanceOverlay_Material3_Corner_Top 0x7f1301b3
int style ShapeAppearanceOverlay_Material3_FloatingActionButton 0x7f1301b4
int style ShapeAppearanceOverlay_Material3_NavigationView_Item 0x7f1301b5
int style ShapeAppearanceOverlay_Material3_SearchBar 0x7f1301b6
int style ShapeAppearanceOverlay_Material3_SearchView 0x7f1301b7
int style ShapeAppearanceOverlay_MaterialAlertDialog_Material3 0x7f1301b8
int style ShapeAppearanceOverlay_MaterialComponents_BottomSheet 0x7f1301b9
int style ShapeAppearanceOverlay_MaterialComponents_Chip 0x7f1301ba
int style ShapeAppearanceOverlay_MaterialComponents_ExtendedFloatingActionButton 0x7f1301bb
int style ShapeAppearanceOverlay_MaterialComponents_FloatingActionButton 0x7f1301bc
int style ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Day 0x7f1301bd
int style ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Window_Fullscreen 0x7f1301be
int style ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Year 0x7f1301bf
int style ShapeAppearanceOverlay_MaterialComponents_TextInputLayout_FilledBox 0x7f1301c0
int style Stripe_Base_BecsDebitWidget_EditText 0x7f1301c1
int style Stripe_Base_BecsDebitWidget_MandateAcceptanceTextView 0x7f1301c2
int style Stripe_Base_CardInputWidget_EditText 0x7f1301c3
int style Stripe_Base_CardInputWidget_TextInputLayout 0x7f1301c4
int style Stripe_Base_CardMultilineWidget_TextInputLayout 0x7f1301c5
int style Stripe_BecsDebitWidget_EditText 0x7f1301c6
int style Stripe_BecsDebitWidget_MandateAcceptanceTextView 0x7f1301c7
int style Stripe_CardInputWidget_EditText 0x7f1301c8
int style Stripe_CardInputWidget_TextInputLayout 0x7f1301c9
int style Stripe_CardMultilineWidget_TextInputLayout 0x7f1301ca
int style Stripe3DS2ActionBar 0x7f1301cb
int style Stripe3DS2ActionBarButton 0x7f1301cc
int style Stripe3DS2Button 0x7f1301cd
int style Stripe3DS2Divider 0x7f1301ce
int style Stripe3DS2Divider_Vertical 0x7f1301cf
int style Stripe3DS2EditText 0x7f1301d0
int style Stripe3DS2FooterChevron 0x7f1301d1
int style Stripe3DS2FooterHeader 0x7f1301d2
int style Stripe3DS2FooterText 0x7f1301d3
int style Stripe3DS2FullScreenDialog 0x7f1301d4
int style Stripe3DS2HeaderTextViewStyle 0x7f1301d5
int style Stripe3DS2TextButton 0x7f1301d6
int style Stripe3DS2TextInputLayout 0x7f1301d7
int style Stripe3DS2TextViewStyle 0x7f1301d8
int style Stripe3DS2Theme 0x7f1301d9
int style Stripe3ds2ProgressTheme 0x7f1301da
int style StripeActionButtonStyle 0x7f1301db
int style StripeAlertDialogStyle 0x7f1301dc
int style StripeBaseTheme 0x7f1301dd
int style StripeCardErrorTextView 0x7f1301de
int style StripeCardFormCountryItem 0x7f1301df
int style StripeCardFormCountryTextInputLayout 0x7f1301e0
int style StripeCardFormEditText 0x7f1301e1
int style StripeCardFormTextInputLayout 0x7f1301e2
int style StripeCardFormView 0x7f1301e3
int style StripeCardFormView_Borderless 0x7f1301e4
int style StripeDefault3DS2Theme 0x7f1301e5
int style StripeDefaultTheme 0x7f1301e6
int style StripeFinancialConnectionsBaseTheme 0x7f1301e7
int style StripeFinancialConnectionsDefaultTheme 0x7f1301e8
int style StripeGooglePayDefaultTheme 0x7f1301e9
int style StripeHCaptchaDialogTheme 0x7f1301ea
int style StripeLinkBaseTheme 0x7f1301eb
int style StripePayLauncherDefaultTheme 0x7f1301ec
int style StripePaymentSheetAddPaymentMethodTheme 0x7f1301ed
int style StripePaymentSheetBaseTheme 0x7f1301ee
int style StripePaymentSheetDefaultTheme 0x7f1301ef
int style StripePaymentSheetFormDivider 0x7f1301f0
int style StripeToolBarStyle 0x7f1301f1
int style StripeTransparentTheme 0x7f1301f2
int style StripeVerticalDivider 0x7f1301f3
int style TextAppearance_AppCompat 0x7f1301f4
int style TextAppearance_AppCompat_Body1 0x7f1301f5
int style TextAppearance_AppCompat_Body2 0x7f1301f6
int style TextAppearance_AppCompat_Button 0x7f1301f7
int style TextAppearance_AppCompat_Caption 0x7f1301f8
int style TextAppearance_AppCompat_Display1 0x7f1301f9
int style TextAppearance_AppCompat_Display2 0x7f1301fa
int style TextAppearance_AppCompat_Display3 0x7f1301fb
int style TextAppearance_AppCompat_Display4 0x7f1301fc
int style TextAppearance_AppCompat_Headline 0x7f1301fd
int style TextAppearance_AppCompat_Inverse 0x7f1301fe
int style TextAppearance_AppCompat_Large 0x7f1301ff
int style TextAppearance_AppCompat_Large_Inverse 0x7f130200
int style TextAppearance_AppCompat_Light_SearchResult_Subtitle 0x7f130201
int style TextAppearance_AppCompat_Light_SearchResult_Title 0x7f130202
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f130203
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f130204
int style TextAppearance_AppCompat_Medium 0x7f130205
int style TextAppearance_AppCompat_Medium_Inverse 0x7f130206
int style TextAppearance_AppCompat_Menu 0x7f130207
int style TextAppearance_AppCompat_SearchResult_Subtitle 0x7f130208
int style TextAppearance_AppCompat_SearchResult_Title 0x7f130209
int style TextAppearance_AppCompat_Small 0x7f13020a
int style TextAppearance_AppCompat_Small_Inverse 0x7f13020b
int style TextAppearance_AppCompat_Subhead 0x7f13020c
int style TextAppearance_AppCompat_Subhead_Inverse 0x7f13020d
int style TextAppearance_AppCompat_Title 0x7f13020e
int style TextAppearance_AppCompat_Title_Inverse 0x7f13020f
int style TextAppearance_AppCompat_Tooltip 0x7f130210
int style TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f130211
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f130212
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f130213
int style TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f130214
int style TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f130215
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f130216
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse 0x7f130217
int style TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f130218
int style TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse 0x7f130219
int style TextAppearance_AppCompat_Widget_Button 0x7f13021a
int style TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f13021b
int style TextAppearance_AppCompat_Widget_Button_Colored 0x7f13021c
int style TextAppearance_AppCompat_Widget_Button_Inverse 0x7f13021d
int style TextAppearance_AppCompat_Widget_DropDownItem 0x7f13021e
int style TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f13021f
int style TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f130220
int style TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f130221
int style TextAppearance_AppCompat_Widget_Switch 0x7f130222
int style TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f130223
int style TextAppearance_Compat_Notification 0x7f130224
int style TextAppearance_Compat_Notification_Info 0x7f130225
int style TextAppearance_Compat_Notification_Info_Media 0x7f130226
int style TextAppearance_Compat_Notification_Line2 0x7f130227
int style TextAppearance_Compat_Notification_Line2_Media 0x7f130228
int style TextAppearance_Compat_Notification_Media 0x7f130229
int style TextAppearance_Compat_Notification_Time 0x7f13022a
int style TextAppearance_Compat_Notification_Time_Media 0x7f13022b
int style TextAppearance_Compat_Notification_Title 0x7f13022c
int style TextAppearance_Compat_Notification_Title_Media 0x7f13022d
int style TextAppearance_Design_CollapsingToolbar_Expanded 0x7f13022e
int style TextAppearance_Design_Counter 0x7f13022f
int style TextAppearance_Design_Counter_Overflow 0x7f130230
int style TextAppearance_Design_Error 0x7f130231
int style TextAppearance_Design_HelperText 0x7f130232
int style TextAppearance_Design_Hint 0x7f130233
int style TextAppearance_Design_Placeholder 0x7f130234
int style TextAppearance_Design_Prefix 0x7f130235
int style TextAppearance_Design_Snackbar_Message 0x7f130236
int style TextAppearance_Design_Suffix 0x7f130237
int style TextAppearance_Design_Tab 0x7f130238
int style TextAppearance_M3_Sys_Typescale_BodyLarge 0x7f130239
int style TextAppearance_M3_Sys_Typescale_BodyMedium 0x7f13023a
int style TextAppearance_M3_Sys_Typescale_BodySmall 0x7f13023b
int style TextAppearance_M3_Sys_Typescale_DisplayLarge 0x7f13023c
int style TextAppearance_M3_Sys_Typescale_DisplayMedium 0x7f13023d
int style TextAppearance_M3_Sys_Typescale_DisplaySmall 0x7f13023e
int style TextAppearance_M3_Sys_Typescale_HeadlineLarge 0x7f13023f
int style TextAppearance_M3_Sys_Typescale_HeadlineMedium 0x7f130240
int style TextAppearance_M3_Sys_Typescale_HeadlineSmall 0x7f130241
int style TextAppearance_M3_Sys_Typescale_LabelLarge 0x7f130242
int style TextAppearance_M3_Sys_Typescale_LabelMedium 0x7f130243
int style TextAppearance_M3_Sys_Typescale_LabelSmall 0x7f130244
int style TextAppearance_M3_Sys_Typescale_TitleLarge 0x7f130245
int style TextAppearance_M3_Sys_Typescale_TitleMedium 0x7f130246
int style TextAppearance_M3_Sys_Typescale_TitleSmall 0x7f130247
int style TextAppearance_Material3_ActionBar_Subtitle 0x7f130248
int style TextAppearance_Material3_ActionBar_Title 0x7f130249
int style TextAppearance_Material3_BodyLarge 0x7f13024a
int style TextAppearance_Material3_BodyMedium 0x7f13024b
int style TextAppearance_Material3_BodySmall 0x7f13024c
int style TextAppearance_Material3_DisplayLarge 0x7f13024d
int style TextAppearance_Material3_DisplayMedium 0x7f13024e
int style TextAppearance_Material3_DisplaySmall 0x7f13024f
int style TextAppearance_Material3_HeadlineLarge 0x7f130250
int style TextAppearance_Material3_HeadlineMedium 0x7f130251
int style TextAppearance_Material3_HeadlineSmall 0x7f130252
int style TextAppearance_Material3_LabelLarge 0x7f130253
int style TextAppearance_Material3_LabelMedium 0x7f130254
int style TextAppearance_Material3_LabelSmall 0x7f130255
int style TextAppearance_Material3_MaterialTimePicker_Title 0x7f130256
int style TextAppearance_Material3_SearchBar 0x7f130257
int style TextAppearance_Material3_SearchView 0x7f130258
int style TextAppearance_Material3_SearchView_Prefix 0x7f130259
int style TextAppearance_Material3_TitleLarge 0x7f13025a
int style TextAppearance_Material3_TitleMedium 0x7f13025b
int style TextAppearance_Material3_TitleSmall 0x7f13025c
int style TextAppearance_MaterialComponents_Badge 0x7f13025d
int style TextAppearance_MaterialComponents_Body1 0x7f13025e
int style TextAppearance_MaterialComponents_Body2 0x7f13025f
int style TextAppearance_MaterialComponents_Button 0x7f130260
int style TextAppearance_MaterialComponents_Caption 0x7f130261
int style TextAppearance_MaterialComponents_Chip 0x7f130262
int style TextAppearance_MaterialComponents_Headline1 0x7f130263
int style TextAppearance_MaterialComponents_Headline2 0x7f130264
int style TextAppearance_MaterialComponents_Headline3 0x7f130265
int style TextAppearance_MaterialComponents_Headline4 0x7f130266
int style TextAppearance_MaterialComponents_Headline5 0x7f130267
int style TextAppearance_MaterialComponents_Headline6 0x7f130268
int style TextAppearance_MaterialComponents_Overline 0x7f130269
int style TextAppearance_MaterialComponents_Subtitle1 0x7f13026a
int style TextAppearance_MaterialComponents_Subtitle2 0x7f13026b
int style TextAppearance_MaterialComponents_TimePicker_Title 0x7f13026c
int style TextAppearance_MaterialComponents_Tooltip 0x7f13026d
int style TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f13026e
int style TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f13026f
int style TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f130270
int style Theme_AppCompat 0x7f130271
int style Theme_AppCompat_CompactMenu 0x7f130272
int style Theme_AppCompat_DayNight 0x7f130273
int style Theme_AppCompat_DayNight_DarkActionBar 0x7f130274
int style Theme_AppCompat_DayNight_Dialog 0x7f130275
int style Theme_AppCompat_DayNight_Dialog_Alert 0x7f130276
int style Theme_AppCompat_DayNight_Dialog_MinWidth 0x7f130277
int style Theme_AppCompat_DayNight_DialogWhenLarge 0x7f130278
int style Theme_AppCompat_DayNight_NoActionBar 0x7f130279
int style Theme_AppCompat_Dialog 0x7f13027a
int style Theme_AppCompat_Dialog_Alert 0x7f13027b
int style Theme_AppCompat_Dialog_MinWidth 0x7f13027c
int style Theme_AppCompat_DialogWhenLarge 0x7f13027d
int style Theme_AppCompat_Empty 0x7f13027e
int style Theme_AppCompat_Light 0x7f13027f
int style Theme_AppCompat_Light_DarkActionBar 0x7f130280
int style Theme_AppCompat_Light_Dialog 0x7f130281
int style Theme_AppCompat_Light_Dialog_Alert 0x7f130282
int style Theme_AppCompat_Light_Dialog_MinWidth 0x7f130283
int style Theme_AppCompat_Light_DialogWhenLarge 0x7f130284
int style Theme_AppCompat_Light_NoActionBar 0x7f130285
int style Theme_AppCompat_NoActionBar 0x7f130286
int style Theme_Design 0x7f130287
int style Theme_Design_BottomSheetDialog 0x7f130288
int style Theme_Design_Light 0x7f130289
int style Theme_Design_Light_BottomSheetDialog 0x7f13028a
int style Theme_Design_Light_NoActionBar 0x7f13028b
int style Theme_Design_NoActionBar 0x7f13028c
int style Theme_Material3_Dark 0x7f13028d
int style Theme_Material3_Dark_BottomSheetDialog 0x7f13028e
int style Theme_Material3_Dark_Dialog 0x7f13028f
int style Theme_Material3_Dark_Dialog_Alert 0x7f130290
int style Theme_Material3_Dark_Dialog_MinWidth 0x7f130291
int style Theme_Material3_Dark_DialogWhenLarge 0x7f130292
int style Theme_Material3_Dark_NoActionBar 0x7f130293
int style Theme_Material3_Dark_SideSheetDialog 0x7f130294
int style Theme_Material3_DayNight 0x7f130295
int style Theme_Material3_DayNight_BottomSheetDialog 0x7f130296
int style Theme_Material3_DayNight_Dialog 0x7f130297
int style Theme_Material3_DayNight_Dialog_Alert 0x7f130298
int style Theme_Material3_DayNight_Dialog_MinWidth 0x7f130299
int style Theme_Material3_DayNight_DialogWhenLarge 0x7f13029a
int style Theme_Material3_DayNight_NoActionBar 0x7f13029b
int style Theme_Material3_DayNight_SideSheetDialog 0x7f13029c
int style Theme_Material3_DynamicColors_Dark 0x7f13029d
int style Theme_Material3_DynamicColors_Dark_NoActionBar 0x7f13029e
int style Theme_Material3_DynamicColors_DayNight 0x7f13029f
int style Theme_Material3_DynamicColors_DayNight_NoActionBar 0x7f1302a0
int style Theme_Material3_DynamicColors_Light 0x7f1302a1
int style Theme_Material3_DynamicColors_Light_NoActionBar 0x7f1302a2
int style Theme_Material3_Light 0x7f1302a3
int style Theme_Material3_Light_BottomSheetDialog 0x7f1302a4
int style Theme_Material3_Light_Dialog 0x7f1302a5
int style Theme_Material3_Light_Dialog_Alert 0x7f1302a6
int style Theme_Material3_Light_Dialog_MinWidth 0x7f1302a7
int style Theme_Material3_Light_DialogWhenLarge 0x7f1302a8
int style Theme_Material3_Light_NoActionBar 0x7f1302a9
int style Theme_Material3_Light_SideSheetDialog 0x7f1302aa
int style Theme_MaterialComponents 0x7f1302ab
int style Theme_MaterialComponents_BottomSheetDialog 0x7f1302ac
int style Theme_MaterialComponents_Bridge 0x7f1302ad
int style Theme_MaterialComponents_CompactMenu 0x7f1302ae
int style Theme_MaterialComponents_DayNight 0x7f1302af
int style Theme_MaterialComponents_DayNight_BottomSheetDialog 0x7f1302b0
int style Theme_MaterialComponents_DayNight_Bridge 0x7f1302b1
int style Theme_MaterialComponents_DayNight_DarkActionBar 0x7f1302b2
int style Theme_MaterialComponents_DayNight_DarkActionBar_Bridge 0x7f1302b3
int style Theme_MaterialComponents_DayNight_Dialog 0x7f1302b4
int style Theme_MaterialComponents_DayNight_Dialog_Alert 0x7f1302b5
int style Theme_MaterialComponents_DayNight_Dialog_Alert_Bridge 0x7f1302b6
int style Theme_MaterialComponents_DayNight_Dialog_Bridge 0x7f1302b7
int style Theme_MaterialComponents_DayNight_Dialog_FixedSize 0x7f1302b8
int style Theme_MaterialComponents_DayNight_Dialog_FixedSize_Bridge 0x7f1302b9
int style Theme_MaterialComponents_DayNight_Dialog_MinWidth 0x7f1302ba
int style Theme_MaterialComponents_DayNight_Dialog_MinWidth_Bridge 0x7f1302bb
int style Theme_MaterialComponents_DayNight_DialogWhenLarge 0x7f1302bc
int style Theme_MaterialComponents_DayNight_NoActionBar 0x7f1302bd
int style Theme_MaterialComponents_DayNight_NoActionBar_Bridge 0x7f1302be
int style Theme_MaterialComponents_Dialog 0x7f1302bf
int style Theme_MaterialComponents_Dialog_Alert 0x7f1302c0
int style Theme_MaterialComponents_Dialog_Alert_Bridge 0x7f1302c1
int style Theme_MaterialComponents_Dialog_Bridge 0x7f1302c2
int style Theme_MaterialComponents_Dialog_FixedSize 0x7f1302c3
int style Theme_MaterialComponents_Dialog_FixedSize_Bridge 0x7f1302c4
int style Theme_MaterialComponents_Dialog_MinWidth 0x7f1302c5
int style Theme_MaterialComponents_Dialog_MinWidth_Bridge 0x7f1302c6
int style Theme_MaterialComponents_DialogWhenLarge 0x7f1302c7
int style Theme_MaterialComponents_Light 0x7f1302c8
int style Theme_MaterialComponents_Light_BottomSheetDialog 0x7f1302c9
int style Theme_MaterialComponents_Light_Bridge 0x7f1302ca
int style Theme_MaterialComponents_Light_DarkActionBar 0x7f1302cb
int style Theme_MaterialComponents_Light_DarkActionBar_Bridge 0x7f1302cc
int style Theme_MaterialComponents_Light_Dialog 0x7f1302cd
int style Theme_MaterialComponents_Light_Dialog_Alert 0x7f1302ce
int style Theme_MaterialComponents_Light_Dialog_Alert_Bridge 0x7f1302cf
int style Theme_MaterialComponents_Light_Dialog_Bridge 0x7f1302d0
int style Theme_MaterialComponents_Light_Dialog_FixedSize 0x7f1302d1
int style Theme_MaterialComponents_Light_Dialog_FixedSize_Bridge 0x7f1302d2
int style Theme_MaterialComponents_Light_Dialog_MinWidth 0x7f1302d3
int style Theme_MaterialComponents_Light_Dialog_MinWidth_Bridge 0x7f1302d4
int style Theme_MaterialComponents_Light_DialogWhenLarge 0x7f1302d5
int style Theme_MaterialComponents_Light_NoActionBar 0x7f1302d6
int style Theme_MaterialComponents_Light_NoActionBar_Bridge 0x7f1302d7
int style Theme_MaterialComponents_NoActionBar 0x7f1302d8
int style Theme_MaterialComponents_NoActionBar_Bridge 0x7f1302d9
int style Theme_PlayCore_Transparent 0x7f1302da
int style ThemeOverlay_AppCompat 0x7f1302db
int style ThemeOverlay_AppCompat_ActionBar 0x7f1302dc
int style ThemeOverlay_AppCompat_Dark 0x7f1302dd
int style ThemeOverlay_AppCompat_Dark_ActionBar 0x7f1302de
int style ThemeOverlay_AppCompat_DayNight 0x7f1302df
int style ThemeOverlay_AppCompat_DayNight_ActionBar 0x7f1302e0
int style ThemeOverlay_AppCompat_Dialog 0x7f1302e1
int style ThemeOverlay_AppCompat_Dialog_Alert 0x7f1302e2
int style ThemeOverlay_AppCompat_Light 0x7f1302e3
int style ThemeOverlay_Design_TextInputEditText 0x7f1302e4
int style ThemeOverlay_Material3 0x7f1302e5
int style ThemeOverlay_Material3_ActionBar 0x7f1302e6
int style ThemeOverlay_Material3_AutoCompleteTextView 0x7f1302e7
int style ThemeOverlay_Material3_AutoCompleteTextView_FilledBox 0x7f1302e8
int style ThemeOverlay_Material3_AutoCompleteTextView_FilledBox_Dense 0x7f1302e9
int style ThemeOverlay_Material3_AutoCompleteTextView_OutlinedBox 0x7f1302ea
int style ThemeOverlay_Material3_AutoCompleteTextView_OutlinedBox_Dense 0x7f1302eb
int style ThemeOverlay_Material3_BottomAppBar 0x7f1302ec
int style ThemeOverlay_Material3_BottomAppBar_Legacy 0x7f1302ed
int style ThemeOverlay_Material3_BottomNavigationView 0x7f1302ee
int style ThemeOverlay_Material3_BottomSheetDialog 0x7f1302ef
int style ThemeOverlay_Material3_Button 0x7f1302f0
int style ThemeOverlay_Material3_Button_ElevatedButton 0x7f1302f1
int style ThemeOverlay_Material3_Button_IconButton 0x7f1302f2
int style ThemeOverlay_Material3_Button_IconButton_Filled 0x7f1302f3
int style ThemeOverlay_Material3_Button_IconButton_Filled_Tonal 0x7f1302f4
int style ThemeOverlay_Material3_Button_TextButton 0x7f1302f5
int style ThemeOverlay_Material3_Button_TextButton_Snackbar 0x7f1302f6
int style ThemeOverlay_Material3_Button_TonalButton 0x7f1302f7
int style ThemeOverlay_Material3_Chip 0x7f1302f8
int style ThemeOverlay_Material3_Chip_Assist 0x7f1302f9
int style ThemeOverlay_Material3_Dark 0x7f1302fa
int style ThemeOverlay_Material3_Dark_ActionBar 0x7f1302fb
int style ThemeOverlay_Material3_DayNight_BottomSheetDialog 0x7f1302fc
int style ThemeOverlay_Material3_DayNight_SideSheetDialog 0x7f1302fd
int style ThemeOverlay_Material3_Dialog 0x7f1302fe
int style ThemeOverlay_Material3_Dialog_Alert 0x7f1302ff
int style ThemeOverlay_Material3_Dialog_Alert_Framework 0x7f130300
int style ThemeOverlay_Material3_DynamicColors_Dark 0x7f130301
int style ThemeOverlay_Material3_DynamicColors_DayNight 0x7f130302
int style ThemeOverlay_Material3_DynamicColors_Light 0x7f130303
int style ThemeOverlay_Material3_ExtendedFloatingActionButton_Primary 0x7f130304
int style ThemeOverlay_Material3_ExtendedFloatingActionButton_Secondary 0x7f130305
int style ThemeOverlay_Material3_ExtendedFloatingActionButton_Surface 0x7f130306
int style ThemeOverlay_Material3_ExtendedFloatingActionButton_Tertiary 0x7f130307
int style ThemeOverlay_Material3_FloatingActionButton_Primary 0x7f130308
int style ThemeOverlay_Material3_FloatingActionButton_Secondary 0x7f130309
int style ThemeOverlay_Material3_FloatingActionButton_Surface 0x7f13030a
int style ThemeOverlay_Material3_FloatingActionButton_Tertiary 0x7f13030b
int style ThemeOverlay_Material3_HarmonizedColors 0x7f13030c
int style ThemeOverlay_Material3_HarmonizedColors_Empty 0x7f13030d
int style ThemeOverlay_Material3_Light 0x7f13030e
int style ThemeOverlay_Material3_Light_Dialog_Alert_Framework 0x7f13030f
int style ThemeOverlay_Material3_MaterialAlertDialog 0x7f130310
int style ThemeOverlay_Material3_MaterialAlertDialog_Centered 0x7f130311
int style ThemeOverlay_Material3_MaterialCalendar 0x7f130312
int style ThemeOverlay_Material3_MaterialCalendar_Fullscreen 0x7f130313
int style ThemeOverlay_Material3_MaterialCalendar_HeaderCancelButton 0x7f130314
int style ThemeOverlay_Material3_MaterialTimePicker 0x7f130315
int style ThemeOverlay_Material3_MaterialTimePicker_Display_TextInputEditText 0x7f130316
int style ThemeOverlay_Material3_NavigationRailView 0x7f130317
int style ThemeOverlay_Material3_NavigationView 0x7f130318
int style ThemeOverlay_Material3_PersonalizedColors 0x7f130319
int style ThemeOverlay_Material3_Search 0x7f13031a
int style ThemeOverlay_Material3_SideSheetDialog 0x7f13031b
int style ThemeOverlay_Material3_Snackbar 0x7f13031c
int style ThemeOverlay_Material3_TabLayout 0x7f13031d
int style ThemeOverlay_Material3_TextInputEditText 0x7f13031e
int style ThemeOverlay_Material3_TextInputEditText_FilledBox 0x7f13031f
int style ThemeOverlay_Material3_TextInputEditText_FilledBox_Dense 0x7f130320
int style ThemeOverlay_Material3_TextInputEditText_OutlinedBox 0x7f130321
int style ThemeOverlay_Material3_TextInputEditText_OutlinedBox_Dense 0x7f130322
int style ThemeOverlay_Material3_Toolbar_Surface 0x7f130323
int style ThemeOverlay_MaterialAlertDialog_Material3_Title_Icon 0x7f130324
int style ThemeOverlay_MaterialComponents 0x7f130325
int style ThemeOverlay_MaterialComponents_ActionBar 0x7f130326
int style ThemeOverlay_MaterialComponents_ActionBar_Primary 0x7f130327
int style ThemeOverlay_MaterialComponents_ActionBar_Surface 0x7f130328
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView 0x7f130329
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_FilledBox 0x7f13032a
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_FilledBox_Dense 0x7f13032b
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_OutlinedBox 0x7f13032c
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_OutlinedBox_Dense 0x7f13032d
int style ThemeOverlay_MaterialComponents_BottomAppBar_Primary 0x7f13032e
int style ThemeOverlay_MaterialComponents_BottomAppBar_Surface 0x7f13032f
int style ThemeOverlay_MaterialComponents_BottomSheetDialog 0x7f130330
int style ThemeOverlay_MaterialComponents_Dark 0x7f130331
int style ThemeOverlay_MaterialComponents_Dark_ActionBar 0x7f130332
int style ThemeOverlay_MaterialComponents_DayNight_BottomSheetDialog 0x7f130333
int style ThemeOverlay_MaterialComponents_Dialog 0x7f130334
int style ThemeOverlay_MaterialComponents_Dialog_Alert 0x7f130335
int style ThemeOverlay_MaterialComponents_Dialog_Alert_Framework 0x7f130336
int style ThemeOverlay_MaterialComponents_Light 0x7f130337
int style ThemeOverlay_MaterialComponents_Light_Dialog_Alert_Framework 0x7f130338
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog 0x7f130339
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Centered 0x7f13033a
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date 0x7f13033b
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Calendar 0x7f13033c
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Header_Text 0x7f13033d
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Header_Text_Day 0x7f13033e
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Spinner 0x7f13033f
int style ThemeOverlay_MaterialComponents_MaterialCalendar 0x7f130340
int style ThemeOverlay_MaterialComponents_MaterialCalendar_Fullscreen 0x7f130341
int style ThemeOverlay_MaterialComponents_TextInputEditText 0x7f130342
int style ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox 0x7f130343
int style ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox_Dense 0x7f130344
int style ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox 0x7f130345
int style ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox_Dense 0x7f130346
int style ThemeOverlay_MaterialComponents_TimePicker 0x7f130347
int style ThemeOverlay_MaterialComponents_TimePicker_Display 0x7f130348
int style ThemeOverlay_MaterialComponents_TimePicker_Display_TextInputEditText 0x7f130349
int style ThemeOverlay_MaterialComponents_Toolbar_Popup_Primary 0x7f13034a
int style ThemeOverlay_MaterialComponents_Toolbar_Primary 0x7f13034b
int style ThemeOverlay_MaterialComponents_Toolbar_Surface 0x7f13034c
int style ThemeTransparent 0x7f13034d
int style WalletFragmentDefaultButtonTextAppearance 0x7f13034e
int style WalletFragmentDefaultDetailsHeaderTextAppearance 0x7f13034f
int style WalletFragmentDefaultDetailsTextAppearance 0x7f130350
int style WalletFragmentDefaultStyle 0x7f130351
int style Widget_AppCompat_ActionBar 0x7f130352
int style Widget_AppCompat_ActionBar_Solid 0x7f130353
int style Widget_AppCompat_ActionBar_TabBar 0x7f130354
int style Widget_AppCompat_ActionBar_TabText 0x7f130355
int style Widget_AppCompat_ActionBar_TabView 0x7f130356
int style Widget_AppCompat_ActionButton 0x7f130357
int style Widget_AppCompat_ActionButton_CloseMode 0x7f130358
int style Widget_AppCompat_ActionButton_Overflow 0x7f130359
int style Widget_AppCompat_ActionMode 0x7f13035a
int style Widget_AppCompat_ActivityChooserView 0x7f13035b
int style Widget_AppCompat_AutoCompleteTextView 0x7f13035c
int style Widget_AppCompat_Button 0x7f13035d
int style Widget_AppCompat_Button_Borderless 0x7f13035e
int style Widget_AppCompat_Button_Borderless_Colored 0x7f13035f
int style Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f130360
int style Widget_AppCompat_Button_Colored 0x7f130361
int style Widget_AppCompat_Button_Small 0x7f130362
int style Widget_AppCompat_ButtonBar 0x7f130363
int style Widget_AppCompat_ButtonBar_AlertDialog 0x7f130364
int style Widget_AppCompat_CompoundButton_CheckBox 0x7f130365
int style Widget_AppCompat_CompoundButton_RadioButton 0x7f130366
int style Widget_AppCompat_CompoundButton_Switch 0x7f130367
int style Widget_AppCompat_DrawerArrowToggle 0x7f130368
int style Widget_AppCompat_DropDownItem_Spinner 0x7f130369
int style Widget_AppCompat_EditText 0x7f13036a
int style Widget_AppCompat_ImageButton 0x7f13036b
int style Widget_AppCompat_Light_ActionBar 0x7f13036c
int style Widget_AppCompat_Light_ActionBar_Solid 0x7f13036d
int style Widget_AppCompat_Light_ActionBar_Solid_Inverse 0x7f13036e
int style Widget_AppCompat_Light_ActionBar_TabBar 0x7f13036f
int style Widget_AppCompat_Light_ActionBar_TabBar_Inverse 0x7f130370
int style Widget_AppCompat_Light_ActionBar_TabText 0x7f130371
int style Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f130372
int style Widget_AppCompat_Light_ActionBar_TabView 0x7f130373
int style Widget_AppCompat_Light_ActionBar_TabView_Inverse 0x7f130374
int style Widget_AppCompat_Light_ActionButton 0x7f130375
int style Widget_AppCompat_Light_ActionButton_CloseMode 0x7f130376
int style Widget_AppCompat_Light_ActionButton_Overflow 0x7f130377
int style Widget_AppCompat_Light_ActionMode_Inverse 0x7f130378
int style Widget_AppCompat_Light_ActivityChooserView 0x7f130379
int style Widget_AppCompat_Light_AutoCompleteTextView 0x7f13037a
int style Widget_AppCompat_Light_DropDownItem_Spinner 0x7f13037b
int style Widget_AppCompat_Light_ListPopupWindow 0x7f13037c
int style Widget_AppCompat_Light_ListView_DropDown 0x7f13037d
int style Widget_AppCompat_Light_PopupMenu 0x7f13037e
int style Widget_AppCompat_Light_PopupMenu_Overflow 0x7f13037f
int style Widget_AppCompat_Light_SearchView 0x7f130380
int style Widget_AppCompat_Light_Spinner_DropDown_ActionBar 0x7f130381
int style Widget_AppCompat_ListMenuView 0x7f130382
int style Widget_AppCompat_ListPopupWindow 0x7f130383
int style Widget_AppCompat_ListView 0x7f130384
int style Widget_AppCompat_ListView_DropDown 0x7f130385
int style Widget_AppCompat_ListView_Menu 0x7f130386
int style Widget_AppCompat_PopupMenu 0x7f130387
int style Widget_AppCompat_PopupMenu_Overflow 0x7f130388
int style Widget_AppCompat_PopupWindow 0x7f130389
int style Widget_AppCompat_ProgressBar 0x7f13038a
int style Widget_AppCompat_ProgressBar_Horizontal 0x7f13038b
int style Widget_AppCompat_RatingBar 0x7f13038c
int style Widget_AppCompat_RatingBar_Indicator 0x7f13038d
int style Widget_AppCompat_RatingBar_Small 0x7f13038e
int style Widget_AppCompat_SearchView 0x7f13038f
int style Widget_AppCompat_SearchView_ActionBar 0x7f130390
int style Widget_AppCompat_SeekBar 0x7f130391
int style Widget_AppCompat_SeekBar_Discrete 0x7f130392
int style Widget_AppCompat_Spinner 0x7f130393
int style Widget_AppCompat_Spinner_DropDown 0x7f130394
int style Widget_AppCompat_Spinner_DropDown_ActionBar 0x7f130395
int style Widget_AppCompat_Spinner_Underlined 0x7f130396
int style Widget_AppCompat_TextView 0x7f130397
int style Widget_AppCompat_TextView_SpinnerItem 0x7f130398
int style Widget_AppCompat_Toolbar 0x7f130399
int style Widget_AppCompat_Toolbar_Button_Navigation 0x7f13039a
int style Widget_Compat_NotificationActionContainer 0x7f13039b
int style Widget_Compat_NotificationActionText 0x7f13039c
int style Widget_Design_AppBarLayout 0x7f13039d
int style Widget_Design_BottomNavigationView 0x7f13039e
int style Widget_Design_BottomSheet_Modal 0x7f13039f
int style Widget_Design_CollapsingToolbar 0x7f1303a0
int style Widget_Design_FloatingActionButton 0x7f1303a1
int style Widget_Design_NavigationView 0x7f1303a2
int style Widget_Design_ScrimInsetsFrameLayout 0x7f1303a3
int style Widget_Design_Snackbar 0x7f1303a4
int style Widget_Design_TabLayout 0x7f1303a5
int style Widget_Design_TextInputEditText 0x7f1303a6
int style Widget_Design_TextInputLayout 0x7f1303a7
int style Widget_Material3_ActionBar_Solid 0x7f1303a8
int style Widget_Material3_ActionMode 0x7f1303a9
int style Widget_Material3_AppBarLayout 0x7f1303aa
int style Widget_Material3_AutoCompleteTextView_FilledBox 0x7f1303ab
int style Widget_Material3_AutoCompleteTextView_FilledBox_Dense 0x7f1303ac
int style Widget_Material3_AutoCompleteTextView_OutlinedBox 0x7f1303ad
int style Widget_Material3_AutoCompleteTextView_OutlinedBox_Dense 0x7f1303ae
int style Widget_Material3_Badge 0x7f1303af
int style Widget_Material3_Badge_AdjustToBounds 0x7f1303b0
int style Widget_Material3_BottomAppBar 0x7f1303b1
int style Widget_Material3_BottomAppBar_Button_Navigation 0x7f1303b2
int style Widget_Material3_BottomAppBar_Legacy 0x7f1303b3
int style Widget_Material3_BottomNavigation_Badge 0x7f1303b4
int style Widget_Material3_BottomNavigationView 0x7f1303b5
int style Widget_Material3_BottomNavigationView_ActiveIndicator 0x7f1303b6
int style Widget_Material3_BottomSheet 0x7f1303b7
int style Widget_Material3_BottomSheet_DragHandle 0x7f1303b8
int style Widget_Material3_BottomSheet_Modal 0x7f1303b9
int style Widget_Material3_Button 0x7f1303ba
int style Widget_Material3_Button_ElevatedButton 0x7f1303bb
int style Widget_Material3_Button_ElevatedButton_Icon 0x7f1303bc
int style Widget_Material3_Button_Icon 0x7f1303bd
int style Widget_Material3_Button_IconButton 0x7f1303be
int style Widget_Material3_Button_IconButton_Filled 0x7f1303bf
int style Widget_Material3_Button_IconButton_Filled_Tonal 0x7f1303c0
int style Widget_Material3_Button_IconButton_Outlined 0x7f1303c1
int style Widget_Material3_Button_OutlinedButton 0x7f1303c2
int style Widget_Material3_Button_OutlinedButton_Icon 0x7f1303c3
int style Widget_Material3_Button_TextButton 0x7f1303c4
int style Widget_Material3_Button_TextButton_Dialog 0x7f1303c5
int style Widget_Material3_Button_TextButton_Dialog_Flush 0x7f1303c6
int style Widget_Material3_Button_TextButton_Dialog_Icon 0x7f1303c7
int style Widget_Material3_Button_TextButton_Icon 0x7f1303c8
int style Widget_Material3_Button_TextButton_Snackbar 0x7f1303c9
int style Widget_Material3_Button_TonalButton 0x7f1303ca
int style Widget_Material3_Button_TonalButton_Icon 0x7f1303cb
int style Widget_Material3_Button_UnelevatedButton 0x7f1303cc
int style Widget_Material3_CardView_Elevated 0x7f1303cd
int style Widget_Material3_CardView_Filled 0x7f1303ce
int style Widget_Material3_CardView_Outlined 0x7f1303cf
int style Widget_Material3_CheckedTextView 0x7f1303d0
int style Widget_Material3_Chip_Assist 0x7f1303d1
int style Widget_Material3_Chip_Assist_Elevated 0x7f1303d2
int style Widget_Material3_Chip_Filter 0x7f1303d3
int style Widget_Material3_Chip_Filter_Elevated 0x7f1303d4
int style Widget_Material3_Chip_Input 0x7f1303d5
int style Widget_Material3_Chip_Input_Elevated 0x7f1303d6
int style Widget_Material3_Chip_Input_Icon 0x7f1303d7
int style Widget_Material3_Chip_Input_Icon_Elevated 0x7f1303d8
int style Widget_Material3_Chip_Suggestion 0x7f1303d9
int style Widget_Material3_Chip_Suggestion_Elevated 0x7f1303da
int style Widget_Material3_ChipGroup 0x7f1303db
int style Widget_Material3_CircularProgressIndicator 0x7f1303dc
int style Widget_Material3_CircularProgressIndicator_ExtraSmall 0x7f1303dd
int style Widget_Material3_CircularProgressIndicator_Legacy 0x7f1303de
int style Widget_Material3_CircularProgressIndicator_Legacy_ExtraSmall 0x7f1303df
int style Widget_Material3_CircularProgressIndicator_Legacy_Medium 0x7f1303e0
int style Widget_Material3_CircularProgressIndicator_Legacy_Small 0x7f1303e1
int style Widget_Material3_CircularProgressIndicator_Medium 0x7f1303e2
int style Widget_Material3_CircularProgressIndicator_Small 0x7f1303e3
int style Widget_Material3_CollapsingToolbar 0x7f1303e4
int style Widget_Material3_CollapsingToolbar_Large 0x7f1303e5
int style Widget_Material3_CollapsingToolbar_Medium 0x7f1303e6
int style Widget_Material3_CompoundButton_CheckBox 0x7f1303e7
int style Widget_Material3_CompoundButton_MaterialSwitch 0x7f1303e8
int style Widget_Material3_CompoundButton_RadioButton 0x7f1303e9
int style Widget_Material3_CompoundButton_Switch 0x7f1303ea
int style Widget_Material3_DrawerLayout 0x7f1303eb
int style Widget_Material3_ExtendedFloatingActionButton_Icon_Primary 0x7f1303ec
int style Widget_Material3_ExtendedFloatingActionButton_Icon_Secondary 0x7f1303ed
int style Widget_Material3_ExtendedFloatingActionButton_Icon_Surface 0x7f1303ee
int style Widget_Material3_ExtendedFloatingActionButton_Icon_Tertiary 0x7f1303ef
int style Widget_Material3_ExtendedFloatingActionButton_Primary 0x7f1303f0
int style Widget_Material3_ExtendedFloatingActionButton_Secondary 0x7f1303f1
int style Widget_Material3_ExtendedFloatingActionButton_Surface 0x7f1303f2
int style Widget_Material3_ExtendedFloatingActionButton_Tertiary 0x7f1303f3
int style Widget_Material3_FloatingActionButton_Large_Primary 0x7f1303f4
int style Widget_Material3_FloatingActionButton_Large_Secondary 0x7f1303f5
int style Widget_Material3_FloatingActionButton_Large_Surface 0x7f1303f6
int style Widget_Material3_FloatingActionButton_Large_Tertiary 0x7f1303f7
int style Widget_Material3_FloatingActionButton_Primary 0x7f1303f8
int style Widget_Material3_FloatingActionButton_Secondary 0x7f1303f9
int style Widget_Material3_FloatingActionButton_Small_Primary 0x7f1303fa
int style Widget_Material3_FloatingActionButton_Small_Secondary 0x7f1303fb
int style Widget_Material3_FloatingActionButton_Small_Surface 0x7f1303fc
int style Widget_Material3_FloatingActionButton_Small_Tertiary 0x7f1303fd
int style Widget_Material3_FloatingActionButton_Surface 0x7f1303fe
int style Widget_Material3_FloatingActionButton_Tertiary 0x7f1303ff
int style Widget_Material3_Light_ActionBar_Solid 0x7f130400
int style Widget_Material3_LinearProgressIndicator 0x7f130401
int style Widget_Material3_LinearProgressIndicator_Legacy 0x7f130402
int style Widget_Material3_MaterialButtonToggleGroup 0x7f130403
int style Widget_Material3_MaterialCalendar 0x7f130404
int style Widget_Material3_MaterialCalendar_Day 0x7f130405
int style Widget_Material3_MaterialCalendar_Day_Invalid 0x7f130406
int style Widget_Material3_MaterialCalendar_Day_Selected 0x7f130407
int style Widget_Material3_MaterialCalendar_Day_Today 0x7f130408
int style Widget_Material3_MaterialCalendar_DayOfWeekLabel 0x7f130409
int style Widget_Material3_MaterialCalendar_DayTextView 0x7f13040a
int style Widget_Material3_MaterialCalendar_Fullscreen 0x7f13040b
int style Widget_Material3_MaterialCalendar_HeaderCancelButton 0x7f13040c
int style Widget_Material3_MaterialCalendar_HeaderDivider 0x7f13040d
int style Widget_Material3_MaterialCalendar_HeaderLayout 0x7f13040e
int style Widget_Material3_MaterialCalendar_HeaderLayout_Fullscreen 0x7f13040f
int style Widget_Material3_MaterialCalendar_HeaderSelection 0x7f130410
int style Widget_Material3_MaterialCalendar_HeaderSelection_Fullscreen 0x7f130411
int style Widget_Material3_MaterialCalendar_HeaderTitle 0x7f130412
int style Widget_Material3_MaterialCalendar_HeaderToggleButton 0x7f130413
int style Widget_Material3_MaterialCalendar_Item 0x7f130414
int style Widget_Material3_MaterialCalendar_MonthNavigationButton 0x7f130415
int style Widget_Material3_MaterialCalendar_MonthTextView 0x7f130416
int style Widget_Material3_MaterialCalendar_Year 0x7f130417
int style Widget_Material3_MaterialCalendar_Year_Selected 0x7f130418
int style Widget_Material3_MaterialCalendar_Year_Today 0x7f130419
int style Widget_Material3_MaterialCalendar_YearNavigationButton 0x7f13041a
int style Widget_Material3_MaterialDivider 0x7f13041b
int style Widget_Material3_MaterialDivider_Heavy 0x7f13041c
int style Widget_Material3_MaterialTimePicker 0x7f13041d
int style Widget_Material3_MaterialTimePicker_Button 0x7f13041e
int style Widget_Material3_MaterialTimePicker_Clock 0x7f13041f
int style Widget_Material3_MaterialTimePicker_Display 0x7f130420
int style Widget_Material3_MaterialTimePicker_Display_Divider 0x7f130421
int style Widget_Material3_MaterialTimePicker_Display_HelperText 0x7f130422
int style Widget_Material3_MaterialTimePicker_Display_TextInputEditText 0x7f130423
int style Widget_Material3_MaterialTimePicker_Display_TextInputLayout 0x7f130424
int style Widget_Material3_MaterialTimePicker_ImageButton 0x7f130425
int style Widget_Material3_NavigationRailView 0x7f130426
int style Widget_Material3_NavigationRailView_ActiveIndicator 0x7f130427
int style Widget_Material3_NavigationRailView_Badge 0x7f130428
int style Widget_Material3_NavigationView 0x7f130429
int style Widget_Material3_PopupMenu 0x7f13042a
int style Widget_Material3_PopupMenu_ContextMenu 0x7f13042b
int style Widget_Material3_PopupMenu_ListPopupWindow 0x7f13042c
int style Widget_Material3_PopupMenu_Overflow 0x7f13042d
int style Widget_Material3_Search_ActionButton_Overflow 0x7f13042e
int style Widget_Material3_Search_Toolbar_Button_Navigation 0x7f13042f
int style Widget_Material3_SearchBar 0x7f130430
int style Widget_Material3_SearchBar_Outlined 0x7f130431
int style Widget_Material3_SearchView 0x7f130432
int style Widget_Material3_SearchView_Prefix 0x7f130433
int style Widget_Material3_SearchView_Toolbar 0x7f130434
int style Widget_Material3_SideSheet 0x7f130435
int style Widget_Material3_SideSheet_Detached 0x7f130436
int style Widget_Material3_SideSheet_Modal 0x7f130437
int style Widget_Material3_SideSheet_Modal_Detached 0x7f130438
int style Widget_Material3_Slider 0x7f130439
int style Widget_Material3_Slider_Label 0x7f13043a
int style Widget_Material3_Slider_Legacy 0x7f13043b
int style Widget_Material3_Slider_Legacy_Label 0x7f13043c
int style Widget_Material3_Snackbar 0x7f13043d
int style Widget_Material3_Snackbar_FullWidth 0x7f13043e
int style Widget_Material3_Snackbar_TextView 0x7f13043f
int style Widget_Material3_TabLayout 0x7f130440
int style Widget_Material3_TabLayout_OnSurface 0x7f130441
int style Widget_Material3_TabLayout_Secondary 0x7f130442
int style Widget_Material3_TextInputEditText_FilledBox 0x7f130443
int style Widget_Material3_TextInputEditText_FilledBox_Dense 0x7f130444
int style Widget_Material3_TextInputEditText_OutlinedBox 0x7f130445
int style Widget_Material3_TextInputEditText_OutlinedBox_Dense 0x7f130446
int style Widget_Material3_TextInputLayout_FilledBox 0x7f130447
int style Widget_Material3_TextInputLayout_FilledBox_Dense 0x7f130448
int style Widget_Material3_TextInputLayout_FilledBox_Dense_ExposedDropdownMenu 0x7f130449
int style Widget_Material3_TextInputLayout_FilledBox_ExposedDropdownMenu 0x7f13044a
int style Widget_Material3_TextInputLayout_OutlinedBox 0x7f13044b
int style Widget_Material3_TextInputLayout_OutlinedBox_Dense 0x7f13044c
int style Widget_Material3_TextInputLayout_OutlinedBox_Dense_ExposedDropdownMenu 0x7f13044d
int style Widget_Material3_TextInputLayout_OutlinedBox_ExposedDropdownMenu 0x7f13044e
int style Widget_Material3_Toolbar 0x7f13044f
int style Widget_Material3_Toolbar_OnSurface 0x7f130450
int style Widget_Material3_Toolbar_Surface 0x7f130451
int style Widget_Material3_Tooltip 0x7f130452
int style Widget_MaterialComponents_ActionBar_Primary 0x7f130453
int style Widget_MaterialComponents_ActionBar_PrimarySurface 0x7f130454
int style Widget_MaterialComponents_ActionBar_Solid 0x7f130455
int style Widget_MaterialComponents_ActionBar_Surface 0x7f130456
int style Widget_MaterialComponents_ActionMode 0x7f130457
int style Widget_MaterialComponents_AppBarLayout_Primary 0x7f130458
int style Widget_MaterialComponents_AppBarLayout_PrimarySurface 0x7f130459
int style Widget_MaterialComponents_AppBarLayout_Surface 0x7f13045a
int style Widget_MaterialComponents_AutoCompleteTextView_FilledBox 0x7f13045b
int style Widget_MaterialComponents_AutoCompleteTextView_FilledBox_Dense 0x7f13045c
int style Widget_MaterialComponents_AutoCompleteTextView_OutlinedBox 0x7f13045d
int style Widget_MaterialComponents_AutoCompleteTextView_OutlinedBox_Dense 0x7f13045e
int style Widget_MaterialComponents_Badge 0x7f13045f
int style Widget_MaterialComponents_BottomAppBar 0x7f130460
int style Widget_MaterialComponents_BottomAppBar_Colored 0x7f130461
int style Widget_MaterialComponents_BottomAppBar_PrimarySurface 0x7f130462
int style Widget_MaterialComponents_BottomNavigationView 0x7f130463
int style Widget_MaterialComponents_BottomNavigationView_Colored 0x7f130464
int style Widget_MaterialComponents_BottomNavigationView_PrimarySurface 0x7f130465
int style Widget_MaterialComponents_BottomSheet 0x7f130466
int style Widget_MaterialComponents_BottomSheet_Modal 0x7f130467
int style Widget_MaterialComponents_Button 0x7f130468
int style Widget_MaterialComponents_Button_Icon 0x7f130469
int style Widget_MaterialComponents_Button_OutlinedButton 0x7f13046a
int style Widget_MaterialComponents_Button_OutlinedButton_Icon 0x7f13046b
int style Widget_MaterialComponents_Button_TextButton 0x7f13046c
int style Widget_MaterialComponents_Button_TextButton_Dialog 0x7f13046d
int style Widget_MaterialComponents_Button_TextButton_Dialog_Flush 0x7f13046e
int style Widget_MaterialComponents_Button_TextButton_Dialog_Icon 0x7f13046f
int style Widget_MaterialComponents_Button_TextButton_Icon 0x7f130470
int style Widget_MaterialComponents_Button_TextButton_Snackbar 0x7f130471
int style Widget_MaterialComponents_Button_UnelevatedButton 0x7f130472
int style Widget_MaterialComponents_Button_UnelevatedButton_Icon 0x7f130473
int style Widget_MaterialComponents_CardView 0x7f130474
int style Widget_MaterialComponents_CheckedTextView 0x7f130475
int style Widget_MaterialComponents_Chip_Action 0x7f130476
int style Widget_MaterialComponents_Chip_Choice 0x7f130477
int style Widget_MaterialComponents_Chip_Entry 0x7f130478
int style Widget_MaterialComponents_Chip_Filter 0x7f130479
int style Widget_MaterialComponents_ChipGroup 0x7f13047a
int style Widget_MaterialComponents_CircularProgressIndicator 0x7f13047b
int style Widget_MaterialComponents_CircularProgressIndicator_ExtraSmall 0x7f13047c
int style Widget_MaterialComponents_CircularProgressIndicator_Medium 0x7f13047d
int style Widget_MaterialComponents_CircularProgressIndicator_Small 0x7f13047e
int style Widget_MaterialComponents_CollapsingToolbar 0x7f13047f
int style Widget_MaterialComponents_CompoundButton_CheckBox 0x7f130480
int style Widget_MaterialComponents_CompoundButton_RadioButton 0x7f130481
int style Widget_MaterialComponents_CompoundButton_Switch 0x7f130482
int style Widget_MaterialComponents_ExtendedFloatingActionButton 0x7f130483
int style Widget_MaterialComponents_ExtendedFloatingActionButton_Icon 0x7f130484
int style Widget_MaterialComponents_FloatingActionButton 0x7f130485
int style Widget_MaterialComponents_Light_ActionBar_Solid 0x7f130486
int style Widget_MaterialComponents_LinearProgressIndicator 0x7f130487
int style Widget_MaterialComponents_MaterialButtonToggleGroup 0x7f130488
int style Widget_MaterialComponents_MaterialCalendar 0x7f130489
int style Widget_MaterialComponents_MaterialCalendar_Day 0x7f13048a
int style Widget_MaterialComponents_MaterialCalendar_Day_Invalid 0x7f13048b
int style Widget_MaterialComponents_MaterialCalendar_Day_Selected 0x7f13048c
int style Widget_MaterialComponents_MaterialCalendar_Day_Today 0x7f13048d
int style Widget_MaterialComponents_MaterialCalendar_DayOfWeekLabel 0x7f13048e
int style Widget_MaterialComponents_MaterialCalendar_DayTextView 0x7f13048f
int style Widget_MaterialComponents_MaterialCalendar_Fullscreen 0x7f130490
int style Widget_MaterialComponents_MaterialCalendar_HeaderCancelButton 0x7f130491
int style Widget_MaterialComponents_MaterialCalendar_HeaderConfirmButton 0x7f130492
int style Widget_MaterialComponents_MaterialCalendar_HeaderDivider 0x7f130493
int style Widget_MaterialComponents_MaterialCalendar_HeaderLayout 0x7f130494
int style Widget_MaterialComponents_MaterialCalendar_HeaderLayout_Fullscreen 0x7f130495
int style Widget_MaterialComponents_MaterialCalendar_HeaderSelection 0x7f130496
int style Widget_MaterialComponents_MaterialCalendar_HeaderSelection_Fullscreen 0x7f130497
int style Widget_MaterialComponents_MaterialCalendar_HeaderTitle 0x7f130498
int style Widget_MaterialComponents_MaterialCalendar_HeaderToggleButton 0x7f130499
int style Widget_MaterialComponents_MaterialCalendar_Item 0x7f13049a
int style Widget_MaterialComponents_MaterialCalendar_MonthNavigationButton 0x7f13049b
int style Widget_MaterialComponents_MaterialCalendar_MonthTextView 0x7f13049c
int style Widget_MaterialComponents_MaterialCalendar_Year 0x7f13049d
int style Widget_MaterialComponents_MaterialCalendar_Year_Selected 0x7f13049e
int style Widget_MaterialComponents_MaterialCalendar_Year_Today 0x7f13049f
int style Widget_MaterialComponents_MaterialCalendar_YearNavigationButton 0x7f1304a0
int style Widget_MaterialComponents_MaterialDivider 0x7f1304a1
int style Widget_MaterialComponents_NavigationRailView 0x7f1304a2
int style Widget_MaterialComponents_NavigationRailView_Colored 0x7f1304a3
int style Widget_MaterialComponents_NavigationRailView_Colored_Compact 0x7f1304a4
int style Widget_MaterialComponents_NavigationRailView_Compact 0x7f1304a5
int style Widget_MaterialComponents_NavigationRailView_PrimarySurface 0x7f1304a6
int style Widget_MaterialComponents_NavigationView 0x7f1304a7
int style Widget_MaterialComponents_PopupMenu 0x7f1304a8
int style Widget_MaterialComponents_PopupMenu_ContextMenu 0x7f1304a9
int style Widget_MaterialComponents_PopupMenu_ListPopupWindow 0x7f1304aa
int style Widget_MaterialComponents_PopupMenu_Overflow 0x7f1304ab
int style Widget_MaterialComponents_ProgressIndicator 0x7f1304ac
int style Widget_MaterialComponents_ShapeableImageView 0x7f1304ad
int style Widget_MaterialComponents_Slider 0x7f1304ae
int style Widget_MaterialComponents_Snackbar 0x7f1304af
int style Widget_MaterialComponents_Snackbar_FullWidth 0x7f1304b0
int style Widget_MaterialComponents_Snackbar_TextView 0x7f1304b1
int style Widget_MaterialComponents_TabLayout 0x7f1304b2
int style Widget_MaterialComponents_TabLayout_Colored 0x7f1304b3
int style Widget_MaterialComponents_TabLayout_PrimarySurface 0x7f1304b4
int style Widget_MaterialComponents_TextInputEditText_FilledBox 0x7f1304b5
int style Widget_MaterialComponents_TextInputEditText_FilledBox_Dense 0x7f1304b6
int style Widget_MaterialComponents_TextInputEditText_OutlinedBox 0x7f1304b7
int style Widget_MaterialComponents_TextInputEditText_OutlinedBox_Dense 0x7f1304b8
int style Widget_MaterialComponents_TextInputLayout_FilledBox 0x7f1304b9
int style Widget_MaterialComponents_TextInputLayout_FilledBox_Dense 0x7f1304ba
int style Widget_MaterialComponents_TextInputLayout_FilledBox_Dense_ExposedDropdownMenu 0x7f1304bb
int style Widget_MaterialComponents_TextInputLayout_FilledBox_ExposedDropdownMenu 0x7f1304bc
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox 0x7f1304bd
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox_Dense 0x7f1304be
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox_Dense_ExposedDropdownMenu 0x7f1304bf
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox_ExposedDropdownMenu 0x7f1304c0
int style Widget_MaterialComponents_TextView 0x7f1304c1
int style Widget_MaterialComponents_TimePicker 0x7f1304c2
int style Widget_MaterialComponents_TimePicker_Button 0x7f1304c3
int style Widget_MaterialComponents_TimePicker_Clock 0x7f1304c4
int style Widget_MaterialComponents_TimePicker_Display 0x7f1304c5
int style Widget_MaterialComponents_TimePicker_Display_Divider 0x7f1304c6
int style Widget_MaterialComponents_TimePicker_Display_HelperText 0x7f1304c7
int style Widget_MaterialComponents_TimePicker_Display_TextInputEditText 0x7f1304c8
int style Widget_MaterialComponents_TimePicker_Display_TextInputLayout 0x7f1304c9
int style Widget_MaterialComponents_TimePicker_ImageButton 0x7f1304ca
int style Widget_MaterialComponents_TimePicker_ImageButton_ShapeAppearance 0x7f1304cb
int style Widget_MaterialComponents_Toolbar 0x7f1304cc
int style Widget_MaterialComponents_Toolbar_Primary 0x7f1304cd
int style Widget_MaterialComponents_Toolbar_PrimarySurface 0x7f1304ce
int style Widget_MaterialComponents_Toolbar_Surface 0x7f1304cf
int style Widget_MaterialComponents_Tooltip 0x7f1304d0
int style Widget_Support_CoordinatorLayout 0x7f1304d1
int[] styleable ActionBar { 0x7f030053, 0x7f03005b, 0x7f03005c, 0x7f03015d, 0x7f03015e, 0x7f03015f, 0x7f030160, 0x7f030161, 0x7f030162, 0x7f03018d, 0x7f0301ac, 0x7f0301ad, 0x7f0301cf, 0x7f03025e, 0x7f030266, 0x7f03026c, 0x7f03026d, 0x7f030271, 0x7f030285, 0x7f03029e, 0x7f030322, 0x7f0303b1, 0x7f0303f2, 0x7f030405, 0x7f030406, 0x7f0304a0, 0x7f0304a4, 0x7f030536, 0x7f030544 }
int styleable ActionBar_background 0
int styleable ActionBar_backgroundSplit 1
int styleable ActionBar_backgroundStacked 2
int styleable ActionBar_contentInsetEnd 3
int styleable ActionBar_contentInsetEndWithActions 4
int styleable ActionBar_contentInsetLeft 5
int styleable ActionBar_contentInsetRight 6
int styleable ActionBar_contentInsetStart 7
int styleable ActionBar_contentInsetStartWithNavigation 8
int styleable ActionBar_customNavigationLayout 9
int styleable ActionBar_displayOptions 10
int styleable ActionBar_divider 11
int styleable ActionBar_elevation 12
int styleable ActionBar_height 13
int styleable ActionBar_hideOnContentScroll 14
int styleable ActionBar_homeAsUpIndicator 15
int styleable ActionBar_homeLayout 16
int styleable ActionBar_icon 17
int styleable ActionBar_indeterminateProgressStyle 18
int styleable ActionBar_itemPadding 19
int styleable ActionBar_logo 20
int styleable ActionBar_navigationMode 21
int styleable ActionBar_popupTheme 22
int styleable ActionBar_progressBarPadding 23
int styleable ActionBar_progressBarStyle 24
int styleable ActionBar_subtitle 25
int styleable ActionBar_subtitleTextStyle 26
int styleable ActionBar_title 27
int styleable ActionBar_titleTextStyle 28
int[] styleable ActionBarLayout { 0x010100b3 }
int styleable ActionBarLayout_android_layout_gravity 0
int[] styleable ActionMenuItemView { 0x0101013f }
int styleable ActionMenuItemView_android_minWidth 0
int[] styleable ActionMenuView { }
int[] styleable ActionMode { 0x7f030053, 0x7f03005b, 0x7f030107, 0x7f03025e, 0x7f0304a4, 0x7f030544 }
int styleable ActionMode_background 0
int styleable ActionMode_backgroundSplit 1
int styleable ActionMode_closeItemLayout 2
int styleable ActionMode_height 3
int styleable ActionMode_subtitleTextStyle 4
int styleable ActionMode_titleTextStyle 5
int[] styleable ActivityChooserView { 0x7f0301f1, 0x7f03028c }
int styleable ActivityChooserView_expandActivityOverflowButtonDrawable 0
int styleable ActivityChooserView_initialActivityCount 1
int[] styleable ActivityFilter { 0x7f030028, 0x7f03002a }
int styleable ActivityFilter_activityAction 0
int styleable ActivityFilter_activityName 1
int[] styleable ActivityNavigator { 0x01010003, 0x7f030002, 0x7f030192, 0x7f030193, 0x7f0304d4 }
int styleable ActivityNavigator_android_name 0
int styleable ActivityNavigator_action 1
int styleable ActivityNavigator_data 2
int styleable ActivityNavigator_dataPattern 3
int styleable ActivityNavigator_targetPackage 4
int[] styleable ActivityRule { 0x7f030038, 0x7f0304d2 }
int styleable ActivityRule_alwaysExpand 0
int styleable ActivityRule_tag 1
int[] styleable AlertDialog { 0x010100f2, 0x7f03009f, 0x7f0300a2, 0x7f030316, 0x7f030317, 0x7f0303ac, 0x7f030452, 0x7f03045a }
int styleable AlertDialog_android_layout 0
int styleable AlertDialog_buttonIconDimen 1
int styleable AlertDialog_buttonPanelSideLayout 2
int styleable AlertDialog_listItemLayout 3
int styleable AlertDialog_listLayout 4
int styleable AlertDialog_multiChoiceItemLayout 5
int styleable AlertDialog_showTitle 6
int styleable AlertDialog_singleChoiceItemLayout 7
int[] styleable AnimatedStateListDrawableCompat { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable AnimatedStateListDrawableCompat_android_dither 0
int styleable AnimatedStateListDrawableCompat_android_visible 1
int styleable AnimatedStateListDrawableCompat_android_variablePadding 2
int styleable AnimatedStateListDrawableCompat_android_constantSize 3
int styleable AnimatedStateListDrawableCompat_android_enterFadeDuration 4
int styleable AnimatedStateListDrawableCompat_android_exitFadeDuration 5
int[] styleable AnimatedStateListDrawableItem { 0x010100d0, 0x01010199 }
int styleable AnimatedStateListDrawableItem_android_id 0
int styleable AnimatedStateListDrawableItem_android_drawable 1
int[] styleable AnimatedStateListDrawableTransition { 0x01010199, 0x01010449, 0x0101044a, 0x0101044b }
int styleable AnimatedStateListDrawableTransition_android_drawable 0
int styleable AnimatedStateListDrawableTransition_android_toId 1
int styleable AnimatedStateListDrawableTransition_android_fromId 2
int styleable AnimatedStateListDrawableTransition_android_reversible 3
int[] styleable AppBarLayout { 0x010100d4, 0x0101048f, 0x01010540, 0x7f0301cf, 0x7f0301f2, 0x7f03030b, 0x7f03030c, 0x7f03030d, 0x7f03048a }
int styleable AppBarLayout_android_background 0
int styleable AppBarLayout_android_touchscreenBlocksFocus 1
int styleable AppBarLayout_android_keyboardNavigationCluster 2
int styleable AppBarLayout_elevation 3
int styleable AppBarLayout_expanded 4
int styleable AppBarLayout_liftOnScroll 5
int styleable AppBarLayout_liftOnScrollColor 6
int styleable AppBarLayout_liftOnScrollTargetViewId 7
int styleable AppBarLayout_statusBarForeground 8
int[] styleable AppBarLayoutStates { 0x7f030481, 0x7f030482, 0x7f030486, 0x7f030487 }
int styleable AppBarLayoutStates_state_collapsed 0
int styleable AppBarLayoutStates_state_collapsible 1
int styleable AppBarLayoutStates_state_liftable 2
int styleable AppBarLayoutStates_state_lifted 3
int[] styleable AppBarLayout_Layout { 0x7f030307, 0x7f030308, 0x7f030309 }
int styleable AppBarLayout_Layout_layout_scrollEffect 0
int styleable AppBarLayout_Layout_layout_scrollFlags 1
int styleable AppBarLayout_Layout_layout_scrollInterpolator 2
int[] styleable AppCompatEmojiHelper { }
int[] styleable AppCompatImageView { 0x01010119, 0x7f030474, 0x7f030533, 0x7f030534 }
int styleable AppCompatImageView_android_src 0
int styleable AppCompatImageView_srcCompat 1
int styleable AppCompatImageView_tint 2
int styleable AppCompatImageView_tintMode 3
int[] styleable AppCompatSeekBar { 0x01010142, 0x7f03052d, 0x7f03052e, 0x7f03052f }
int styleable AppCompatSeekBar_android_thumb 0
int styleable AppCompatSeekBar_tickMark 1
int styleable AppCompatSeekBar_tickMarkTint 2
int styleable AppCompatSeekBar_tickMarkTintMode 3
int[] styleable AppCompatTextHelper { 0x01010034, 0x0101016d, 0x0101016e, 0x0101016f, 0x01010170, 0x01010392, 0x01010393 }
int styleable AppCompatTextHelper_android_textAppearance 0
int styleable AppCompatTextHelper_android_drawableTop 1
int styleable AppCompatTextHelper_android_drawableBottom 2
int styleable AppCompatTextHelper_android_drawableLeft 3
int styleable AppCompatTextHelper_android_drawableRight 4
int styleable AppCompatTextHelper_android_drawableStart 5
int styleable AppCompatTextHelper_android_drawableEnd 6
int[] styleable AppCompatTextView { 0x01010034, 0x7f03004c, 0x7f03004d, 0x7f03004e, 0x7f03004f, 0x7f030050, 0x7f0301b9, 0x7f0301ba, 0x7f0301bb, 0x7f0301bc, 0x7f0301be, 0x7f0301bf, 0x7f0301c0, 0x7f0301c1, 0x7f0301d3, 0x7f030215, 0x7f030239, 0x7f030242, 0x7f0302bb, 0x7f03030f, 0x7f0304d8, 0x7f03050f }
int styleable AppCompatTextView_android_textAppearance 0
int styleable AppCompatTextView_autoSizeMaxTextSize 1
int styleable AppCompatTextView_autoSizeMinTextSize 2
int styleable AppCompatTextView_autoSizePresetSizes 3
int styleable AppCompatTextView_autoSizeStepGranularity 4
int styleable AppCompatTextView_autoSizeTextType 5
int styleable AppCompatTextView_drawableBottomCompat 6
int styleable AppCompatTextView_drawableEndCompat 7
int styleable AppCompatTextView_drawableLeftCompat 8
int styleable AppCompatTextView_drawableRightCompat 9
int styleable AppCompatTextView_drawableStartCompat 10
int styleable AppCompatTextView_drawableTint 11
int styleable AppCompatTextView_drawableTintMode 12
int styleable AppCompatTextView_drawableTopCompat 13
int styleable AppCompatTextView_emojiCompatEnabled 14
int styleable AppCompatTextView_firstBaselineToTopHeight 15
int styleable AppCompatTextView_fontFamily 16
int styleable AppCompatTextView_fontVariationSettings 17
int styleable AppCompatTextView_lastBaselineToBottomHeight 18
int styleable AppCompatTextView_lineHeight 19
int styleable AppCompatTextView_textAllCaps 20
int styleable AppCompatTextView_textLocale 21
int[] styleable AppCompatTheme { 0x01010057, 0x010100ae, 0x7f030003, 0x7f030004, 0x7f030005, 0x7f030006, 0x7f030007, 0x7f030008, 0x7f030009, 0x7f03000a, 0x7f03000b, 0x7f03000c, 0x7f03000d, 0x7f03000e, 0x7f03000f, 0x7f030011, 0x7f030012, 0x7f030013, 0x7f030014, 0x7f030015, 0x7f030016, 0x7f030017, 0x7f030018, 0x7f030019, 0x7f03001a, 0x7f03001b, 0x7f03001c, 0x7f03001d, 0x7f03001e, 0x7f03001f, 0x7f030020, 0x7f030021, 0x7f030022, 0x7f030023, 0x7f030029, 0x7f03002d, 0x7f03002e, 0x7f03002f, 0x7f030030, 0x7f03004a, 0x7f030084, 0x7f030097, 0x7f030098, 0x7f030099, 0x7f03009a, 0x7f03009b, 0x7f0300a4, 0x7f0300a5, 0x7f0300d1, 0x7f0300dc, 0x7f030114, 0x7f030115, 0x7f030116, 0x7f030118, 0x7f030119, 0x7f03011a, 0x7f03011b, 0x7f030134, 0x7f030136, 0x7f03014c, 0x7f03016c, 0x7f0301a3, 0x7f0301a8, 0x7f0301a9, 0x7f0301af, 0x7f0301b4, 0x7f0301c6, 0x7f0301c7, 0x7f0301cb, 0x7f0301cc, 0x7f0301ce, 0x7f03026c, 0x7f03027f, 0x7f030312, 0x7f030313, 0x7f030314, 0x7f030315, 0x7f030318, 0x7f030319, 0x7f03031a, 0x7f03031b, 0x7f03031c, 0x7f03031d, 0x7f03031e, 0x7f03031f, 0x7f030320, 0x7f0303d0, 0x7f0303d1, 0x7f0303d2, 0x7f0303f1, 0x7f0303f3, 0x7f03040d, 0x7f03040f, 0x7f030410, 0x7f030411, 0x7f03042c, 0x7f030431, 0x7f030433, 0x7f030434, 0x7f030465, 0x7f030466, 0x7f0304b1, 0x7f0304ef, 0x7f0304f1, 0x7f0304f2, 0x7f0304f3, 0x7f0304f5, 0x7f0304f6, 0x7f0304f7, 0x7f0304f8, 0x7f030503, 0x7f030504, 0x7f030547, 0x7f030548, 0x7f03054b, 0x7f03054c, 0x7f03057e, 0x7f03058d, 0x7f03058e, 0x7f03058f, 0x7f030590, 0x7f030591, 0x7f030592, 0x7f030593, 0x7f030594, 0x7f030595, 0x7f030596 }
int styleable AppCompatTheme_android_windowIsFloating 0
int styleable AppCompatTheme_android_windowAnimationStyle 1
int styleable AppCompatTheme_actionBarDivider 2
int styleable AppCompatTheme_actionBarItemBackground 3
int styleable AppCompatTheme_actionBarPopupTheme 4
int styleable AppCompatTheme_actionBarSize 5
int styleable AppCompatTheme_actionBarSplitStyle 6
int styleable AppCompatTheme_actionBarStyle 7
int styleable AppCompatTheme_actionBarTabBarStyle 8
int styleable AppCompatTheme_actionBarTabStyle 9
int styleable AppCompatTheme_actionBarTabTextStyle 10
int styleable AppCompatTheme_actionBarTheme 11
int styleable AppCompatTheme_actionBarWidgetTheme 12
int styleable AppCompatTheme_actionButtonStyle 13
int styleable AppCompatTheme_actionDropDownStyle 14
int styleable AppCompatTheme_actionMenuTextAppearance 15
int styleable AppCompatTheme_actionMenuTextColor 16
int styleable AppCompatTheme_actionModeBackground 17
int styleable AppCompatTheme_actionModeCloseButtonStyle 18
int styleable AppCompatTheme_actionModeCloseContentDescription 19
int styleable AppCompatTheme_actionModeCloseDrawable 20
int styleable AppCompatTheme_actionModeCopyDrawable 21
int styleable AppCompatTheme_actionModeCutDrawable 22
int styleable AppCompatTheme_actionModeFindDrawable 23
int styleable AppCompatTheme_actionModePasteDrawable 24
int styleable AppCompatTheme_actionModePopupWindowStyle 25
int styleable AppCompatTheme_actionModeSelectAllDrawable 26
int styleable AppCompatTheme_actionModeShareDrawable 27
int styleable AppCompatTheme_actionModeSplitBackground 28
int styleable AppCompatTheme_actionModeStyle 29
int styleable AppCompatTheme_actionModeTheme 30
int styleable AppCompatTheme_actionModeWebSearchDrawable 31
int styleable AppCompatTheme_actionOverflowButtonStyle 32
int styleable AppCompatTheme_actionOverflowMenuStyle 33
int styleable AppCompatTheme_activityChooserViewStyle 34
int styleable AppCompatTheme_alertDialogButtonGroupStyle 35
int styleable AppCompatTheme_alertDialogCenterButtons 36
int styleable AppCompatTheme_alertDialogStyle 37
int styleable AppCompatTheme_alertDialogTheme 38
int styleable AppCompatTheme_autoCompleteTextViewStyle 39
int styleable AppCompatTheme_borderlessButtonStyle 40
int styleable AppCompatTheme_buttonBarButtonStyle 41
int styleable AppCompatTheme_buttonBarNegativeButtonStyle 42
int styleable AppCompatTheme_buttonBarNeutralButtonStyle 43
int styleable AppCompatTheme_buttonBarPositiveButtonStyle 44
int styleable AppCompatTheme_buttonBarStyle 45
int styleable AppCompatTheme_buttonStyle 46
int styleable AppCompatTheme_buttonStyleSmall 47
int styleable AppCompatTheme_checkboxStyle 48
int styleable AppCompatTheme_checkedTextViewStyle 49
int styleable AppCompatTheme_colorAccent 50
int styleable AppCompatTheme_colorBackgroundFloating 51
int styleable AppCompatTheme_colorButtonNormal 52
int styleable AppCompatTheme_colorControlActivated 53
int styleable AppCompatTheme_colorControlHighlight 54
int styleable AppCompatTheme_colorControlNormal 55
int styleable AppCompatTheme_colorError 56
int styleable AppCompatTheme_colorPrimary 57
int styleable AppCompatTheme_colorPrimaryDark 58
int styleable AppCompatTheme_colorSwitchThumbNormal 59
int styleable AppCompatTheme_controlBackground 60
int styleable AppCompatTheme_dialogCornerRadius 61
int styleable AppCompatTheme_dialogPreferredPadding 62
int styleable AppCompatTheme_dialogTheme 63
int styleable AppCompatTheme_dividerHorizontal 64
int styleable AppCompatTheme_dividerVertical 65
int styleable AppCompatTheme_dropDownListViewStyle 66
int styleable AppCompatTheme_dropdownListPreferredItemHeight 67
int styleable AppCompatTheme_editTextBackground 68
int styleable AppCompatTheme_editTextColor 69
int styleable AppCompatTheme_editTextStyle 70
int styleable AppCompatTheme_homeAsUpIndicator 71
int styleable AppCompatTheme_imageButtonStyle 72
int styleable AppCompatTheme_listChoiceBackgroundIndicator 73
int styleable AppCompatTheme_listChoiceIndicatorMultipleAnimated 74
int styleable AppCompatTheme_listChoiceIndicatorSingleAnimated 75
int styleable AppCompatTheme_listDividerAlertDialog 76
int styleable AppCompatTheme_listMenuViewStyle 77
int styleable AppCompatTheme_listPopupWindowStyle 78
int styleable AppCompatTheme_listPreferredItemHeight 79
int styleable AppCompatTheme_listPreferredItemHeightLarge 80
int styleable AppCompatTheme_listPreferredItemHeightSmall 81
int styleable AppCompatTheme_listPreferredItemPaddingEnd 82
int styleable AppCompatTheme_listPreferredItemPaddingLeft 83
int styleable AppCompatTheme_listPreferredItemPaddingRight 84
int styleable AppCompatTheme_listPreferredItemPaddingStart 85
int styleable AppCompatTheme_panelBackground 86
int styleable AppCompatTheme_panelMenuListTheme 87
int styleable AppCompatTheme_panelMenuListWidth 88
int styleable AppCompatTheme_popupMenuStyle 89
int styleable AppCompatTheme_popupWindowStyle 90
int styleable AppCompatTheme_radioButtonStyle 91
int styleable AppCompatTheme_ratingBarStyle 92
int styleable AppCompatTheme_ratingBarStyleIndicator 93
int styleable AppCompatTheme_ratingBarStyleSmall 94
int styleable AppCompatTheme_searchViewStyle 95
int styleable AppCompatTheme_seekBarStyle 96
int styleable AppCompatTheme_selectableItemBackground 97
int styleable AppCompatTheme_selectableItemBackgroundBorderless 98
int styleable AppCompatTheme_spinnerDropDownItemStyle 99
int styleable AppCompatTheme_spinnerStyle 100
int styleable AppCompatTheme_switchStyle 101
int styleable AppCompatTheme_textAppearanceLargePopupMenu 102
int styleable AppCompatTheme_textAppearanceListItem 103
int styleable AppCompatTheme_textAppearanceListItemSecondary 104
int styleable AppCompatTheme_textAppearanceListItemSmall 105
int styleable AppCompatTheme_textAppearancePopupMenuHeader 106
int styleable AppCompatTheme_textAppearanceSearchResultSubtitle 107
int styleable AppCompatTheme_textAppearanceSearchResultTitle 108
int styleable AppCompatTheme_textAppearanceSmallPopupMenu 109
int styleable AppCompatTheme_textColorAlertDialogListItem 110
int styleable AppCompatTheme_textColorSearchUrl 111
int styleable AppCompatTheme_toolbarNavigationButtonStyle 112
int styleable AppCompatTheme_toolbarStyle 113
int styleable AppCompatTheme_tooltipForegroundColor 114
int styleable AppCompatTheme_tooltipFrameBackground 115
int styleable AppCompatTheme_viewInflaterClass 116
int styleable AppCompatTheme_windowActionBar 117
int styleable AppCompatTheme_windowActionBarOverlay 118
int styleable AppCompatTheme_windowActionModeOverlay 119
int styleable AppCompatTheme_windowFixedHeightMajor 120
int styleable AppCompatTheme_windowFixedHeightMinor 121
int styleable AppCompatTheme_windowFixedWidthMajor 122
int styleable AppCompatTheme_windowFixedWidthMinor 123
int styleable AppCompatTheme_windowMinWidthMajor 124
int styleable AppCompatTheme_windowMinWidthMinor 125
int styleable AppCompatTheme_windowNoTitle 126
int[] styleable BackgroundStyle { 0x0101030e, 0x7f030433 }
int styleable BackgroundStyle_android_selectableItemBackground 0
int styleable BackgroundStyle_selectableItemBackground 1
int[] styleable Badge { 0x7f030048, 0x7f030054, 0x7f03005f, 0x7f030060, 0x7f030061, 0x7f030062, 0x7f030063, 0x7f030065, 0x7f030066, 0x7f030067, 0x7f030068, 0x7f030069, 0x7f03006a, 0x7f03006b, 0x7f03006c, 0x7f03006d, 0x7f03006e, 0x7f03006f, 0x7f03026e, 0x7f03026f, 0x7f0302ba, 0x7f030365, 0x7f030369, 0x7f0303b9, 0x7f0303bb, 0x7f03057c, 0x7f03057d }
int styleable Badge_autoAdjustToWithinGrandparentBounds 0
int styleable Badge_backgroundColor 1
int styleable Badge_badgeGravity 2
int styleable Badge_badgeHeight 3
int styleable Badge_badgeRadius 4
int styleable Badge_badgeShapeAppearance 5
int styleable Badge_badgeShapeAppearanceOverlay 6
int styleable Badge_badgeText 7
int styleable Badge_badgeTextAppearance 8
int styleable Badge_badgeTextColor 9
int styleable Badge_badgeVerticalPadding 10
int styleable Badge_badgeWidePadding 11
int styleable Badge_badgeWidth 12
int styleable Badge_badgeWithTextHeight 13
int styleable Badge_badgeWithTextRadius 14
int styleable Badge_badgeWithTextShapeAppearance 15
int styleable Badge_badgeWithTextShapeAppearanceOverlay 16
int styleable Badge_badgeWithTextWidth 17
int styleable Badge_horizontalOffset 18
int styleable Badge_horizontalOffsetWithText 19
int styleable Badge_largeFontVerticalOffsetAdjustment 20
int styleable Badge_maxCharacterCount 21
int styleable Badge_maxNumber 22
int styleable Badge_number 23
int styleable Badge_offsetAlignmentMode 24
int styleable Badge_verticalOffset 25
int styleable Badge_verticalOffsetWithText 26
int[] styleable BaseProgressIndicator { 0x01010139, 0x7f030263, 0x7f030286, 0x7f03028b, 0x7f030374, 0x7f030449, 0x7f03044b, 0x7f030554, 0x7f030557, 0x7f03055e }
int styleable BaseProgressIndicator_android_indeterminate 0
int styleable BaseProgressIndicator_hideAnimationBehavior 1
int styleable BaseProgressIndicator_indicatorColor 2
int styleable BaseProgressIndicator_indicatorTrackGapSize 3
int styleable BaseProgressIndicator_minHideDelay 4
int styleable BaseProgressIndicator_showAnimationBehavior 5
int styleable BaseProgressIndicator_showDelay 6
int styleable BaseProgressIndicator_trackColor 7
int styleable BaseProgressIndicator_trackCornerRadius 8
int styleable BaseProgressIndicator_trackThickness 9
int[] styleable BecsDebitWidget { 0x7f030152 }
int styleable BecsDebitWidget_companyName 0
int[] styleable BottomAppBar { 0x7f03002b, 0x7f03005d, 0x7f0301cf, 0x7f030204, 0x7f030205, 0x7f030206, 0x7f030207, 0x7f030208, 0x7f030209, 0x7f03020a, 0x7f030267, 0x7f03036e, 0x7f0303b0, 0x7f0303c8, 0x7f0303ca, 0x7f0303cb, 0x7f03041b }
int styleable BottomAppBar_addElevationShadow 0
int styleable BottomAppBar_backgroundTint 1
int styleable BottomAppBar_elevation 2
int styleable BottomAppBar_fabAlignmentMode 3
int styleable BottomAppBar_fabAlignmentModeEndMargin 4
int styleable BottomAppBar_fabAnchorMode 5
int styleable BottomAppBar_fabAnimationMode 6
int styleable BottomAppBar_fabCradleMargin 7
int styleable BottomAppBar_fabCradleRoundedCornerRadius 8
int styleable BottomAppBar_fabCradleVerticalOffset 9
int styleable BottomAppBar_hideOnScroll 10
int styleable BottomAppBar_menuAlignmentMode 11
int styleable BottomAppBar_navigationIconTint 12
int styleable BottomAppBar_paddingBottomSystemWindowInsets 13
int styleable BottomAppBar_paddingLeftSystemWindowInsets 14
int styleable BottomAppBar_paddingRightSystemWindowInsets 15
int styleable BottomAppBar_removeEmbeddedFabElevation 16
int[] styleable BottomNavigationView { 0x01010140, 0x7f030153, 0x7f030298, 0x7f030438, 0x7f030440 }
int styleable BottomNavigationView_android_minHeight 0
int styleable BottomNavigationView_compatShadowEnabled 1
int styleable BottomNavigationView_itemHorizontalTranslationEnabled 2
int styleable BottomNavigationView_shapeAppearance 3
int styleable BottomNavigationView_shapeAppearanceOverlay 4
int[] styleable BottomSheetBehavior_Layout { 0x0101011f, 0x01010120, 0x01010440, 0x7f03005d, 0x7f030076, 0x7f030077, 0x7f030078, 0x7f030079, 0x7f03007a, 0x7f03007c, 0x7f03007d, 0x7f03007e, 0x7f03007f, 0x7f03024c, 0x7f030329, 0x7f03032a, 0x7f03032b, 0x7f0303c8, 0x7f0303ca, 0x7f0303cb, 0x7f0303cf, 0x7f030438, 0x7f030440, 0x7f030445 }
int styleable BottomSheetBehavior_Layout_android_maxWidth 0
int styleable BottomSheetBehavior_Layout_android_maxHeight 1
int styleable BottomSheetBehavior_Layout_android_elevation 2
int styleable BottomSheetBehavior_Layout_backgroundTint 3
int styleable BottomSheetBehavior_Layout_behavior_draggable 4
int styleable BottomSheetBehavior_Layout_behavior_expandedOffset 5
int styleable BottomSheetBehavior_Layout_behavior_fitToContents 6
int styleable BottomSheetBehavior_Layout_behavior_halfExpandedRatio 7
int styleable BottomSheetBehavior_Layout_behavior_hideable 8
int styleable BottomSheetBehavior_Layout_behavior_peekHeight 9
int styleable BottomSheetBehavior_Layout_behavior_saveFlags 10
int styleable BottomSheetBehavior_Layout_behavior_significantVelocityThreshold 11
int styleable BottomSheetBehavior_Layout_behavior_skipCollapsed 12
int styleable BottomSheetBehavior_Layout_gestureInsetBottomIgnored 13
int styleable BottomSheetBehavior_Layout_marginLeftSystemWindowInsets 14
int styleable BottomSheetBehavior_Layout_marginRightSystemWindowInsets 15
int styleable BottomSheetBehavior_Layout_marginTopSystemWindowInsets 16
int styleable BottomSheetBehavior_Layout_paddingBottomSystemWindowInsets 17
int styleable BottomSheetBehavior_Layout_paddingLeftSystemWindowInsets 18
int styleable BottomSheetBehavior_Layout_paddingRightSystemWindowInsets 19
int styleable BottomSheetBehavior_Layout_paddingTopSystemWindowInsets 20
int styleable BottomSheetBehavior_Layout_shapeAppearance 21
int styleable BottomSheetBehavior_Layout_shapeAppearanceOverlay 22
int styleable BottomSheetBehavior_Layout_shouldRemoveExpandedCorners 23
int[] styleable ButtonBarLayout { 0x7f030034 }
int styleable ButtonBarLayout_allowStacking 0
int[] styleable Capability { 0x7f03040c, 0x7f030443 }
int styleable Capability_queryPatterns 0
int styleable Capability_shortcutMatchRequired 1
int[] styleable CardElement { 0x7f030446, 0x7f030447, 0x7f030448 }
int styleable CardElement_shouldRequirePostalCode 0
int styleable CardElement_shouldRequireUsZipCode 1
int styleable CardElement_shouldShowPostalCode 2
int[] styleable CardInputView { 0x01010544, 0x7f0300b9, 0x7f0300bc, 0x7f0300bd }
int styleable CardInputView_android_focusedByDefault 0
int styleable CardInputView_cardHintText 1
int styleable CardInputView_cardTextErrorColor 2
int styleable CardInputView_cardTint 3
int[] styleable CardView { 0x0101013f, 0x01010140, 0x7f0300b4, 0x7f0300b5, 0x7f0300b6, 0x7f0300ba, 0x7f0300bb, 0x7f0300be, 0x7f030163, 0x7f030164, 0x7f030166, 0x7f030167, 0x7f030169 }
int styleable CardView_android_minWidth 0
int styleable CardView_android_minHeight 1
int styleable CardView_cardBackgroundColor 2
int styleable CardView_cardCornerRadius 3
int styleable CardView_cardElevation 4
int styleable CardView_cardMaxElevation 5
int styleable CardView_cardPreventCornerOverlap 6
int styleable CardView_cardUseCompatPadding 7
int styleable CardView_contentPadding 8
int styleable CardView_contentPaddingBottom 9
int styleable CardView_contentPaddingLeft 10
int styleable CardView_contentPaddingRight 11
int styleable CardView_contentPaddingTop 12
int[] styleable Carousel { 0x7f0300c0, 0x7f0300c1, 0x7f0300c2, 0x7f0300c3, 0x7f0300c4, 0x7f0300c5, 0x7f0300c6, 0x7f0300c7, 0x7f0300c8, 0x7f0300c9, 0x7f0300ca }
int styleable Carousel_carousel_alignment 0
int styleable Carousel_carousel_backwardTransition 1
int styleable Carousel_carousel_emptyViewsBehavior 2
int styleable Carousel_carousel_firstView 3
int styleable Carousel_carousel_forwardTransition 4
int styleable Carousel_carousel_infinite 5
int styleable Carousel_carousel_nextState 6
int styleable Carousel_carousel_previousState 7
int styleable Carousel_carousel_touchUpMode 8
int styleable Carousel_carousel_touchUp_dampeningFactor 9
int styleable Carousel_carousel_touchUp_velocityThreshold 10
int[] styleable CheckBoxPreference { 0x010101ef, 0x010101f0, 0x010101f1, 0x7f0301ab, 0x7f0304aa, 0x7f0304ab }
int styleable CheckBoxPreference_android_summaryOn 0
int styleable CheckBoxPreference_android_summaryOff 1
int styleable CheckBoxPreference_android_disableDependentsState 2
int styleable CheckBoxPreference_disableDependentsState 3
int styleable CheckBoxPreference_summaryOff 4
int styleable CheckBoxPreference_summaryOn 5
int[] styleable CheckedTextView { 0x01010108, 0x7f0300ce, 0x7f0300cf, 0x7f0300d0 }
int styleable CheckedTextView_android_checkMark 0
int styleable CheckedTextView_checkMarkCompat 1
int styleable CheckedTextView_checkMarkTint 2
int styleable CheckedTextView_checkMarkTintMode 3
int[] styleable Chip { 0x01010034, 0x01010095, 0x01010098, 0x010100ab, 0x0101011f, 0x0101014f, 0x010101e5, 0x7f0300d4, 0x7f0300d5, 0x7f0300d9, 0x7f0300da, 0x7f0300dd, 0x7f0300de, 0x7f0300df, 0x7f0300e1, 0x7f0300e2, 0x7f0300e3, 0x7f0300e4, 0x7f0300e5, 0x7f0300e6, 0x7f0300e7, 0x7f0300ec, 0x7f0300ed, 0x7f0300ee, 0x7f0300f0, 0x7f030100, 0x7f030101, 0x7f030102, 0x7f030103, 0x7f030104, 0x7f030105, 0x7f030106, 0x7f0301e1, 0x7f030264, 0x7f030272, 0x7f030277, 0x7f03041e, 0x7f030438, 0x7f030440, 0x7f03044e, 0x7f030505, 0x7f030514 }
int styleable Chip_android_textAppearance 0
int styleable Chip_android_textSize 1
int styleable Chip_android_textColor 2
int styleable Chip_android_ellipsize 3
int styleable Chip_android_maxWidth 4
int styleable Chip_android_text 5
int styleable Chip_android_checkable 6
int styleable Chip_checkedIcon 7
int styleable Chip_checkedIconEnabled 8
int styleable Chip_checkedIconTint 9
int styleable Chip_checkedIconVisible 10
int styleable Chip_chipBackgroundColor 11
int styleable Chip_chipCornerRadius 12
int styleable Chip_chipEndPadding 13
int styleable Chip_chipIcon 14
int styleable Chip_chipIconEnabled 15
int styleable Chip_chipIconSize 16
int styleable Chip_chipIconTint 17
int styleable Chip_chipIconVisible 18
int styleable Chip_chipMinHeight 19
int styleable Chip_chipMinTouchTargetSize 20
int styleable Chip_chipStartPadding 21
int styleable Chip_chipStrokeColor 22
int styleable Chip_chipStrokeWidth 23
int styleable Chip_chipSurfaceColor 24
int styleable Chip_closeIcon 25
int styleable Chip_closeIconEnabled 26
int styleable Chip_closeIconEndPadding 27
int styleable Chip_closeIconSize 28
int styleable Chip_closeIconStartPadding 29
int styleable Chip_closeIconTint 30
int styleable Chip_closeIconVisible 31
int styleable Chip_ensureMinTouchTargetSize 32
int styleable Chip_hideMotionSpec 33
int styleable Chip_iconEndPadding 34
int styleable Chip_iconStartPadding 35
int styleable Chip_rippleColor 36
int styleable Chip_shapeAppearance 37
int styleable Chip_shapeAppearanceOverlay 38
int styleable Chip_showMotionSpec 39
int styleable Chip_textEndPadding 40
int styleable Chip_textStartPadding 41
int[] styleable ChipGroup { 0x7f0300d3, 0x7f0300e8, 0x7f0300e9, 0x7f0300ea, 0x7f030435, 0x7f03045b, 0x7f03045d }
int styleable ChipGroup_checkedChip 0
int styleable ChipGroup_chipSpacing 1
int styleable ChipGroup_chipSpacingHorizontal 2
int styleable ChipGroup_chipSpacingVertical 3
int styleable ChipGroup_selectionRequired 4
int styleable ChipGroup_singleLine 5
int styleable ChipGroup_singleSelection 6
int[] styleable CircularProgressIndicator { 0x7f030287, 0x7f030289, 0x7f03028a }
int styleable CircularProgressIndicator_indicatorDirectionCircular 0
int styleable CircularProgressIndicator_indicatorInset 1
int styleable CircularProgressIndicator_indicatorSize 2
int[] styleable ClockFaceView { 0x7f0300fc, 0x7f0300ff }
int styleable ClockFaceView_clockFaceBackgroundColor 0
int styleable ClockFaceView_clockNumberTextColor 1
int[] styleable ClockHandView { 0x7f0300fd, 0x7f03034f, 0x7f030436 }
int styleable ClockHandView_clockHandColor 0
int styleable ClockHandView_materialCircleRadius 1
int styleable ClockHandView_selectorSize 2
int[] styleable CollapsingToolbarLayout { 0x7f03010b, 0x7f03010c, 0x7f03010d, 0x7f03016a, 0x7f0301f4, 0x7f0301f5, 0x7f0301f6, 0x7f0301f7, 0x7f0301f8, 0x7f0301f9, 0x7f0301fa, 0x7f0301fb, 0x7f030203, 0x7f030244, 0x7f030368, 0x7f030426, 0x7f030428, 0x7f03048b, 0x7f030536, 0x7f030538, 0x7f030539, 0x7f030540, 0x7f030543, 0x7f030546 }
int styleable CollapsingToolbarLayout_collapsedTitleGravity 0
int styleable CollapsingToolbarLayout_collapsedTitleTextAppearance 1
int styleable CollapsingToolbarLayout_collapsedTitleTextColor 2
int styleable CollapsingToolbarLayout_contentScrim 3
int styleable CollapsingToolbarLayout_expandedTitleGravity 4
int styleable CollapsingToolbarLayout_expandedTitleMargin 5
int styleable CollapsingToolbarLayout_expandedTitleMarginBottom 6
int styleable CollapsingToolbarLayout_expandedTitleMarginEnd 7
int styleable CollapsingToolbarLayout_expandedTitleMarginStart 8
int styleable CollapsingToolbarLayout_expandedTitleMarginTop 9
int styleable CollapsingToolbarLayout_expandedTitleTextAppearance 10
int styleable CollapsingToolbarLayout_expandedTitleTextColor 11
int styleable CollapsingToolbarLayout_extraMultilineHeightEnabled 12
int styleable CollapsingToolbarLayout_forceApplySystemWindowInsetTop 13
int styleable CollapsingToolbarLayout_maxLines 14
int styleable CollapsingToolbarLayout_scrimAnimationDuration 15
int styleable CollapsingToolbarLayout_scrimVisibleHeightTrigger 16
int styleable CollapsingToolbarLayout_statusBarScrim 17
int styleable CollapsingToolbarLayout_title 18
int styleable CollapsingToolbarLayout_titleCollapseMode 19
int styleable CollapsingToolbarLayout_titleEnabled 20
int styleable CollapsingToolbarLayout_titlePositionInterpolator 21
int styleable CollapsingToolbarLayout_titleTextEllipsize 22
int styleable CollapsingToolbarLayout_toolbarId 23
int[] styleable CollapsingToolbarLayout_Layout { 0x7f0302c9, 0x7f0302ca }
int styleable CollapsingToolbarLayout_Layout_layout_collapseMode 0
int styleable CollapsingToolbarLayout_Layout_layout_collapseParallaxMultiplier 1
int[] styleable ColorStateListItem { 0x010101a5, 0x0101031f, 0x01010647, 0x7f030035, 0x7f0302b6 }
int styleable ColorStateListItem_android_color 0
int styleable ColorStateListItem_android_alpha 1
int styleable ColorStateListItem_android_lStar 2
int styleable ColorStateListItem_alpha 3
int styleable ColorStateListItem_lStar 4
int[] styleable CompoundButton { 0x01010107, 0x7f03009c, 0x7f0300a7, 0x7f0300a8 }
int styleable CompoundButton_android_button 0
int styleable CompoundButton_buttonCompat 1
int styleable CompoundButton_buttonTint 2
int styleable CompoundButton_buttonTintMode 3
int[] styleable Constraint { 0x010100c4, 0x010100d0, 0x010100dc, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103b5, 0x010103b6, 0x010103fa, 0x01010440, 0x7f03003a, 0x7f03003d, 0x7f030071, 0x7f030072, 0x7f030073, 0x7f0300cc, 0x7f030158, 0x7f030159, 0x7f0301b8, 0x7f030225, 0x7f030226, 0x7f030227, 0x7f030228, 0x7f030229, 0x7f03022a, 0x7f03022b, 0x7f03022c, 0x7f03022d, 0x7f03022e, 0x7f03022f, 0x7f030230, 0x7f030231, 0x7f030233, 0x7f030234, 0x7f030235, 0x7f030236, 0x7f030237, 0x7f03025a, 0x7f0302cb, 0x7f0302cc, 0x7f0302cd, 0x7f0302ce, 0x7f0302cf, 0x7f0302d0, 0x7f0302d1, 0x7f0302d2, 0x7f0302d3, 0x7f0302d4, 0x7f0302d5, 0x7f0302d6, 0x7f0302d7, 0x7f0302d8, 0x7f0302d9, 0x7f0302da, 0x7f0302db, 0x7f0302dc, 0x7f0302dd, 0x7f0302de, 0x7f0302df, 0x7f0302e0, 0x7f0302e1, 0x7f0302e2, 0x7f0302e3, 0x7f0302e4, 0x7f0302e5, 0x7f0302e6, 0x7f0302e7, 0x7f0302e8, 0x7f0302e9, 0x7f0302ea, 0x7f0302eb, 0x7f0302ec, 0x7f0302ed, 0x7f0302ee, 0x7f0302ef, 0x7f0302f0, 0x7f0302f1, 0x7f0302f2, 0x7f0302f3, 0x7f0302f4, 0x7f0302f5, 0x7f0302f6, 0x7f0302f7, 0x7f0302f8, 0x7f0302fa, 0x7f0302fb, 0x7f0302fc, 0x7f0302fd, 0x7f0302fe, 0x7f0302ff, 0x7f030300, 0x7f030301, 0x7f030302, 0x7f030305, 0x7f03030a, 0x7f0303a6, 0x7f0303a7, 0x7f0303d8, 0x7f0303e4, 0x7f0303ea, 0x7f030407, 0x7f030408, 0x7f030409, 0x7f030561, 0x7f030563, 0x7f030565, 0x7f030583 }
int styleable Constraint_android_orientation 0
int styleable Constraint_android_id 1
int styleable Constraint_android_visibility 2
int styleable Constraint_android_layout_width 3
int styleable Constraint_android_layout_height 4
int styleable Constraint_android_layout_marginLeft 5
int styleable Constraint_android_layout_marginTop 6
int styleable Constraint_android_layout_marginRight 7
int styleable Constraint_android_layout_marginBottom 8
int styleable Constraint_android_maxWidth 9
int styleable Constraint_android_maxHeight 10
int styleable Constraint_android_minWidth 11
int styleable Constraint_android_minHeight 12
int styleable Constraint_android_alpha 13
int styleable Constraint_android_transformPivotX 14
int styleable Constraint_android_transformPivotY 15
int styleable Constraint_android_translationX 16
int styleable Constraint_android_translationY 17
int styleable Constraint_android_scaleX 18
int styleable Constraint_android_scaleY 19
int styleable Constraint_android_rotation 20
int styleable Constraint_android_rotationX 21
int styleable Constraint_android_rotationY 22
int styleable Constraint_android_layout_marginStart 23
int styleable Constraint_android_layout_marginEnd 24
int styleable Constraint_android_translationZ 25
int styleable Constraint_android_elevation 26
int styleable Constraint_animateCircleAngleTo 27
int styleable Constraint_animateRelativeTo 28
int styleable Constraint_barrierAllowsGoneWidgets 29
int styleable Constraint_barrierDirection 30
int styleable Constraint_barrierMargin 31
int styleable Constraint_chainUseRtl 32
int styleable Constraint_constraint_referenced_ids 33
int styleable Constraint_constraint_referenced_tags 34
int styleable Constraint_drawPath 35
int styleable Constraint_flow_firstHorizontalBias 36
int styleable Constraint_flow_firstHorizontalStyle 37
int styleable Constraint_flow_firstVerticalBias 38
int styleable Constraint_flow_firstVerticalStyle 39
int styleable Constraint_flow_horizontalAlign 40
int styleable Constraint_flow_horizontalBias 41
int styleable Constraint_flow_horizontalGap 42
int styleable Constraint_flow_horizontalStyle 43
int styleable Constraint_flow_lastHorizontalBias 44
int styleable Constraint_flow_lastHorizontalStyle 45
int styleable Constraint_flow_lastVerticalBias 46
int styleable Constraint_flow_lastVerticalStyle 47
int styleable Constraint_flow_maxElementsWrap 48
int styleable Constraint_flow_verticalAlign 49
int styleable Constraint_flow_verticalBias 50
int styleable Constraint_flow_verticalGap 51
int styleable Constraint_flow_verticalStyle 52
int styleable Constraint_flow_wrapMode 53
int styleable Constraint_guidelineUseRtl 54
int styleable Constraint_layout_constrainedHeight 55
int styleable Constraint_layout_constrainedWidth 56
int styleable Constraint_layout_constraintBaseline_creator 57
int styleable Constraint_layout_constraintBaseline_toBaselineOf 58
int styleable Constraint_layout_constraintBaseline_toBottomOf 59
int styleable Constraint_layout_constraintBaseline_toTopOf 60
int styleable Constraint_layout_constraintBottom_creator 61
int styleable Constraint_layout_constraintBottom_toBottomOf 62
int styleable Constraint_layout_constraintBottom_toTopOf 63
int styleable Constraint_layout_constraintCircle 64
int styleable Constraint_layout_constraintCircleAngle 65
int styleable Constraint_layout_constraintCircleRadius 66
int styleable Constraint_layout_constraintDimensionRatio 67
int styleable Constraint_layout_constraintEnd_toEndOf 68
int styleable Constraint_layout_constraintEnd_toStartOf 69
int styleable Constraint_layout_constraintGuide_begin 70
int styleable Constraint_layout_constraintGuide_end 71
int styleable Constraint_layout_constraintGuide_percent 72
int styleable Constraint_layout_constraintHeight 73
int styleable Constraint_layout_constraintHeight_default 74
int styleable Constraint_layout_constraintHeight_max 75
int styleable Constraint_layout_constraintHeight_min 76
int styleable Constraint_layout_constraintHeight_percent 77
int styleable Constraint_layout_constraintHorizontal_bias 78
int styleable Constraint_layout_constraintHorizontal_chainStyle 79
int styleable Constraint_layout_constraintHorizontal_weight 80
int styleable Constraint_layout_constraintLeft_creator 81
int styleable Constraint_layout_constraintLeft_toLeftOf 82
int styleable Constraint_layout_constraintLeft_toRightOf 83
int styleable Constraint_layout_constraintRight_creator 84
int styleable Constraint_layout_constraintRight_toLeftOf 85
int styleable Constraint_layout_constraintRight_toRightOf 86
int styleable Constraint_layout_constraintStart_toEndOf 87
int styleable Constraint_layout_constraintStart_toStartOf 88
int styleable Constraint_layout_constraintTag 89
int styleable Constraint_layout_constraintTop_creator 90
int styleable Constraint_layout_constraintTop_toBottomOf 91
int styleable Constraint_layout_constraintTop_toTopOf 92
int styleable Constraint_layout_constraintVertical_bias 93
int styleable Constraint_layout_constraintVertical_chainStyle 94
int styleable Constraint_layout_constraintVertical_weight 95
int styleable Constraint_layout_constraintWidth 96
int styleable Constraint_layout_constraintWidth_default 97
int styleable Constraint_layout_constraintWidth_max 98
int styleable Constraint_layout_constraintWidth_min 99
int styleable Constraint_layout_constraintWidth_percent 100
int styleable Constraint_layout_editor_absoluteX 101
int styleable Constraint_layout_editor_absoluteY 102
int styleable Constraint_layout_goneMarginBaseline 103
int styleable Constraint_layout_goneMarginBottom 104
int styleable Constraint_layout_goneMarginEnd 105
int styleable Constraint_layout_goneMarginLeft 106
int styleable Constraint_layout_goneMarginRight 107
int styleable Constraint_layout_goneMarginStart 108
int styleable Constraint_layout_goneMarginTop 109
int styleable Constraint_layout_marginBaseline 110
int styleable Constraint_layout_wrapBehaviorInParent 111
int styleable Constraint_motionProgress 112
int styleable Constraint_motionStagger 113
int styleable Constraint_pathMotionArc 114
int styleable Constraint_pivotAnchor 115
int styleable Constraint_polarRelativeTo 116
int styleable Constraint_quantizeMotionInterpolator 117
int styleable Constraint_quantizeMotionPhase 118
int styleable Constraint_quantizeMotionSteps 119
int styleable Constraint_transformPivotTarget 120
int styleable Constraint_transitionEasing 121
int styleable Constraint_transitionPathRotate 122
int styleable Constraint_visibilityMode 123
int[] styleable ConstraintLayout_Layout { 0x010100c4, 0x010100d5, 0x010100d6, 0x010100d7, 0x010100d8, 0x010100d9, 0x010100dc, 0x010100f4, 0x010100f5, 0x010100f6, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x010103b3, 0x010103b4, 0x010103b5, 0x010103b6, 0x01010440, 0x0101053b, 0x0101053c, 0x7f030071, 0x7f030072, 0x7f030073, 0x7f0300cc, 0x7f0300f4, 0x7f0300f5, 0x7f0300f6, 0x7f0300f7, 0x7f0300f8, 0x7f030155, 0x7f030158, 0x7f030159, 0x7f030225, 0x7f030226, 0x7f030227, 0x7f030228, 0x7f030229, 0x7f03022a, 0x7f03022b, 0x7f03022c, 0x7f03022d, 0x7f03022e, 0x7f03022f, 0x7f030230, 0x7f030231, 0x7f030233, 0x7f030234, 0x7f030235, 0x7f030236, 0x7f030237, 0x7f03025a, 0x7f0302c3, 0x7f0302cb, 0x7f0302cc, 0x7f0302cd, 0x7f0302ce, 0x7f0302cf, 0x7f0302d0, 0x7f0302d1, 0x7f0302d2, 0x7f0302d3, 0x7f0302d4, 0x7f0302d5, 0x7f0302d6, 0x7f0302d7, 0x7f0302d8, 0x7f0302d9, 0x7f0302da, 0x7f0302db, 0x7f0302dc, 0x7f0302dd, 0x7f0302de, 0x7f0302df, 0x7f0302e0, 0x7f0302e1, 0x7f0302e2, 0x7f0302e3, 0x7f0302e4, 0x7f0302e5, 0x7f0302e6, 0x7f0302e7, 0x7f0302e8, 0x7f0302e9, 0x7f0302ea, 0x7f0302eb, 0x7f0302ec, 0x7f0302ed, 0x7f0302ee, 0x7f0302ef, 0x7f0302f0, 0x7f0302f1, 0x7f0302f2, 0x7f0302f3, 0x7f0302f4, 0x7f0302f5, 0x7f0302f6, 0x7f0302f7, 0x7f0302f8, 0x7f0302fa, 0x7f0302fb, 0x7f0302fc, 0x7f0302fd, 0x7f0302fe, 0x7f0302ff, 0x7f030300, 0x7f030301, 0x7f030302, 0x7f030305, 0x7f030306, 0x7f03030a }
int styleable ConstraintLayout_Layout_android_orientation 0
int styleable ConstraintLayout_Layout_android_padding 1
int styleable ConstraintLayout_Layout_android_paddingLeft 2
int styleable ConstraintLayout_Layout_android_paddingTop 3
int styleable ConstraintLayout_Layout_android_paddingRight 4
int styleable ConstraintLayout_Layout_android_paddingBottom 5
int styleable ConstraintLayout_Layout_android_visibility 6
int styleable ConstraintLayout_Layout_android_layout_width 7
int styleable ConstraintLayout_Layout_android_layout_height 8
int styleable ConstraintLayout_Layout_android_layout_margin 9
int styleable ConstraintLayout_Layout_android_layout_marginLeft 10
int styleable ConstraintLayout_Layout_android_layout_marginTop 11
int styleable ConstraintLayout_Layout_android_layout_marginRight 12
int styleable ConstraintLayout_Layout_android_layout_marginBottom 13
int styleable ConstraintLayout_Layout_android_maxWidth 14
int styleable ConstraintLayout_Layout_android_maxHeight 15
int styleable ConstraintLayout_Layout_android_minWidth 16
int styleable ConstraintLayout_Layout_android_minHeight 17
int styleable ConstraintLayout_Layout_android_paddingStart 18
int styleable ConstraintLayout_Layout_android_paddingEnd 19
int styleable ConstraintLayout_Layout_android_layout_marginStart 20
int styleable ConstraintLayout_Layout_android_layout_marginEnd 21
int styleable ConstraintLayout_Layout_android_elevation 22
int styleable ConstraintLayout_Layout_android_layout_marginHorizontal 23
int styleable ConstraintLayout_Layout_android_layout_marginVertical 24
int styleable ConstraintLayout_Layout_barrierAllowsGoneWidgets 25
int styleable ConstraintLayout_Layout_barrierDirection 26
int styleable ConstraintLayout_Layout_barrierMargin 27
int styleable ConstraintLayout_Layout_chainUseRtl 28
int styleable ConstraintLayout_Layout_circularflow_angles 29
int styleable ConstraintLayout_Layout_circularflow_defaultAngle 30
int styleable ConstraintLayout_Layout_circularflow_defaultRadius 31
int styleable ConstraintLayout_Layout_circularflow_radiusInDP 32
int styleable ConstraintLayout_Layout_circularflow_viewCenter 33
int styleable ConstraintLayout_Layout_constraintSet 34
int styleable ConstraintLayout_Layout_constraint_referenced_ids 35
int styleable ConstraintLayout_Layout_constraint_referenced_tags 36
int styleable ConstraintLayout_Layout_flow_firstHorizontalBias 37
int styleable ConstraintLayout_Layout_flow_firstHorizontalStyle 38
int styleable ConstraintLayout_Layout_flow_firstVerticalBias 39
int styleable ConstraintLayout_Layout_flow_firstVerticalStyle 40
int styleable ConstraintLayout_Layout_flow_horizontalAlign 41
int styleable ConstraintLayout_Layout_flow_horizontalBias 42
int styleable ConstraintLayout_Layout_flow_horizontalGap 43
int styleable ConstraintLayout_Layout_flow_horizontalStyle 44
int styleable ConstraintLayout_Layout_flow_lastHorizontalBias 45
int styleable ConstraintLayout_Layout_flow_lastHorizontalStyle 46
int styleable ConstraintLayout_Layout_flow_lastVerticalBias 47
int styleable ConstraintLayout_Layout_flow_lastVerticalStyle 48
int styleable ConstraintLayout_Layout_flow_maxElementsWrap 49
int styleable ConstraintLayout_Layout_flow_verticalAlign 50
int styleable ConstraintLayout_Layout_flow_verticalBias 51
int styleable ConstraintLayout_Layout_flow_verticalGap 52
int styleable ConstraintLayout_Layout_flow_verticalStyle 53
int styleable ConstraintLayout_Layout_flow_wrapMode 54
int styleable ConstraintLayout_Layout_guidelineUseRtl 55
int styleable ConstraintLayout_Layout_layoutDescription 56
int styleable ConstraintLayout_Layout_layout_constrainedHeight 57
int styleable ConstraintLayout_Layout_layout_constrainedWidth 58
int styleable ConstraintLayout_Layout_layout_constraintBaseline_creator 59
int styleable ConstraintLayout_Layout_layout_constraintBaseline_toBaselineOf 60
int styleable ConstraintLayout_Layout_layout_constraintBaseline_toBottomOf 61
int styleable ConstraintLayout_Layout_layout_constraintBaseline_toTopOf 62
int styleable ConstraintLayout_Layout_layout_constraintBottom_creator 63
int styleable ConstraintLayout_Layout_layout_constraintBottom_toBottomOf 64
int styleable ConstraintLayout_Layout_layout_constraintBottom_toTopOf 65
int styleable ConstraintLayout_Layout_layout_constraintCircle 66
int styleable ConstraintLayout_Layout_layout_constraintCircleAngle 67
int styleable ConstraintLayout_Layout_layout_constraintCircleRadius 68
int styleable ConstraintLayout_Layout_layout_constraintDimensionRatio 69
int styleable ConstraintLayout_Layout_layout_constraintEnd_toEndOf 70
int styleable ConstraintLayout_Layout_layout_constraintEnd_toStartOf 71
int styleable ConstraintLayout_Layout_layout_constraintGuide_begin 72
int styleable ConstraintLayout_Layout_layout_constraintGuide_end 73
int styleable ConstraintLayout_Layout_layout_constraintGuide_percent 74
int styleable ConstraintLayout_Layout_layout_constraintHeight 75
int styleable ConstraintLayout_Layout_layout_constraintHeight_default 76
int styleable ConstraintLayout_Layout_layout_constraintHeight_max 77
int styleable ConstraintLayout_Layout_layout_constraintHeight_min 78
int styleable ConstraintLayout_Layout_layout_constraintHeight_percent 79
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_bias 80
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_chainStyle 81
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_weight 82
int styleable ConstraintLayout_Layout_layout_constraintLeft_creator 83
int styleable ConstraintLayout_Layout_layout_constraintLeft_toLeftOf 84
int styleable ConstraintLayout_Layout_layout_constraintLeft_toRightOf 85
int styleable ConstraintLayout_Layout_layout_constraintRight_creator 86
int styleable ConstraintLayout_Layout_layout_constraintRight_toLeftOf 87
int styleable ConstraintLayout_Layout_layout_constraintRight_toRightOf 88
int styleable ConstraintLayout_Layout_layout_constraintStart_toEndOf 89
int styleable ConstraintLayout_Layout_layout_constraintStart_toStartOf 90
int styleable ConstraintLayout_Layout_layout_constraintTag 91
int styleable ConstraintLayout_Layout_layout_constraintTop_creator 92
int styleable ConstraintLayout_Layout_layout_constraintTop_toBottomOf 93
int styleable ConstraintLayout_Layout_layout_constraintTop_toTopOf 94
int styleable ConstraintLayout_Layout_layout_constraintVertical_bias 95
int styleable ConstraintLayout_Layout_layout_constraintVertical_chainStyle 96
int styleable ConstraintLayout_Layout_layout_constraintVertical_weight 97
int styleable ConstraintLayout_Layout_layout_constraintWidth 98
int styleable ConstraintLayout_Layout_layout_constraintWidth_default 99
int styleable ConstraintLayout_Layout_layout_constraintWidth_max 100
int styleable ConstraintLayout_Layout_layout_constraintWidth_min 101
int styleable ConstraintLayout_Layout_layout_constraintWidth_percent 102
int styleable ConstraintLayout_Layout_layout_editor_absoluteX 103
int styleable ConstraintLayout_Layout_layout_editor_absoluteY 104
int styleable ConstraintLayout_Layout_layout_goneMarginBaseline 105
int styleable ConstraintLayout_Layout_layout_goneMarginBottom 106
int styleable ConstraintLayout_Layout_layout_goneMarginEnd 107
int styleable ConstraintLayout_Layout_layout_goneMarginLeft 108
int styleable ConstraintLayout_Layout_layout_goneMarginRight 109
int styleable ConstraintLayout_Layout_layout_goneMarginStart 110
int styleable ConstraintLayout_Layout_layout_goneMarginTop 111
int styleable ConstraintLayout_Layout_layout_marginBaseline 112
int styleable ConstraintLayout_Layout_layout_optimizationLevel 113
int styleable ConstraintLayout_Layout_layout_wrapBehaviorInParent 114
int[] styleable ConstraintLayout_ReactiveGuide { 0x7f030412, 0x7f030413, 0x7f030414, 0x7f030415 }
int styleable ConstraintLayout_ReactiveGuide_reactiveGuide_animateChange 0
int styleable ConstraintLayout_ReactiveGuide_reactiveGuide_applyToAllConstraintSets 1
int styleable ConstraintLayout_ReactiveGuide_reactiveGuide_applyToConstraintSet 2
int styleable ConstraintLayout_ReactiveGuide_reactiveGuide_valueId 3
int[] styleable ConstraintLayout_placeholder { 0x7f03015b, 0x7f0303e9 }
int styleable ConstraintLayout_placeholder_content 0
int styleable ConstraintLayout_placeholder_placeholder_emptyVisibility 1
int[] styleable ConstraintOverride { 0x010100c4, 0x010100d0, 0x010100dc, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103b5, 0x010103b6, 0x010103fa, 0x01010440, 0x7f03003a, 0x7f03003d, 0x7f030071, 0x7f030072, 0x7f030073, 0x7f0300cc, 0x7f030158, 0x7f0301b8, 0x7f030225, 0x7f030226, 0x7f030227, 0x7f030228, 0x7f030229, 0x7f03022a, 0x7f03022b, 0x7f03022c, 0x7f03022d, 0x7f03022e, 0x7f03022f, 0x7f030230, 0x7f030231, 0x7f030233, 0x7f030234, 0x7f030235, 0x7f030236, 0x7f030237, 0x7f03025a, 0x7f0302cb, 0x7f0302cc, 0x7f0302cd, 0x7f0302d1, 0x7f0302d5, 0x7f0302d6, 0x7f0302d7, 0x7f0302da, 0x7f0302db, 0x7f0302dc, 0x7f0302dd, 0x7f0302de, 0x7f0302df, 0x7f0302e0, 0x7f0302e1, 0x7f0302e2, 0x7f0302e3, 0x7f0302e4, 0x7f0302e5, 0x7f0302e8, 0x7f0302ed, 0x7f0302ee, 0x7f0302f1, 0x7f0302f2, 0x7f0302f3, 0x7f0302f4, 0x7f0302f5, 0x7f0302f6, 0x7f0302f7, 0x7f0302f8, 0x7f0302fa, 0x7f0302fb, 0x7f0302fc, 0x7f0302fd, 0x7f0302fe, 0x7f0302ff, 0x7f030300, 0x7f030301, 0x7f030302, 0x7f030305, 0x7f03030a, 0x7f0303a6, 0x7f0303a7, 0x7f0303a8, 0x7f0303d8, 0x7f0303e4, 0x7f0303ea, 0x7f030407, 0x7f030408, 0x7f030409, 0x7f030561, 0x7f030563, 0x7f030565, 0x7f030583 }
int styleable ConstraintOverride_android_orientation 0
int styleable ConstraintOverride_android_id 1
int styleable ConstraintOverride_android_visibility 2
int styleable ConstraintOverride_android_layout_width 3
int styleable ConstraintOverride_android_layout_height 4
int styleable ConstraintOverride_android_layout_marginLeft 5
int styleable ConstraintOverride_android_layout_marginTop 6
int styleable ConstraintOverride_android_layout_marginRight 7
int styleable ConstraintOverride_android_layout_marginBottom 8
int styleable ConstraintOverride_android_maxWidth 9
int styleable ConstraintOverride_android_maxHeight 10
int styleable ConstraintOverride_android_minWidth 11
int styleable ConstraintOverride_android_minHeight 12
int styleable ConstraintOverride_android_alpha 13
int styleable ConstraintOverride_android_transformPivotX 14
int styleable ConstraintOverride_android_transformPivotY 15
int styleable ConstraintOverride_android_translationX 16
int styleable ConstraintOverride_android_translationY 17
int styleable ConstraintOverride_android_scaleX 18
int styleable ConstraintOverride_android_scaleY 19
int styleable ConstraintOverride_android_rotation 20
int styleable ConstraintOverride_android_rotationX 21
int styleable ConstraintOverride_android_rotationY 22
int styleable ConstraintOverride_android_layout_marginStart 23
int styleable ConstraintOverride_android_layout_marginEnd 24
int styleable ConstraintOverride_android_translationZ 25
int styleable ConstraintOverride_android_elevation 26
int styleable ConstraintOverride_animateCircleAngleTo 27
int styleable ConstraintOverride_animateRelativeTo 28
int styleable ConstraintOverride_barrierAllowsGoneWidgets 29
int styleable ConstraintOverride_barrierDirection 30
int styleable ConstraintOverride_barrierMargin 31
int styleable ConstraintOverride_chainUseRtl 32
int styleable ConstraintOverride_constraint_referenced_ids 33
int styleable ConstraintOverride_drawPath 34
int styleable ConstraintOverride_flow_firstHorizontalBias 35
int styleable ConstraintOverride_flow_firstHorizontalStyle 36
int styleable ConstraintOverride_flow_firstVerticalBias 37
int styleable ConstraintOverride_flow_firstVerticalStyle 38
int styleable ConstraintOverride_flow_horizontalAlign 39
int styleable ConstraintOverride_flow_horizontalBias 40
int styleable ConstraintOverride_flow_horizontalGap 41
int styleable ConstraintOverride_flow_horizontalStyle 42
int styleable ConstraintOverride_flow_lastHorizontalBias 43
int styleable ConstraintOverride_flow_lastHorizontalStyle 44
int styleable ConstraintOverride_flow_lastVerticalBias 45
int styleable ConstraintOverride_flow_lastVerticalStyle 46
int styleable ConstraintOverride_flow_maxElementsWrap 47
int styleable ConstraintOverride_flow_verticalAlign 48
int styleable ConstraintOverride_flow_verticalBias 49
int styleable ConstraintOverride_flow_verticalGap 50
int styleable ConstraintOverride_flow_verticalStyle 51
int styleable ConstraintOverride_flow_wrapMode 52
int styleable ConstraintOverride_guidelineUseRtl 53
int styleable ConstraintOverride_layout_constrainedHeight 54
int styleable ConstraintOverride_layout_constrainedWidth 55
int styleable ConstraintOverride_layout_constraintBaseline_creator 56
int styleable ConstraintOverride_layout_constraintBottom_creator 57
int styleable ConstraintOverride_layout_constraintCircleAngle 58
int styleable ConstraintOverride_layout_constraintCircleRadius 59
int styleable ConstraintOverride_layout_constraintDimensionRatio 60
int styleable ConstraintOverride_layout_constraintGuide_begin 61
int styleable ConstraintOverride_layout_constraintGuide_end 62
int styleable ConstraintOverride_layout_constraintGuide_percent 63
int styleable ConstraintOverride_layout_constraintHeight 64
int styleable ConstraintOverride_layout_constraintHeight_default 65
int styleable ConstraintOverride_layout_constraintHeight_max 66
int styleable ConstraintOverride_layout_constraintHeight_min 67
int styleable ConstraintOverride_layout_constraintHeight_percent 68
int styleable ConstraintOverride_layout_constraintHorizontal_bias 69
int styleable ConstraintOverride_layout_constraintHorizontal_chainStyle 70
int styleable ConstraintOverride_layout_constraintHorizontal_weight 71
int styleable ConstraintOverride_layout_constraintLeft_creator 72
int styleable ConstraintOverride_layout_constraintRight_creator 73
int styleable ConstraintOverride_layout_constraintTag 74
int styleable ConstraintOverride_layout_constraintTop_creator 75
int styleable ConstraintOverride_layout_constraintVertical_bias 76
int styleable ConstraintOverride_layout_constraintVertical_chainStyle 77
int styleable ConstraintOverride_layout_constraintVertical_weight 78
int styleable ConstraintOverride_layout_constraintWidth 79
int styleable ConstraintOverride_layout_constraintWidth_default 80
int styleable ConstraintOverride_layout_constraintWidth_max 81
int styleable ConstraintOverride_layout_constraintWidth_min 82
int styleable ConstraintOverride_layout_constraintWidth_percent 83
int styleable ConstraintOverride_layout_editor_absoluteX 84
int styleable ConstraintOverride_layout_editor_absoluteY 85
int styleable ConstraintOverride_layout_goneMarginBaseline 86
int styleable ConstraintOverride_layout_goneMarginBottom 87
int styleable ConstraintOverride_layout_goneMarginEnd 88
int styleable ConstraintOverride_layout_goneMarginLeft 89
int styleable ConstraintOverride_layout_goneMarginRight 90
int styleable ConstraintOverride_layout_goneMarginStart 91
int styleable ConstraintOverride_layout_goneMarginTop 92
int styleable ConstraintOverride_layout_marginBaseline 93
int styleable ConstraintOverride_layout_wrapBehaviorInParent 94
int styleable ConstraintOverride_motionProgress 95
int styleable ConstraintOverride_motionStagger 96
int styleable ConstraintOverride_motionTarget 97
int styleable ConstraintOverride_pathMotionArc 98
int styleable ConstraintOverride_pivotAnchor 99
int styleable ConstraintOverride_polarRelativeTo 100
int styleable ConstraintOverride_quantizeMotionInterpolator 101
int styleable ConstraintOverride_quantizeMotionPhase 102
int styleable ConstraintOverride_quantizeMotionSteps 103
int styleable ConstraintOverride_transformPivotTarget 104
int styleable ConstraintOverride_transitionEasing 105
int styleable ConstraintOverride_transitionPathRotate 106
int styleable ConstraintOverride_visibilityMode 107
int[] styleable ConstraintSet { 0x010100c4, 0x010100d0, 0x010100dc, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x010101b5, 0x010101b6, 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103b5, 0x010103b6, 0x010103fa, 0x01010440, 0x7f03003a, 0x7f03003d, 0x7f030071, 0x7f030072, 0x7f030073, 0x7f0300cc, 0x7f030154, 0x7f030158, 0x7f030159, 0x7f0301a1, 0x7f0301b8, 0x7f030225, 0x7f030226, 0x7f030227, 0x7f030228, 0x7f030229, 0x7f03022a, 0x7f03022b, 0x7f03022c, 0x7f03022d, 0x7f03022e, 0x7f03022f, 0x7f030230, 0x7f030231, 0x7f030233, 0x7f030234, 0x7f030235, 0x7f030236, 0x7f030237, 0x7f03025a, 0x7f0302cb, 0x7f0302cc, 0x7f0302cd, 0x7f0302ce, 0x7f0302cf, 0x7f0302d0, 0x7f0302d1, 0x7f0302d2, 0x7f0302d3, 0x7f0302d4, 0x7f0302d5, 0x7f0302d6, 0x7f0302d7, 0x7f0302d8, 0x7f0302d9, 0x7f0302da, 0x7f0302db, 0x7f0302dc, 0x7f0302de, 0x7f0302df, 0x7f0302e0, 0x7f0302e1, 0x7f0302e2, 0x7f0302e3, 0x7f0302e4, 0x7f0302e5, 0x7f0302e6, 0x7f0302e7, 0x7f0302e8, 0x7f0302e9, 0x7f0302ea, 0x7f0302eb, 0x7f0302ec, 0x7f0302ed, 0x7f0302ee, 0x7f0302ef, 0x7f0302f0, 0x7f0302f1, 0x7f0302f2, 0x7f0302f3, 0x7f0302f5, 0x7f0302f6, 0x7f0302f7, 0x7f0302f8, 0x7f0302fa, 0x7f0302fb, 0x7f0302fc, 0x7f0302fd, 0x7f0302fe, 0x7f0302ff, 0x7f030300, 0x7f030301, 0x7f030302, 0x7f030305, 0x7f03030a, 0x7f0303a6, 0x7f0303a7, 0x7f0303d8, 0x7f0303e4, 0x7f0303ea, 0x7f030409, 0x7f03047f, 0x7f030563, 0x7f030565 }
int styleable ConstraintSet_android_orientation 0
int styleable ConstraintSet_android_id 1
int styleable ConstraintSet_android_visibility 2
int styleable ConstraintSet_android_layout_width 3
int styleable ConstraintSet_android_layout_height 4
int styleable ConstraintSet_android_layout_marginLeft 5
int styleable ConstraintSet_android_layout_marginTop 6
int styleable ConstraintSet_android_layout_marginRight 7
int styleable ConstraintSet_android_layout_marginBottom 8
int styleable ConstraintSet_android_maxWidth 9
int styleable ConstraintSet_android_maxHeight 10
int styleable ConstraintSet_android_minWidth 11
int styleable ConstraintSet_android_minHeight 12
int styleable ConstraintSet_android_pivotX 13
int styleable ConstraintSet_android_pivotY 14
int styleable ConstraintSet_android_alpha 15
int styleable ConstraintSet_android_transformPivotX 16
int styleable ConstraintSet_android_transformPivotY 17
int styleable ConstraintSet_android_translationX 18
int styleable ConstraintSet_android_translationY 19
int styleable ConstraintSet_android_scaleX 20
int styleable ConstraintSet_android_scaleY 21
int styleable ConstraintSet_android_rotation 22
int styleable ConstraintSet_android_rotationX 23
int styleable ConstraintSet_android_rotationY 24
int styleable ConstraintSet_android_layout_marginStart 25
int styleable ConstraintSet_android_layout_marginEnd 26
int styleable ConstraintSet_android_translationZ 27
int styleable ConstraintSet_android_elevation 28
int styleable ConstraintSet_animateCircleAngleTo 29
int styleable ConstraintSet_animateRelativeTo 30
int styleable ConstraintSet_barrierAllowsGoneWidgets 31
int styleable ConstraintSet_barrierDirection 32
int styleable ConstraintSet_barrierMargin 33
int styleable ConstraintSet_chainUseRtl 34
int styleable ConstraintSet_constraintRotate 35
int styleable ConstraintSet_constraint_referenced_ids 36
int styleable ConstraintSet_constraint_referenced_tags 37
int styleable ConstraintSet_deriveConstraintsFrom 38
int styleable ConstraintSet_drawPath 39
int styleable ConstraintSet_flow_firstHorizontalBias 40
int styleable ConstraintSet_flow_firstHorizontalStyle 41
int styleable ConstraintSet_flow_firstVerticalBias 42
int styleable ConstraintSet_flow_firstVerticalStyle 43
int styleable ConstraintSet_flow_horizontalAlign 44
int styleable ConstraintSet_flow_horizontalBias 45
int styleable ConstraintSet_flow_horizontalGap 46
int styleable ConstraintSet_flow_horizontalStyle 47
int styleable ConstraintSet_flow_lastHorizontalBias 48
int styleable ConstraintSet_flow_lastHorizontalStyle 49
int styleable ConstraintSet_flow_lastVerticalBias 50
int styleable ConstraintSet_flow_lastVerticalStyle 51
int styleable ConstraintSet_flow_maxElementsWrap 52
int styleable ConstraintSet_flow_verticalAlign 53
int styleable ConstraintSet_flow_verticalBias 54
int styleable ConstraintSet_flow_verticalGap 55
int styleable ConstraintSet_flow_verticalStyle 56
int styleable ConstraintSet_flow_wrapMode 57
int styleable ConstraintSet_guidelineUseRtl 58
int styleable ConstraintSet_layout_constrainedHeight 59
int styleable ConstraintSet_layout_constrainedWidth 60
int styleable ConstraintSet_layout_constraintBaseline_creator 61
int styleable ConstraintSet_layout_constraintBaseline_toBaselineOf 62
int styleable ConstraintSet_layout_constraintBaseline_toBottomOf 63
int styleable ConstraintSet_layout_constraintBaseline_toTopOf 64
int styleable ConstraintSet_layout_constraintBottom_creator 65
int styleable ConstraintSet_layout_constraintBottom_toBottomOf 66
int styleable ConstraintSet_layout_constraintBottom_toTopOf 67
int styleable ConstraintSet_layout_constraintCircle 68
int styleable ConstraintSet_layout_constraintCircleAngle 69
int styleable ConstraintSet_layout_constraintCircleRadius 70
int styleable ConstraintSet_layout_constraintDimensionRatio 71
int styleable ConstraintSet_layout_constraintEnd_toEndOf 72
int styleable ConstraintSet_layout_constraintEnd_toStartOf 73
int styleable ConstraintSet_layout_constraintGuide_begin 74
int styleable ConstraintSet_layout_constraintGuide_end 75
int styleable ConstraintSet_layout_constraintGuide_percent 76
int styleable ConstraintSet_layout_constraintHeight_default 77
int styleable ConstraintSet_layout_constraintHeight_max 78
int styleable ConstraintSet_layout_constraintHeight_min 79
int styleable ConstraintSet_layout_constraintHeight_percent 80
int styleable ConstraintSet_layout_constraintHorizontal_bias 81
int styleable ConstraintSet_layout_constraintHorizontal_chainStyle 82
int styleable ConstraintSet_layout_constraintHorizontal_weight 83
int styleable ConstraintSet_layout_constraintLeft_creator 84
int styleable ConstraintSet_layout_constraintLeft_toLeftOf 85
int styleable ConstraintSet_layout_constraintLeft_toRightOf 86
int styleable ConstraintSet_layout_constraintRight_creator 87
int styleable ConstraintSet_layout_constraintRight_toLeftOf 88
int styleable ConstraintSet_layout_constraintRight_toRightOf 89
int styleable ConstraintSet_layout_constraintStart_toEndOf 90
int styleable ConstraintSet_layout_constraintStart_toStartOf 91
int styleable ConstraintSet_layout_constraintTag 92
int styleable ConstraintSet_layout_constraintTop_creator 93
int styleable ConstraintSet_layout_constraintTop_toBottomOf 94
int styleable ConstraintSet_layout_constraintTop_toTopOf 95
int styleable ConstraintSet_layout_constraintVertical_bias 96
int styleable ConstraintSet_layout_constraintVertical_chainStyle 97
int styleable ConstraintSet_layout_constraintVertical_weight 98
int styleable ConstraintSet_layout_constraintWidth_default 99
int styleable ConstraintSet_layout_constraintWidth_max 100
int styleable ConstraintSet_layout_constraintWidth_min 101
int styleable ConstraintSet_layout_constraintWidth_percent 102
int styleable ConstraintSet_layout_editor_absoluteX 103
int styleable ConstraintSet_layout_editor_absoluteY 104
int styleable ConstraintSet_layout_goneMarginBaseline 105
int styleable ConstraintSet_layout_goneMarginBottom 106
int styleable ConstraintSet_layout_goneMarginEnd 107
int styleable ConstraintSet_layout_goneMarginLeft 108
int styleable ConstraintSet_layout_goneMarginRight 109
int styleable ConstraintSet_layout_goneMarginStart 110
int styleable ConstraintSet_layout_goneMarginTop 111
int styleable ConstraintSet_layout_marginBaseline 112
int styleable ConstraintSet_layout_wrapBehaviorInParent 113
int styleable ConstraintSet_motionProgress 114
int styleable ConstraintSet_motionStagger 115
int styleable ConstraintSet_pathMotionArc 116
int styleable ConstraintSet_pivotAnchor 117
int styleable ConstraintSet_polarRelativeTo 118
int styleable ConstraintSet_quantizeMotionSteps 119
int styleable ConstraintSet_stateLabels 120
int styleable ConstraintSet_transitionEasing 121
int styleable ConstraintSet_transitionPathRotate 122
int[] styleable CoordinatorLayout { 0x7f0302b5, 0x7f030489 }
int styleable CoordinatorLayout_keylines 0
int styleable CoordinatorLayout_statusBarBackground 1
int[] styleable CoordinatorLayout_Layout { 0x010100b3, 0x7f0302c6, 0x7f0302c7, 0x7f0302c8, 0x7f0302f9, 0x7f030303, 0x7f030304 }
int styleable CoordinatorLayout_Layout_android_layout_gravity 0
int styleable CoordinatorLayout_Layout_layout_anchor 1
int styleable CoordinatorLayout_Layout_layout_anchorGravity 2
int styleable CoordinatorLayout_Layout_layout_behavior 3
int styleable CoordinatorLayout_Layout_layout_dodgeInsetEdges 4
int styleable CoordinatorLayout_Layout_layout_insetEdge 5
int styleable CoordinatorLayout_Layout_layout_keyline 6
int[] styleable CustomAttribute { 0x7f030047, 0x7f030187, 0x7f030188, 0x7f030189, 0x7f03018a, 0x7f03018b, 0x7f03018c, 0x7f03018e, 0x7f03018f, 0x7f030190, 0x7f030370 }
int styleable CustomAttribute_attributeName 0
int styleable CustomAttribute_customBoolean 1
int styleable CustomAttribute_customColorDrawableValue 2
int styleable CustomAttribute_customColorValue 3
int styleable CustomAttribute_customDimension 4
int styleable CustomAttribute_customFloatValue 5
int styleable CustomAttribute_customIntegerValue 6
int styleable CustomAttribute_customPixelDimension 7
int styleable CustomAttribute_customReference 8
int styleable CustomAttribute_customStringValue 9
int styleable CustomAttribute_methodName 10
int[] styleable CustomWalletTheme { 0x7f030191, 0x7f03054a, 0x7f030597 }
int styleable CustomWalletTheme_customThemeStyle 0
int styleable CustomWalletTheme_toolbarTextColorStyle 1
int styleable CustomWalletTheme_windowTransitionStyle 2
int[] styleable DialogPreference { 0x010101f2, 0x010101f3, 0x010101f4, 0x010101f5, 0x010101f6, 0x010101f7, 0x7f0301a4, 0x7f0301a5, 0x7f0301a6, 0x7f0301aa, 0x7f0303b4, 0x7f0303f4 }
int styleable DialogPreference_android_dialogTitle 0
int styleable DialogPreference_android_dialogMessage 1
int styleable DialogPreference_android_dialogIcon 2
int styleable DialogPreference_android_positiveButtonText 3
int styleable DialogPreference_android_negativeButtonText 4
int styleable DialogPreference_android_dialogLayout 5
int styleable DialogPreference_dialogIcon 6
int styleable DialogPreference_dialogLayout 7
int styleable DialogPreference_dialogMessage 8
int styleable DialogPreference_dialogTitle 9
int styleable DialogPreference_negativeButtonText 10
int styleable DialogPreference_positiveButtonText 11
int[] styleable DrawerArrowToggle { 0x7f030045, 0x7f030046, 0x7f030070, 0x7f030113, 0x7f0301bd, 0x7f03024b, 0x7f030464, 0x7f03051a }
int styleable DrawerArrowToggle_arrowHeadLength 0
int styleable DrawerArrowToggle_arrowShaftLength 1
int styleable DrawerArrowToggle_barLength 2
int styleable DrawerArrowToggle_color 3
int styleable DrawerArrowToggle_drawableSize 4
int styleable DrawerArrowToggle_gapBetweenBars 5
int styleable DrawerArrowToggle_spinBars 6
int styleable DrawerArrowToggle_thickness 7
int[] styleable DrawerLayout { 0x7f0301cf }
int styleable DrawerLayout_elevation 0
int[] styleable EditTextPreference { 0x7f030579 }
int styleable EditTextPreference_useSimpleSummaryProvider 0
int[] styleable ExtendedFloatingActionButton { 0x7f03010a, 0x7f0301cf, 0x7f0301fc, 0x7f0301fd, 0x7f030264, 0x7f03044e, 0x7f030453 }
int styleable ExtendedFloatingActionButton_collapsedSize 0
int styleable ExtendedFloatingActionButton_elevation 1
int styleable ExtendedFloatingActionButton_extendMotionSpec 2
int styleable ExtendedFloatingActionButton_extendStrategy 3
int styleable ExtendedFloatingActionButton_hideMotionSpec 4
int styleable ExtendedFloatingActionButton_showMotionSpec 5
int styleable ExtendedFloatingActionButton_shrinkMotionSpec 6
int[] styleable ExtendedFloatingActionButton_Behavior_Layout { 0x7f030074, 0x7f030075 }
int styleable ExtendedFloatingActionButton_Behavior_Layout_behavior_autoHide 0
int styleable ExtendedFloatingActionButton_Behavior_Layout_behavior_autoShrink 1
int[] styleable FloatingActionButton { 0x0101000e, 0x7f03005d, 0x7f03005e, 0x7f030083, 0x7f0301cf, 0x7f0301e1, 0x7f03020b, 0x7f03020c, 0x7f030264, 0x7f030270, 0x7f030367, 0x7f030403, 0x7f03041e, 0x7f030438, 0x7f030440, 0x7f03044e, 0x7f030576 }
int styleable FloatingActionButton_android_enabled 0
int styleable FloatingActionButton_backgroundTint 1
int styleable FloatingActionButton_backgroundTintMode 2
int styleable FloatingActionButton_borderWidth 3
int styleable FloatingActionButton_elevation 4
int styleable FloatingActionButton_ensureMinTouchTargetSize 5
int styleable FloatingActionButton_fabCustomSize 6
int styleable FloatingActionButton_fabSize 7
int styleable FloatingActionButton_hideMotionSpec 8
int styleable FloatingActionButton_hoveredFocusedTranslationZ 9
int styleable FloatingActionButton_maxImageSize 10
int styleable FloatingActionButton_pressedTranslationZ 11
int styleable FloatingActionButton_rippleColor 12
int styleable FloatingActionButton_shapeAppearance 13
int styleable FloatingActionButton_shapeAppearanceOverlay 14
int styleable FloatingActionButton_showMotionSpec 15
int styleable FloatingActionButton_useCompatPadding 16
int[] styleable FloatingActionButton_Behavior_Layout { 0x7f030074 }
int styleable FloatingActionButton_Behavior_Layout_behavior_autoHide 0
int[] styleable FlowLayout { 0x7f0302a9, 0x7f030310 }
int styleable FlowLayout_itemSpacing 0
int styleable FlowLayout_lineSpacing 1
int[] styleable FontFamily { 0x7f03023a, 0x7f03023b, 0x7f03023c, 0x7f03023d, 0x7f03023e, 0x7f03023f, 0x7f030240 }
int styleable FontFamily_fontProviderAuthority 0
int styleable FontFamily_fontProviderCerts 1
int styleable FontFamily_fontProviderFetchStrategy 2
int styleable FontFamily_fontProviderFetchTimeout 3
int styleable FontFamily_fontProviderPackage 4
int styleable FontFamily_fontProviderQuery 5
int styleable FontFamily_fontProviderSystemFontFamily 6
int[] styleable FontFamilyFont { 0x01010532, 0x01010533, 0x0101053f, 0x0101056f, 0x01010570, 0x7f030238, 0x7f030241, 0x7f030242, 0x7f030243, 0x7f03056a }
int styleable FontFamilyFont_android_font 0
int styleable FontFamilyFont_android_fontWeight 1
int styleable FontFamilyFont_android_fontStyle 2
int styleable FontFamilyFont_android_ttcIndex 3
int styleable FontFamilyFont_android_fontVariationSettings 4
int styleable FontFamilyFont_font 5
int styleable FontFamilyFont_fontStyle 6
int styleable FontFamilyFont_fontVariationSettings 7
int styleable FontFamilyFont_fontWeight 8
int styleable FontFamilyFont_ttcIndex 9
int[] styleable ForegroundLinearLayout { 0x01010109, 0x01010200, 0x7f030246 }
int styleable ForegroundLinearLayout_android_foreground 0
int styleable ForegroundLinearLayout_android_foregroundGravity 1
int styleable ForegroundLinearLayout_foregroundInsidePadding 2
int[] styleable Fragment { 0x01010003, 0x010100d0, 0x010100d1 }
int styleable Fragment_android_name 0
int styleable Fragment_android_id 1
int styleable Fragment_android_tag 2
int[] styleable FragmentContainerView { 0x01010003, 0x010100d1 }
int styleable FragmentContainerView_android_name 0
int styleable FragmentContainerView_android_tag 1
int[] styleable GradientColor { 0x0101019d, 0x0101019e, 0x010101a1, 0x010101a2, 0x010101a3, 0x010101a4, 0x01010201, 0x0101020b, 0x01010510, 0x01010511, 0x01010512, 0x01010513 }
int styleable GradientColor_android_startColor 0
int styleable GradientColor_android_endColor 1
int styleable GradientColor_android_type 2
int styleable GradientColor_android_centerX 3
int styleable GradientColor_android_centerY 4
int styleable GradientColor_android_gradientRadius 5
int styleable GradientColor_android_tileMode 6
int styleable GradientColor_android_centerColor 7
int styleable GradientColor_android_startX 8
int styleable GradientColor_android_startY 9
int styleable GradientColor_android_endX 10
int styleable GradientColor_android_endY 11
int[] styleable GradientColorItem { 0x010101a5, 0x01010514 }
int styleable GradientColorItem_android_color 0
int styleable GradientColorItem_android_offset 1
int[] styleable Grid { 0x7f03024f, 0x7f030250, 0x7f030251, 0x7f030252, 0x7f030253, 0x7f030254, 0x7f030255, 0x7f030256, 0x7f030257, 0x7f030258, 0x7f030259 }
int styleable Grid_grid_columnWeights 0
int styleable Grid_grid_columns 1
int styleable Grid_grid_horizontalGaps 2
int styleable Grid_grid_orientation 3
int styleable Grid_grid_rowWeights 4
int styleable Grid_grid_rows 5
int styleable Grid_grid_skips 6
int styleable Grid_grid_spans 7
int styleable Grid_grid_useRtl 8
int styleable Grid_grid_validateInputs 9
int styleable Grid_grid_verticalGaps 10
int[] styleable ImageFilterView { 0x7f030037, 0x7f030080, 0x7f030096, 0x7f03016b, 0x7f030182, 0x7f030280, 0x7f030281, 0x7f030282, 0x7f030283, 0x7f0303c6, 0x7f030420, 0x7f030421, 0x7f030423, 0x7f030585 }
int styleable ImageFilterView_altSrc 0
int styleable ImageFilterView_blendSrc 1
int styleable ImageFilterView_brightness 2
int styleable ImageFilterView_contrast 3
int styleable ImageFilterView_crossfade 4
int styleable ImageFilterView_imagePanX 5
int styleable ImageFilterView_imagePanY 6
int styleable ImageFilterView_imageRotate 7
int styleable ImageFilterView_imageZoom 8
int styleable ImageFilterView_overlay 9
int styleable ImageFilterView_round 10
int styleable ImageFilterView_roundPercent 11
int styleable ImageFilterView_saturation 12
int styleable ImageFilterView_warmth 13
int[] styleable Insets { 0x7f030329, 0x7f03032a, 0x7f03032b, 0x7f0303c8, 0x7f0303ca, 0x7f0303cb, 0x7f0303cd, 0x7f0303cf }
int styleable Insets_marginLeftSystemWindowInsets 0
int styleable Insets_marginRightSystemWindowInsets 1
int styleable Insets_marginTopSystemWindowInsets 2
int styleable Insets_paddingBottomSystemWindowInsets 3
int styleable Insets_paddingLeftSystemWindowInsets 4
int styleable Insets_paddingRightSystemWindowInsets 5
int styleable Insets_paddingStartSystemWindowInsets 6
int styleable Insets_paddingTopSystemWindowInsets 7
int[] styleable KeyAttribute { 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f030186, 0x7f03024a, 0x7f0303a6, 0x7f0303a8, 0x7f030561, 0x7f030563, 0x7f030565 }
int styleable KeyAttribute_android_alpha 0
int styleable KeyAttribute_android_transformPivotX 1
int styleable KeyAttribute_android_transformPivotY 2
int styleable KeyAttribute_android_translationX 3
int styleable KeyAttribute_android_translationY 4
int styleable KeyAttribute_android_scaleX 5
int styleable KeyAttribute_android_scaleY 6
int styleable KeyAttribute_android_rotation 7
int styleable KeyAttribute_android_rotationX 8
int styleable KeyAttribute_android_rotationY 9
int styleable KeyAttribute_android_translationZ 10
int styleable KeyAttribute_android_elevation 11
int styleable KeyAttribute_curveFit 12
int styleable KeyAttribute_framePosition 13
int styleable KeyAttribute_motionProgress 14
int styleable KeyAttribute_motionTarget 15
int styleable KeyAttribute_transformPivotTarget 16
int styleable KeyAttribute_transitionEasing 17
int styleable KeyAttribute_transitionPathRotate 18
int[] styleable KeyCycle { 0x0101031f, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f030186, 0x7f03024a, 0x7f0303a6, 0x7f0303a8, 0x7f030563, 0x7f030565, 0x7f030587, 0x7f030588, 0x7f030589, 0x7f03058a, 0x7f03058b }
int styleable KeyCycle_android_alpha 0
int styleable KeyCycle_android_translationX 1
int styleable KeyCycle_android_translationY 2
int styleable KeyCycle_android_scaleX 3
int styleable KeyCycle_android_scaleY 4
int styleable KeyCycle_android_rotation 5
int styleable KeyCycle_android_rotationX 6
int styleable KeyCycle_android_rotationY 7
int styleable KeyCycle_android_translationZ 8
int styleable KeyCycle_android_elevation 9
int styleable KeyCycle_curveFit 10
int styleable KeyCycle_framePosition 11
int styleable KeyCycle_motionProgress 12
int styleable KeyCycle_motionTarget 13
int styleable KeyCycle_transitionEasing 14
int styleable KeyCycle_transitionPathRotate 15
int styleable KeyCycle_waveOffset 16
int styleable KeyCycle_wavePeriod 17
int styleable KeyCycle_wavePhase 18
int styleable KeyCycle_waveShape 19
int styleable KeyCycle_waveVariesBy 20
int[] styleable KeyFrame { }
int[] styleable KeyFramesAcceleration { }
int[] styleable KeyFramesVelocity { }
int[] styleable KeyPosition { 0x7f030186, 0x7f0301b8, 0x7f03024a, 0x7f0302b3, 0x7f0303a8, 0x7f0303d8, 0x7f0303de, 0x7f0303df, 0x7f0303e0, 0x7f0303e1, 0x7f03045e, 0x7f030563 }
int styleable KeyPosition_curveFit 0
int styleable KeyPosition_drawPath 1
int styleable KeyPosition_framePosition 2
int styleable KeyPosition_keyPositionType 3
int styleable KeyPosition_motionTarget 4
int styleable KeyPosition_pathMotionArc 5
int styleable KeyPosition_percentHeight 6
int styleable KeyPosition_percentWidth 7
int styleable KeyPosition_percentX 8
int styleable KeyPosition_percentY 9
int styleable KeyPosition_sizePercent 10
int styleable KeyPosition_transitionEasing 11
int[] styleable KeyTimeCycle { 0x0101031f, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f030186, 0x7f03024a, 0x7f0303a6, 0x7f0303a8, 0x7f030563, 0x7f030565, 0x7f030586, 0x7f030587, 0x7f030588, 0x7f030589, 0x7f03058a }
int styleable KeyTimeCycle_android_alpha 0
int styleable KeyTimeCycle_android_translationX 1
int styleable KeyTimeCycle_android_translationY 2
int styleable KeyTimeCycle_android_scaleX 3
int styleable KeyTimeCycle_android_scaleY 4
int styleable KeyTimeCycle_android_rotation 5
int styleable KeyTimeCycle_android_rotationX 6
int styleable KeyTimeCycle_android_rotationY 7
int styleable KeyTimeCycle_android_translationZ 8
int styleable KeyTimeCycle_android_elevation 9
int styleable KeyTimeCycle_curveFit 10
int styleable KeyTimeCycle_framePosition 11
int styleable KeyTimeCycle_motionProgress 12
int styleable KeyTimeCycle_motionTarget 13
int styleable KeyTimeCycle_transitionEasing 14
int styleable KeyTimeCycle_transitionPathRotate 15
int styleable KeyTimeCycle_waveDecay 16
int styleable KeyTimeCycle_waveOffset 17
int styleable KeyTimeCycle_wavePeriod 18
int styleable KeyTimeCycle_wavePhase 19
int styleable KeyTimeCycle_waveShape 20
int[] styleable KeyTrigger { 0x7f03024a, 0x7f0303a8, 0x7f0303a9, 0x7f0303aa, 0x7f0303bc, 0x7f0303be, 0x7f0303bf, 0x7f030567, 0x7f030568, 0x7f030569, 0x7f030580, 0x7f030581, 0x7f030582 }
int styleable KeyTrigger_framePosition 0
int styleable KeyTrigger_motionTarget 1
int styleable KeyTrigger_motion_postLayoutCollision 2
int styleable KeyTrigger_motion_triggerOnCollision 3
int styleable KeyTrigger_onCross 4
int styleable KeyTrigger_onNegativeCross 5
int styleable KeyTrigger_onPositiveCross 6
int styleable KeyTrigger_triggerId 7
int styleable KeyTrigger_triggerReceiver 8
int styleable KeyTrigger_triggerSlack 9
int styleable KeyTrigger_viewTransitionOnCross 10
int styleable KeyTrigger_viewTransitionOnNegativeCross 11
int styleable KeyTrigger_viewTransitionOnPositiveCross 12
int[] styleable Layout { 0x010100c4, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x010103b5, 0x010103b6, 0x7f030071, 0x7f030072, 0x7f030073, 0x7f0300cc, 0x7f030158, 0x7f030159, 0x7f03025a, 0x7f0302cb, 0x7f0302cc, 0x7f0302cd, 0x7f0302ce, 0x7f0302cf, 0x7f0302d0, 0x7f0302d1, 0x7f0302d2, 0x7f0302d3, 0x7f0302d4, 0x7f0302d5, 0x7f0302d6, 0x7f0302d7, 0x7f0302d8, 0x7f0302d9, 0x7f0302da, 0x7f0302db, 0x7f0302dc, 0x7f0302dd, 0x7f0302de, 0x7f0302df, 0x7f0302e0, 0x7f0302e1, 0x7f0302e2, 0x7f0302e3, 0x7f0302e4, 0x7f0302e5, 0x7f0302e6, 0x7f0302e7, 0x7f0302e8, 0x7f0302e9, 0x7f0302ea, 0x7f0302eb, 0x7f0302ec, 0x7f0302ee, 0x7f0302ef, 0x7f0302f0, 0x7f0302f1, 0x7f0302f2, 0x7f0302f3, 0x7f0302f4, 0x7f0302f5, 0x7f0302f6, 0x7f0302f7, 0x7f0302f8, 0x7f0302fa, 0x7f0302fb, 0x7f0302fc, 0x7f0302fd, 0x7f0302fe, 0x7f0302ff, 0x7f030300, 0x7f030301, 0x7f030302, 0x7f030305, 0x7f03030a, 0x7f030366, 0x7f03036b, 0x7f030373, 0x7f030377 }
int styleable Layout_android_orientation 0
int styleable Layout_android_layout_width 1
int styleable Layout_android_layout_height 2
int styleable Layout_android_layout_marginLeft 3
int styleable Layout_android_layout_marginTop 4
int styleable Layout_android_layout_marginRight 5
int styleable Layout_android_layout_marginBottom 6
int styleable Layout_android_layout_marginStart 7
int styleable Layout_android_layout_marginEnd 8
int styleable Layout_barrierAllowsGoneWidgets 9
int styleable Layout_barrierDirection 10
int styleable Layout_barrierMargin 11
int styleable Layout_chainUseRtl 12
int styleable Layout_constraint_referenced_ids 13
int styleable Layout_constraint_referenced_tags 14
int styleable Layout_guidelineUseRtl 15
int styleable Layout_layout_constrainedHeight 16
int styleable Layout_layout_constrainedWidth 17
int styleable Layout_layout_constraintBaseline_creator 18
int styleable Layout_layout_constraintBaseline_toBaselineOf 19
int styleable Layout_layout_constraintBaseline_toBottomOf 20
int styleable Layout_layout_constraintBaseline_toTopOf 21
int styleable Layout_layout_constraintBottom_creator 22
int styleable Layout_layout_constraintBottom_toBottomOf 23
int styleable Layout_layout_constraintBottom_toTopOf 24
int styleable Layout_layout_constraintCircle 25
int styleable Layout_layout_constraintCircleAngle 26
int styleable Layout_layout_constraintCircleRadius 27
int styleable Layout_layout_constraintDimensionRatio 28
int styleable Layout_layout_constraintEnd_toEndOf 29
int styleable Layout_layout_constraintEnd_toStartOf 30
int styleable Layout_layout_constraintGuide_begin 31
int styleable Layout_layout_constraintGuide_end 32
int styleable Layout_layout_constraintGuide_percent 33
int styleable Layout_layout_constraintHeight 34
int styleable Layout_layout_constraintHeight_default 35
int styleable Layout_layout_constraintHeight_max 36
int styleable Layout_layout_constraintHeight_min 37
int styleable Layout_layout_constraintHeight_percent 38
int styleable Layout_layout_constraintHorizontal_bias 39
int styleable Layout_layout_constraintHorizontal_chainStyle 40
int styleable Layout_layout_constraintHorizontal_weight 41
int styleable Layout_layout_constraintLeft_creator 42
int styleable Layout_layout_constraintLeft_toLeftOf 43
int styleable Layout_layout_constraintLeft_toRightOf 44
int styleable Layout_layout_constraintRight_creator 45
int styleable Layout_layout_constraintRight_toLeftOf 46
int styleable Layout_layout_constraintRight_toRightOf 47
int styleable Layout_layout_constraintStart_toEndOf 48
int styleable Layout_layout_constraintStart_toStartOf 49
int styleable Layout_layout_constraintTop_creator 50
int styleable Layout_layout_constraintTop_toBottomOf 51
int styleable Layout_layout_constraintTop_toTopOf 52
int styleable Layout_layout_constraintVertical_bias 53
int styleable Layout_layout_constraintVertical_chainStyle 54
int styleable Layout_layout_constraintVertical_weight 55
int styleable Layout_layout_constraintWidth 56
int styleable Layout_layout_constraintWidth_default 57
int styleable Layout_layout_constraintWidth_max 58
int styleable Layout_layout_constraintWidth_min 59
int styleable Layout_layout_constraintWidth_percent 60
int styleable Layout_layout_editor_absoluteX 61
int styleable Layout_layout_editor_absoluteY 62
int styleable Layout_layout_goneMarginBaseline 63
int styleable Layout_layout_goneMarginBottom 64
int styleable Layout_layout_goneMarginEnd 65
int styleable Layout_layout_goneMarginLeft 66
int styleable Layout_layout_goneMarginRight 67
int styleable Layout_layout_goneMarginStart 68
int styleable Layout_layout_goneMarginTop 69
int styleable Layout_layout_marginBaseline 70
int styleable Layout_layout_wrapBehaviorInParent 71
int styleable Layout_maxHeight 72
int styleable Layout_maxWidth 73
int styleable Layout_minHeight 74
int styleable Layout_minWidth 75
int[] styleable LinearLayoutCompat { 0x010100af, 0x010100c4, 0x01010126, 0x01010127, 0x01010128, 0x7f0301ad, 0x7f0301b2, 0x7f03036c, 0x7f03044c }
int styleable LinearLayoutCompat_android_gravity 0
int styleable LinearLayoutCompat_android_orientation 1
int styleable LinearLayoutCompat_android_baselineAligned 2
int styleable LinearLayoutCompat_android_baselineAlignedChildIndex 3
int styleable LinearLayoutCompat_android_weightSum 4
int styleable LinearLayoutCompat_divider 5
int styleable LinearLayoutCompat_dividerPadding 6
int styleable LinearLayoutCompat_measureWithLargestChild 7
int styleable LinearLayoutCompat_showDividers 8
int[] styleable LinearLayoutCompat_Layout { 0x010100b3, 0x010100f4, 0x010100f5, 0x01010181 }
int styleable LinearLayoutCompat_Layout_android_layout_gravity 0
int styleable LinearLayoutCompat_Layout_android_layout_width 1
int styleable LinearLayoutCompat_Layout_android_layout_height 2
int styleable LinearLayoutCompat_Layout_android_layout_weight 3
int[] styleable LinearProgressIndicator { 0x7f030284, 0x7f030288, 0x7f03055d }
int styleable LinearProgressIndicator_indeterminateAnimationType 0
int styleable LinearProgressIndicator_indicatorDirectionLinear 1
int styleable LinearProgressIndicator_trackStopIndicatorSize 2
int[] styleable ListPopupWindow { 0x010102ac, 0x010102ad }
int styleable ListPopupWindow_android_dropDownHorizontalOffset 0
int styleable ListPopupWindow_android_dropDownVerticalOffset 1
int[] styleable ListPreference { 0x010100b2, 0x010101f8, 0x7f0301e3, 0x7f0301e4, 0x7f030579 }
int styleable ListPreference_android_entries 0
int styleable ListPreference_android_entryValues 1
int styleable ListPreference_entries 2
int styleable ListPreference_entryValues 3
int styleable ListPreference_useSimpleSummaryProvider 4
int[] styleable LoadingImageView { 0x7f0300f1, 0x7f03027d, 0x7f03027e }
int styleable LoadingImageView_circleCrop 0
int styleable LoadingImageView_imageAspectRatio 1
int styleable LoadingImageView_imageAspectRatioAdjust 2
int[] styleable MapAttrs { 0x7f030039, 0x7f030054, 0x7f0300ad, 0x7f0300ae, 0x7f0300af, 0x7f0300b0, 0x7f0300b1, 0x7f0300b2, 0x7f0300b3, 0x7f0302bd, 0x7f0302be, 0x7f0302bf, 0x7f0302c0, 0x7f030321, 0x7f030326, 0x7f030327, 0x7f03056b, 0x7f03056c, 0x7f03056d, 0x7f03056e, 0x7f03056f, 0x7f030570, 0x7f030571, 0x7f030572, 0x7f03057a, 0x7f03059b }
int styleable MapAttrs_ambientEnabled 0
int styleable MapAttrs_backgroundColor 1
int styleable MapAttrs_cameraBearing 2
int styleable MapAttrs_cameraMaxZoomPreference 3
int styleable MapAttrs_cameraMinZoomPreference 4
int styleable MapAttrs_cameraTargetLat 5
int styleable MapAttrs_cameraTargetLng 6
int styleable MapAttrs_cameraTilt 7
int styleable MapAttrs_cameraZoom 8
int styleable MapAttrs_latLngBoundsNorthEastLatitude 9
int styleable MapAttrs_latLngBoundsNorthEastLongitude 10
int styleable MapAttrs_latLngBoundsSouthWestLatitude 11
int styleable MapAttrs_latLngBoundsSouthWestLongitude 12
int styleable MapAttrs_liteMode 13
int styleable MapAttrs_mapId 14
int styleable MapAttrs_mapType 15
int styleable MapAttrs_uiCompass 16
int styleable MapAttrs_uiMapToolbar 17
int styleable MapAttrs_uiRotateGestures 18
int styleable MapAttrs_uiScrollGestures 19
int styleable MapAttrs_uiScrollGesturesDuringRotateOrZoom 20
int styleable MapAttrs_uiTiltGestures 21
int styleable MapAttrs_uiZoomControls 22
int styleable MapAttrs_uiZoomGestures 23
int styleable MapAttrs_useViewLifecycle 24
int styleable MapAttrs_zOrderOnTop 25
int[] styleable MaterialAlertDialog { 0x7f030056, 0x7f030057, 0x7f030058, 0x7f030059, 0x7f03005d }
int styleable MaterialAlertDialog_backgroundInsetBottom 0
int styleable MaterialAlertDialog_backgroundInsetEnd 1
int styleable MaterialAlertDialog_backgroundInsetStart 2
int styleable MaterialAlertDialog_backgroundInsetTop 3
int styleable MaterialAlertDialog_backgroundTint 4
int[] styleable MaterialAlertDialogTheme { 0x7f030333, 0x7f030334, 0x7f030335, 0x7f030336, 0x7f030337, 0x7f030338 }
int styleable MaterialAlertDialogTheme_materialAlertDialogBodyTextStyle 0
int styleable MaterialAlertDialogTheme_materialAlertDialogButtonSpacerVisibility 1
int styleable MaterialAlertDialogTheme_materialAlertDialogTheme 2
int styleable MaterialAlertDialogTheme_materialAlertDialogTitleIconStyle 3
int styleable MaterialAlertDialogTheme_materialAlertDialogTitlePanelStyle 4
int styleable MaterialAlertDialogTheme_materialAlertDialogTitleTextStyle 5
int[] styleable MaterialAutoCompleteTextView { 0x01010220, 0x0101048c, 0x7f0301c5, 0x7f030456, 0x7f030457, 0x7f030458, 0x7f030459 }
int styleable MaterialAutoCompleteTextView_android_inputType 0
int styleable MaterialAutoCompleteTextView_android_popupElevation 1
int styleable MaterialAutoCompleteTextView_dropDownBackgroundTint 2
int styleable MaterialAutoCompleteTextView_simpleItemLayout 3
int styleable MaterialAutoCompleteTextView_simpleItemSelectedColor 4
int styleable MaterialAutoCompleteTextView_simpleItemSelectedRippleColor 5
int styleable MaterialAutoCompleteTextView_simpleItems 6
int[] styleable MaterialButton { 0x010100d4, 0x010101b7, 0x010101b8, 0x010101b9, 0x010101ba, 0x010101e5, 0x7f03005d, 0x7f03005e, 0x7f030174, 0x7f0301cf, 0x7f030271, 0x7f030273, 0x7f030274, 0x7f030275, 0x7f030278, 0x7f030279, 0x7f03041e, 0x7f030438, 0x7f030440, 0x7f030498, 0x7f030499, 0x7f030545 }
int styleable MaterialButton_android_background 0
int styleable MaterialButton_android_insetLeft 1
int styleable MaterialButton_android_insetRight 2
int styleable MaterialButton_android_insetTop 3
int styleable MaterialButton_android_insetBottom 4
int styleable MaterialButton_android_checkable 5
int styleable MaterialButton_backgroundTint 6
int styleable MaterialButton_backgroundTintMode 7
int styleable MaterialButton_cornerRadius 8
int styleable MaterialButton_elevation 9
int styleable MaterialButton_icon 10
int styleable MaterialButton_iconGravity 11
int styleable MaterialButton_iconPadding 12
int styleable MaterialButton_iconSize 13
int styleable MaterialButton_iconTint 14
int styleable MaterialButton_iconTintMode 15
int styleable MaterialButton_rippleColor 16
int styleable MaterialButton_shapeAppearance 17
int styleable MaterialButton_shapeAppearanceOverlay 18
int styleable MaterialButton_strokeColor 19
int styleable MaterialButton_strokeWidth 20
int styleable MaterialButton_toggleCheckedStateOnClick 21
int[] styleable MaterialButtonToggleGroup { 0x0101000e, 0x7f0300d2, 0x7f030435, 0x7f03045d }
int styleable MaterialButtonToggleGroup_android_enabled 0
int styleable MaterialButtonToggleGroup_checkedButton 1
int styleable MaterialButtonToggleGroup_selectionRequired 2
int styleable MaterialButtonToggleGroup_singleSelection 3
int[] styleable MaterialCalendar { 0x0101020d, 0x7f03005d, 0x7f030194, 0x7f030195, 0x7f030196, 0x7f030197, 0x7f0303b7, 0x7f03040e, 0x7f030598, 0x7f030599, 0x7f03059a }
int styleable MaterialCalendar_android_windowFullscreen 0
int styleable MaterialCalendar_backgroundTint 1
int styleable MaterialCalendar_dayInvalidStyle 2
int styleable MaterialCalendar_daySelectedStyle 3
int styleable MaterialCalendar_dayStyle 4
int styleable MaterialCalendar_dayTodayStyle 5
int styleable MaterialCalendar_nestedScrollable 6
int styleable MaterialCalendar_rangeFillColor 7
int styleable MaterialCalendar_yearSelectedStyle 8
int styleable MaterialCalendar_yearStyle 9
int styleable MaterialCalendar_yearTodayStyle 10
int[] styleable MaterialCalendarItem { 0x010101b7, 0x010101b8, 0x010101b9, 0x010101ba, 0x7f030296, 0x7f0302a2, 0x7f0302a3, 0x7f0302aa, 0x7f0302ab, 0x7f0302b0 }
int styleable MaterialCalendarItem_android_insetLeft 0
int styleable MaterialCalendarItem_android_insetRight 1
int styleable MaterialCalendarItem_android_insetTop 2
int styleable MaterialCalendarItem_android_insetBottom 3
int styleable MaterialCalendarItem_itemFillColor 4
int styleable MaterialCalendarItem_itemShapeAppearance 5
int styleable MaterialCalendarItem_itemShapeAppearanceOverlay 6
int styleable MaterialCalendarItem_itemStrokeColor 7
int styleable MaterialCalendarItem_itemStrokeWidth 8
int styleable MaterialCalendarItem_itemTextColor 9
int[] styleable MaterialCardView { 0x010101e5, 0x7f0300b7, 0x7f0300d4, 0x7f0300d6, 0x7f0300d7, 0x7f0300d8, 0x7f0300d9, 0x7f03041e, 0x7f030438, 0x7f030440, 0x7f030483, 0x7f030498, 0x7f030499 }
int styleable MaterialCardView_android_checkable 0
int styleable MaterialCardView_cardForegroundColor 1
int styleable MaterialCardView_checkedIcon 2
int styleable MaterialCardView_checkedIconGravity 3
int styleable MaterialCardView_checkedIconMargin 4
int styleable MaterialCardView_checkedIconSize 5
int styleable MaterialCardView_checkedIconTint 6
int styleable MaterialCardView_rippleColor 7
int styleable MaterialCardView_shapeAppearance 8
int styleable MaterialCardView_shapeAppearanceOverlay 9
int styleable MaterialCardView_state_dragged 10
int styleable MaterialCardView_strokeColor 11
int styleable MaterialCardView_strokeWidth 12
int[] styleable MaterialCheckBox { 0x01010107, 0x7f03009c, 0x7f03009e, 0x7f0300a0, 0x7f0300a1, 0x7f0300a7, 0x7f0300cb, 0x7f0300db, 0x7f0301e6, 0x7f0301ed, 0x7f030578 }
int styleable MaterialCheckBox_android_button 0
int styleable MaterialCheckBox_buttonCompat 1
int styleable MaterialCheckBox_buttonIcon 2
int styleable MaterialCheckBox_buttonIconTint 3
int styleable MaterialCheckBox_buttonIconTintMode 4
int styleable MaterialCheckBox_buttonTint 5
int styleable MaterialCheckBox_centerIfNoTextEnabled 6
int styleable MaterialCheckBox_checkedState 7
int styleable MaterialCheckBox_errorAccessibilityLabel 8
int styleable MaterialCheckBox_errorShown 9
int styleable MaterialCheckBox_useMaterialThemeColors 10
int[] styleable MaterialCheckBoxStates { 0x7f030484, 0x7f030485 }
int styleable MaterialCheckBoxStates_state_error 0
int styleable MaterialCheckBoxStates_state_indeterminate 1
int[] styleable MaterialDivider { 0x7f0301ae, 0x7f0301b0, 0x7f0301b1, 0x7f0301b3, 0x7f0302bc }
int styleable MaterialDivider_dividerColor 0
int styleable MaterialDivider_dividerInsetEnd 1
int styleable MaterialDivider_dividerInsetStart 2
int styleable MaterialDivider_dividerThickness 3
int styleable MaterialDivider_lastItemDecorated 4
int[] styleable MaterialRadioButton { 0x7f0300a7, 0x7f030578 }
int styleable MaterialRadioButton_buttonTint 0
int styleable MaterialRadioButton_useMaterialThemeColors 1
int[] styleable MaterialShape { 0x7f030438, 0x7f030440 }
int styleable MaterialShape_shapeAppearance 0
int styleable MaterialShape_shapeAppearanceOverlay 1
int[] styleable MaterialSwitch { 0x7f03051e, 0x7f03051f, 0x7f030520, 0x7f030521, 0x7f030558, 0x7f030559, 0x7f03055a }
int styleable MaterialSwitch_thumbIcon 0
int styleable MaterialSwitch_thumbIconSize 1
int styleable MaterialSwitch_thumbIconTint 2
int styleable MaterialSwitch_thumbIconTintMode 3
int styleable MaterialSwitch_trackDecoration 4
int styleable MaterialSwitch_trackDecorationTint 5
int styleable MaterialSwitch_trackDecorationTintMode 6
int[] styleable MaterialTextAppearance { 0x010104b6, 0x0101057f, 0x7f03030f }
int styleable MaterialTextAppearance_android_letterSpacing 0
int styleable MaterialTextAppearance_android_lineHeight 1
int styleable MaterialTextAppearance_lineHeight 2
int[] styleable MaterialTextView { 0x01010034, 0x0101057f, 0x7f03030f }
int styleable MaterialTextView_android_textAppearance 0
int styleable MaterialTextView_android_lineHeight 1
int styleable MaterialTextView_lineHeight 2
int[] styleable MaterialTimePicker { 0x7f03005d, 0x7f0300fe, 0x7f0302b4 }
int styleable MaterialTimePicker_backgroundTint 0
int styleable MaterialTimePicker_clockIcon 1
int styleable MaterialTimePicker_keyboardIcon 2
int[] styleable MaterialToolbar { 0x7f030323, 0x7f030325, 0x7f0303b0, 0x7f0304a1, 0x7f030537 }
int styleable MaterialToolbar_logoAdjustViewBounds 0
int styleable MaterialToolbar_logoScaleType 1
int styleable MaterialToolbar_navigationIconTint 2
int styleable MaterialToolbar_subtitleCentered 3
int styleable MaterialToolbar_titleCentered 4
int[] styleable MenuGroup { 0x0101000e, 0x010100d0, 0x01010194, 0x010101de, 0x010101df, 0x010101e0 }
int styleable MenuGroup_android_enabled 0
int styleable MenuGroup_android_id 1
int styleable MenuGroup_android_visible 2
int styleable MenuGroup_android_menuCategory 3
int styleable MenuGroup_android_orderInCategory 4
int styleable MenuGroup_android_checkableBehavior 5
int[] styleable MenuItem { 0x01010002, 0x0101000e, 0x010100d0, 0x01010106, 0x01010194, 0x010101de, 0x010101df, 0x010101e1, 0x010101e2, 0x010101e3, 0x010101e4, 0x010101e5, 0x0101026f, 0x7f030010, 0x7f030024, 0x7f030026, 0x7f030036, 0x7f03015c, 0x7f030278, 0x7f030279, 0x7f0303ba, 0x7f03044a, 0x7f03054e }
int styleable MenuItem_android_icon 0
int styleable MenuItem_android_enabled 1
int styleable MenuItem_android_id 2
int styleable MenuItem_android_checked 3
int styleable MenuItem_android_visible 4
int styleable MenuItem_android_menuCategory 5
int styleable MenuItem_android_orderInCategory 6
int styleable MenuItem_android_title 7
int styleable MenuItem_android_titleCondensed 8
int styleable MenuItem_android_alphabeticShortcut 9
int styleable MenuItem_android_numericShortcut 10
int styleable MenuItem_android_checkable 11
int styleable MenuItem_android_onClick 12
int styleable MenuItem_actionLayout 13
int styleable MenuItem_actionProviderClass 14
int styleable MenuItem_actionViewClass 15
int styleable MenuItem_alphabeticModifiers 16
int styleable MenuItem_contentDescription 17
int styleable MenuItem_iconTint 18
int styleable MenuItem_iconTintMode 19
int styleable MenuItem_numericModifiers 20
int styleable MenuItem_showAsAction 21
int styleable MenuItem_tooltipText 22
int[] styleable MenuView { 0x010100ae, 0x0101012c, 0x0101012d, 0x0101012e, 0x0101012f, 0x01010130, 0x01010131, 0x7f030402, 0x7f03049a }
int styleable MenuView_android_windowAnimationStyle 0
int styleable MenuView_android_itemTextAppearance 1
int styleable MenuView_android_horizontalDivider 2
int styleable MenuView_android_verticalDivider 3
int styleable MenuView_android_headerBackground 4
int styleable MenuView_android_itemBackground 5
int styleable MenuView_android_itemIconDisabledAlpha 6
int styleable MenuView_preserveIconSpacing 7
int styleable MenuView_subMenuArrow 8
int[] styleable MockView { 0x7f030378, 0x7f030379, 0x7f03037a, 0x7f03037b, 0x7f03037c, 0x7f03037d }
int styleable MockView_mock_diagonalsColor 0
int styleable MockView_mock_label 1
int styleable MockView_mock_labelBackgroundColor 2
int styleable MockView_mock_labelColor 3
int styleable MockView_mock_showDiagonals 4
int styleable MockView_mock_showLabel 5
int[] styleable Motion { 0x7f03003a, 0x7f03003d, 0x7f0301b8, 0x7f0303a5, 0x7f0303a7, 0x7f0303d8, 0x7f030407, 0x7f030408, 0x7f030409, 0x7f030563 }
int styleable Motion_animateCircleAngleTo 0
int styleable Motion_animateRelativeTo 1
int styleable Motion_drawPath 2
int styleable Motion_motionPathRotate 3
int styleable Motion_motionStagger 4
int styleable Motion_pathMotionArc 5
int styleable Motion_quantizeMotionInterpolator 6
int styleable Motion_quantizeMotionPhase 7
int styleable Motion_quantizeMotionSteps 8
int styleable Motion_transitionEasing 9
int[] styleable MotionEffect { 0x7f03039b, 0x7f03039c, 0x7f03039d, 0x7f03039e, 0x7f03039f, 0x7f0303a0, 0x7f0303a1, 0x7f0303a2 }
int styleable MotionEffect_motionEffect_alpha 0
int styleable MotionEffect_motionEffect_end 1
int styleable MotionEffect_motionEffect_move 2
int styleable MotionEffect_motionEffect_start 3
int styleable MotionEffect_motionEffect_strict 4
int styleable MotionEffect_motionEffect_translationX 5
int styleable MotionEffect_motionEffect_translationY 6
int styleable MotionEffect_motionEffect_viewTransition 7
int[] styleable MotionHelper { 0x7f0303bd, 0x7f0303c0 }
int styleable MotionHelper_onHide 0
int styleable MotionHelper_onShow 1
int[] styleable MotionLabel { 0x01010095, 0x01010096, 0x01010097, 0x01010098, 0x010100af, 0x0101014f, 0x01010164, 0x010103ac, 0x01010535, 0x7f030081, 0x7f030082, 0x7f030424, 0x7f0304fe, 0x7f0304ff, 0x7f030500, 0x7f030501, 0x7f030502, 0x7f030510, 0x7f030511, 0x7f030512, 0x7f030513, 0x7f030515, 0x7f030516, 0x7f030517, 0x7f030518 }
int styleable MotionLabel_android_textSize 0
int styleable MotionLabel_android_typeface 1
int styleable MotionLabel_android_textStyle 2
int styleable MotionLabel_android_textColor 3
int styleable MotionLabel_android_gravity 4
int styleable MotionLabel_android_text 5
int styleable MotionLabel_android_shadowRadius 6
int styleable MotionLabel_android_fontFamily 7
int styleable MotionLabel_android_autoSizeTextType 8
int styleable MotionLabel_borderRound 9
int styleable MotionLabel_borderRoundPercent 10
int styleable MotionLabel_scaleFromTextSize 11
int styleable MotionLabel_textBackground 12
int styleable MotionLabel_textBackgroundPanX 13
int styleable MotionLabel_textBackgroundPanY 14
int styleable MotionLabel_textBackgroundRotate 15
int styleable MotionLabel_textBackgroundZoom 16
int styleable MotionLabel_textOutlineColor 17
int styleable MotionLabel_textOutlineThickness 18
int styleable MotionLabel_textPanX 19
int styleable MotionLabel_textPanY 20
int styleable MotionLabel_textureBlurFactor 21
int styleable MotionLabel_textureEffect 22
int styleable MotionLabel_textureHeight 23
int styleable MotionLabel_textureWidth 24
int[] styleable MotionLayout { 0x7f030042, 0x7f030183, 0x7f0302c3, 0x7f03037e, 0x7f0303a6, 0x7f03044f }
int styleable MotionLayout_applyMotionScene 0
int styleable MotionLayout_currentState 1
int styleable MotionLayout_layoutDescription 2
int styleable MotionLayout_motionDebug 3
int styleable MotionLayout_motionProgress 4
int styleable MotionLayout_showPaths 5
int[] styleable MotionScene { 0x7f030198, 0x7f0302c4 }
int styleable MotionScene_defaultDuration 0
int styleable MotionScene_layoutDuringTransition 1
int[] styleable MotionTelltales { 0x7f0304d5, 0x7f0304d6, 0x7f0304d7 }
int styleable MotionTelltales_telltales_tailColor 0
int styleable MotionTelltales_telltales_tailScale 1
int styleable MotionTelltales_telltales_velocityMode 2
int[] styleable MultiSelectListPreference { 0x010100b2, 0x010101f8, 0x7f0301e3, 0x7f0301e4 }
int styleable MultiSelectListPreference_android_entries 0
int styleable MultiSelectListPreference_android_entryValues 1
int styleable MultiSelectListPreference_entries 2
int styleable MultiSelectListPreference_entryValues 3
int[] styleable NavAction { 0x010100d0, 0x7f0301a2, 0x7f0301e2, 0x7f0301f0, 0x7f0302c1, 0x7f0303eb, 0x7f0303ec, 0x7f0303ed, 0x7f0303ee, 0x7f0303ef, 0x7f03041c }
int styleable NavAction_android_id 0
int styleable NavAction_destination 1
int styleable NavAction_enterAnim 2
int styleable NavAction_exitAnim 3
int styleable NavAction_launchSingleTop 4
int styleable NavAction_popEnterAnim 5
int styleable NavAction_popExitAnim 6
int styleable NavAction_popUpTo 7
int styleable NavAction_popUpToInclusive 8
int styleable NavAction_popUpToSaveState 9
int styleable NavAction_restoreState 10
int[] styleable NavArgument { 0x01010003, 0x010101ed, 0x7f030044, 0x7f0303b8 }
int styleable NavArgument_android_name 0
int styleable NavArgument_android_defaultValue 1
int styleable NavArgument_argType 2
int styleable NavArgument_nullable 3
int[] styleable NavDeepLink { 0x010104ee, 0x7f030002, 0x7f030371, 0x7f030575 }
int styleable NavDeepLink_android_autoVerify 0
int styleable NavDeepLink_action 1
int styleable NavDeepLink_mimeType 2
int styleable NavDeepLink_uri 3
int[] styleable NavGraphNavigator { 0x7f030477 }
int styleable NavGraphNavigator_startDestination 0
int[] styleable NavHost { 0x7f0303ad }
int styleable NavHost_navGraph 0
int[] styleable NavInclude { 0x7f03024e }
int styleable NavInclude_graph 0
int[] styleable NavigationBarActiveIndicator { 0x01010155, 0x01010159, 0x010101a5, 0x7f030328, 0x7f030438 }
int styleable NavigationBarActiveIndicator_android_height 0
int styleable NavigationBarActiveIndicator_android_width 1
int styleable NavigationBarActiveIndicator_android_color 2
int styleable NavigationBarActiveIndicator_marginHorizontal 3
int styleable NavigationBarActiveIndicator_shapeAppearance 4
int[] styleable NavigationBarView { 0x7f030027, 0x7f03005d, 0x7f0301cf, 0x7f030294, 0x7f030295, 0x7f03029a, 0x7f03029b, 0x7f03029f, 0x7f0302a0, 0x7f0302a1, 0x7f0302ad, 0x7f0302ae, 0x7f0302af, 0x7f0302b0, 0x7f0302b9, 0x7f03036d }
int styleable NavigationBarView_activeIndicatorLabelPadding 0
int styleable NavigationBarView_backgroundTint 1
int styleable NavigationBarView_elevation 2
int styleable NavigationBarView_itemActiveIndicatorStyle 3
int styleable NavigationBarView_itemBackground 4
int styleable NavigationBarView_itemIconSize 5
int styleable NavigationBarView_itemIconTint 6
int styleable NavigationBarView_itemPaddingBottom 7
int styleable NavigationBarView_itemPaddingTop 8
int styleable NavigationBarView_itemRippleColor 9
int styleable NavigationBarView_itemTextAppearanceActive 10
int styleable NavigationBarView_itemTextAppearanceActiveBoldEnabled 11
int styleable NavigationBarView_itemTextAppearanceInactive 12
int styleable NavigationBarView_itemTextColor 13
int styleable NavigationBarView_labelVisibilityMode 14
int styleable NavigationBarView_menu 15
int[] styleable NavigationRailView { 0x7f03025d, 0x7f03029d, 0x7f03036f, 0x7f0303c8, 0x7f0303cd, 0x7f0303cf, 0x7f030438, 0x7f030440 }
int styleable NavigationRailView_headerLayout 0
int styleable NavigationRailView_itemMinHeight 1
int styleable NavigationRailView_menuGravity 2
int styleable NavigationRailView_paddingBottomSystemWindowInsets 3
int styleable NavigationRailView_paddingStartSystemWindowInsets 4
int styleable NavigationRailView_paddingTopSystemWindowInsets 5
int styleable NavigationRailView_shapeAppearance 6
int styleable NavigationRailView_shapeAppearanceOverlay 7
int[] styleable NavigationView { 0x010100b3, 0x010100d4, 0x010100dd, 0x0101011f, 0x7f030086, 0x7f0301b0, 0x7f0301b1, 0x7f0301c3, 0x7f0301cf, 0x7f03025d, 0x7f030295, 0x7f030297, 0x7f030299, 0x7f03029a, 0x7f03029b, 0x7f03029c, 0x7f0302a1, 0x7f0302a2, 0x7f0302a3, 0x7f0302a4, 0x7f0302a5, 0x7f0302a6, 0x7f0302a7, 0x7f0302a8, 0x7f0302ac, 0x7f0302ae, 0x7f0302b0, 0x7f0302b1, 0x7f03036d, 0x7f030438, 0x7f030440, 0x7f03049b, 0x7f03049c, 0x7f03049d, 0x7f03049e, 0x7f03054f }
int styleable NavigationView_android_layout_gravity 0
int styleable NavigationView_android_background 1
int styleable NavigationView_android_fitsSystemWindows 2
int styleable NavigationView_android_maxWidth 3
int styleable NavigationView_bottomInsetScrimEnabled 4
int styleable NavigationView_dividerInsetEnd 5
int styleable NavigationView_dividerInsetStart 6
int styleable NavigationView_drawerLayoutCornerSize 7
int styleable NavigationView_elevation 8
int styleable NavigationView_headerLayout 9
int styleable NavigationView_itemBackground 10
int styleable NavigationView_itemHorizontalPadding 11
int styleable NavigationView_itemIconPadding 12
int styleable NavigationView_itemIconSize 13
int styleable NavigationView_itemIconTint 14
int styleable NavigationView_itemMaxLines 15
int styleable NavigationView_itemRippleColor 16
int styleable NavigationView_itemShapeAppearance 17
int styleable NavigationView_itemShapeAppearanceOverlay 18
int styleable NavigationView_itemShapeFillColor 19
int styleable NavigationView_itemShapeInsetBottom 20
int styleable NavigationView_itemShapeInsetEnd 21
int styleable NavigationView_itemShapeInsetStart 22
int styleable NavigationView_itemShapeInsetTop 23
int styleable NavigationView_itemTextAppearance 24
int styleable NavigationView_itemTextAppearanceActiveBoldEnabled 25
int styleable NavigationView_itemTextColor 26
int styleable NavigationView_itemVerticalPadding 27
int styleable NavigationView_menu 28
int styleable NavigationView_shapeAppearance 29
int styleable NavigationView_shapeAppearanceOverlay 30
int styleable NavigationView_subheaderColor 31
int styleable NavigationView_subheaderInsetEnd 32
int styleable NavigationView_subheaderInsetStart 33
int styleable NavigationView_subheaderTextAppearance 34
int styleable NavigationView_topInsetScrimEnabled 35
int[] styleable Navigator { 0x01010001, 0x010100d0, 0x7f030422 }
int styleable Navigator_android_label 0
int styleable Navigator_android_id 1
int styleable Navigator_route 2
int[] styleable OnClick { 0x7f0300fb, 0x7f0304d3 }
int styleable OnClick_clickAction 0
int styleable OnClick_targetId 1
int[] styleable OnSwipe { 0x7f030049, 0x7f0301b5, 0x7f0301b6, 0x7f0301b7, 0x7f03030e, 0x7f030362, 0x7f03036a, 0x7f0303ab, 0x7f0303b5, 0x7f0303c2, 0x7f03041f, 0x7f03046f, 0x7f030470, 0x7f030471, 0x7f030472, 0x7f030473, 0x7f030550, 0x7f030551, 0x7f030552 }
int styleable OnSwipe_autoCompleteMode 0
int styleable OnSwipe_dragDirection 1
int styleable OnSwipe_dragScale 2
int styleable OnSwipe_dragThreshold 3
int styleable OnSwipe_limitBoundsTo 4
int styleable OnSwipe_maxAcceleration 5
int styleable OnSwipe_maxVelocity 6
int styleable OnSwipe_moveWhenScrollAtTop 7
int styleable OnSwipe_nestedScrollFlags 8
int styleable OnSwipe_onTouchUp 9
int styleable OnSwipe_rotationCenterId 10
int styleable OnSwipe_springBoundary 11
int styleable OnSwipe_springDamping 12
int styleable OnSwipe_springMass 13
int styleable OnSwipe_springStiffness 14
int styleable OnSwipe_springStopThreshold 15
int styleable OnSwipe_touchAnchorId 16
int styleable OnSwipe_touchAnchorSide 17
int styleable OnSwipe_touchRegionId 18
int[] styleable PayButtonAttributes { 0x7f0300a6, 0x7f030174 }
int styleable PayButtonAttributes_buttonTheme 0
int styleable PayButtonAttributes_cornerRadius 1
int[] styleable PopupWindow { 0x01010176, 0x010102c9, 0x7f0303c5 }
int styleable PopupWindow_android_popupBackground 0
int styleable PopupWindow_android_popupAnimationStyle 1
int styleable PopupWindow_overlapAnchor 2
int[] styleable PopupWindowBackgroundState { 0x7f030480 }
int styleable PopupWindowBackgroundState_state_above_anchor 0
int[] styleable Preference { 0x01010002, 0x0101000d, 0x0101000e, 0x010100f2, 0x010101e1, 0x010101e6, 0x010101e8, 0x010101e9, 0x010101ea, 0x010101eb, 0x010101ec, 0x010101ed, 0x010101ee, 0x010102e3, 0x0101055c, 0x01010561, 0x7f030031, 0x7f030033, 0x7f03019d, 0x7f0301a0, 0x7f0301d4, 0x7f0301d6, 0x7f030247, 0x7f030271, 0x7f030276, 0x7f030293, 0x7f0302b2, 0x7f0302c2, 0x7f0303c3, 0x7f0303e3, 0x7f030432, 0x7f030444, 0x7f03045c, 0x7f0304a9, 0x7f030536, 0x7f03058c }
int styleable Preference_android_icon 0
int styleable Preference_android_persistent 1
int styleable Preference_android_enabled 2
int styleable Preference_android_layout 3
int styleable Preference_android_title 4
int styleable Preference_android_selectable 5
int styleable Preference_android_key 6
int styleable Preference_android_summary 7
int styleable Preference_android_order 8
int styleable Preference_android_widgetLayout 9
int styleable Preference_android_dependency 10
int styleable Preference_android_defaultValue 11
int styleable Preference_android_shouldDisableView 12
int styleable Preference_android_fragment 13
int styleable Preference_android_singleLineTitle 14
int styleable Preference_android_iconSpaceReserved 15
int styleable Preference_allowDividerAbove 16
int styleable Preference_allowDividerBelow 17
int styleable Preference_defaultValue 18
int styleable Preference_dependency 19
int styleable Preference_enableCopying 20
int styleable Preference_enabled 21
int styleable Preference_fragment 22
int styleable Preference_icon 23
int styleable Preference_iconSpaceReserved 24
int styleable Preference_isPreferenceVisible 25
int styleable Preference_key 26
int styleable Preference_layout 27
int styleable Preference_order 28
int styleable Preference_persistent 29
int styleable Preference_selectable 30
int styleable Preference_shouldDisableView 31
int styleable Preference_singleLineTitle 32
int styleable Preference_summary 33
int styleable Preference_title 34
int styleable Preference_widgetLayout 35
int[] styleable PreferenceFragment { 0x010100f2, 0x01010129, 0x0101012a, 0x7f030032 }
int styleable PreferenceFragment_android_layout 0
int styleable PreferenceFragment_android_divider 1
int styleable PreferenceFragment_android_dividerHeight 2
int styleable PreferenceFragment_allowDividerAfterLastItem 3
int[] styleable PreferenceFragmentCompat { 0x010100f2, 0x01010129, 0x0101012a, 0x7f030032 }
int styleable PreferenceFragmentCompat_android_layout 0
int styleable PreferenceFragmentCompat_android_divider 1
int styleable PreferenceFragmentCompat_android_dividerHeight 2
int styleable PreferenceFragmentCompat_allowDividerAfterLastItem 3
int[] styleable PreferenceGroup { 0x010101e7, 0x7f03028d, 0x7f0303c4 }
int styleable PreferenceGroup_android_orderingFromXml 0
int styleable PreferenceGroup_initialExpandedChildrenCount 1
int styleable PreferenceGroup_orderingFromXml 2
int[] styleable PreferenceImageView { 0x0101011f, 0x01010120, 0x7f030366, 0x7f03036b }
int styleable PreferenceImageView_android_maxWidth 0
int styleable PreferenceImageView_android_maxHeight 1
int styleable PreferenceImageView_maxHeight 2
int styleable PreferenceImageView_maxWidth 3
int[] styleable PreferenceTheme { 0x7f0300cd, 0x7f0301a7, 0x7f0301c8, 0x7f0301cd, 0x7f0303f5, 0x7f0303f6, 0x7f0303f7, 0x7f0303f8, 0x7f0303f9, 0x7f0303fa, 0x7f0303fb, 0x7f0303fc, 0x7f0303fd, 0x7f0303fe, 0x7f030430, 0x7f0304af, 0x7f0304b0 }
int styleable PreferenceTheme_checkBoxPreferenceStyle 0
int styleable PreferenceTheme_dialogPreferenceStyle 1
int styleable PreferenceTheme_dropdownPreferenceStyle 2
int styleable PreferenceTheme_editTextPreferenceStyle 3
int styleable PreferenceTheme_preferenceCategoryStyle 4
int styleable PreferenceTheme_preferenceCategoryTitleTextAppearance 5
int styleable PreferenceTheme_preferenceCategoryTitleTextColor 6
int styleable PreferenceTheme_preferenceFragmentCompatStyle 7
int styleable PreferenceTheme_preferenceFragmentListStyle 8
int styleable PreferenceTheme_preferenceFragmentStyle 9
int styleable PreferenceTheme_preferenceInformationStyle 10
int styleable PreferenceTheme_preferenceScreenStyle 11
int styleable PreferenceTheme_preferenceStyle 12
int styleable PreferenceTheme_preferenceTheme 13
int styleable PreferenceTheme_seekBarPreferenceStyle 14
int styleable PreferenceTheme_switchPreferenceCompatStyle 15
int styleable PreferenceTheme_switchPreferenceStyle 16
int[] styleable PropertySet { 0x010100dc, 0x0101031f, 0x7f0302ed, 0x7f0303a6, 0x7f030583 }
int styleable PropertySet_android_visibility 0
int styleable PropertySet_android_alpha 1
int styleable PropertySet_layout_constraintTag 2
int styleable PropertySet_motionProgress 3
int styleable PropertySet_visibilityMode 4
int[] styleable RadialViewGroup { 0x7f03034f }
int styleable RadialViewGroup_materialCircleRadius 0
int[] styleable RangeSlider { 0x7f030375, 0x7f03057b }
int styleable RangeSlider_minSeparation 0
int styleable RangeSlider_values 1
int[] styleable RecycleListView { 0x7f0303c7, 0x7f0303ce }
int styleable RecycleListView_paddingBottomNoButtons 0
int styleable RecycleListView_paddingTopNoTitle 1
int[] styleable RecyclerView { 0x010100c4, 0x010100eb, 0x010100f1, 0x7f03020d, 0x7f03020e, 0x7f03020f, 0x7f030210, 0x7f030211, 0x7f0302c5, 0x7f03041d, 0x7f030463, 0x7f030475 }
int styleable RecyclerView_android_orientation 0
int styleable RecyclerView_android_clipToPadding 1
int styleable RecyclerView_android_descendantFocusability 2
int styleable RecyclerView_fastScrollEnabled 3
int styleable RecyclerView_fastScrollHorizontalThumbDrawable 4
int styleable RecyclerView_fastScrollHorizontalTrackDrawable 5
int styleable RecyclerView_fastScrollVerticalThumbDrawable 6
int styleable RecyclerView_fastScrollVerticalTrackDrawable 7
int styleable RecyclerView_layoutManager 8
int styleable RecyclerView_reverseLayout 9
int styleable RecyclerView_spanCount 10
int styleable RecyclerView_stackFromEnd 11
int[] styleable ScrimInsetsFrameLayout { 0x7f03028e }
int styleable ScrimInsetsFrameLayout_insetForeground 0
int[] styleable ScrollingViewBehavior_Layout { 0x7f03007b }
int styleable ScrollingViewBehavior_Layout_behavior_overlapTop 0
int[] styleable SearchBar { 0x01010034, 0x0101014f, 0x01010150, 0x7f03005d, 0x7f030199, 0x7f03019b, 0x7f0301cf, 0x7f030245, 0x7f030265, 0x7f0303b0, 0x7f030498, 0x7f030499, 0x7f030535 }
int styleable SearchBar_android_textAppearance 0
int styleable SearchBar_android_text 1
int styleable SearchBar_android_hint 2
int styleable SearchBar_backgroundTint 3
int styleable SearchBar_defaultMarginsEnabled 4
int styleable SearchBar_defaultScrollFlagsEnabled 5
int styleable SearchBar_elevation 6
int styleable SearchBar_forceDefaultNavigationOnClickListener 7
int styleable SearchBar_hideNavigationIcon 8
int styleable SearchBar_navigationIconTint 9
int styleable SearchBar_strokeColor 10
int styleable SearchBar_strokeWidth 11
int styleable SearchBar_tintNavigationIcon 12
int[] styleable SearchView { 0x01010034, 0x010100da, 0x0101011f, 0x0101014f, 0x01010150, 0x01010220, 0x01010264, 0x7f03003b, 0x7f03003c, 0x7f03004b, 0x7f030052, 0x7f03005d, 0x7f030100, 0x7f030151, 0x7f03019a, 0x7f03024d, 0x7f03025d, 0x7f030265, 0x7f03027a, 0x7f0302c2, 0x7f03040a, 0x7f03040b, 0x7f030429, 0x7f03042a, 0x7f03042b, 0x7f03049f, 0x7f0304a8, 0x7f030577, 0x7f030584 }
int styleable SearchView_android_textAppearance 0
int styleable SearchView_android_focusable 1
int styleable SearchView_android_maxWidth 2
int styleable SearchView_android_text 3
int styleable SearchView_android_hint 4
int styleable SearchView_android_inputType 5
int styleable SearchView_android_imeOptions 6
int styleable SearchView_animateMenuItems 7
int styleable SearchView_animateNavigationIcon 8
int styleable SearchView_autoShowKeyboard 9
int styleable SearchView_backHandlingEnabled 10
int styleable SearchView_backgroundTint 11
int styleable SearchView_closeIcon 12
int styleable SearchView_commitIcon 13
int styleable SearchView_defaultQueryHint 14
int styleable SearchView_goIcon 15
int styleable SearchView_headerLayout 16
int styleable SearchView_hideNavigationIcon 17
int styleable SearchView_iconifiedByDefault 18
int styleable SearchView_layout 19
int styleable SearchView_queryBackground 20
int styleable SearchView_queryHint 21
int styleable SearchView_searchHintIcon 22
int styleable SearchView_searchIcon 23
int styleable SearchView_searchPrefixText 24
int styleable SearchView_submitBackground 25
int styleable SearchView_suggestionRowLayout 26
int styleable SearchView_useDrawerArrowDrawable 27
int styleable SearchView_voiceIcon 28
int[] styleable SeekBarPreference { 0x010100f2, 0x01010136, 0x7f03002c, 0x7f030372, 0x7f03042f, 0x7f030450, 0x7f030574 }
int styleable SeekBarPreference_android_layout 0
int styleable SeekBarPreference_android_max 1
int styleable SeekBarPreference_adjustable 2
int styleable SeekBarPreference_min 3
int styleable SeekBarPreference_seekBarIncrement 4
int styleable SeekBarPreference_showSeekBarValue 5
int styleable SeekBarPreference_updatesContinuously 6
int[] styleable ShapeAppearance { 0x7f03016f, 0x7f030170, 0x7f030171, 0x7f030172, 0x7f030173, 0x7f030175, 0x7f030176, 0x7f030177, 0x7f030178, 0x7f030179 }
int styleable ShapeAppearance_cornerFamily 0
int styleable ShapeAppearance_cornerFamilyBottomLeft 1
int styleable ShapeAppearance_cornerFamilyBottomRight 2
int styleable ShapeAppearance_cornerFamilyTopLeft 3
int styleable ShapeAppearance_cornerFamilyTopRight 4
int styleable ShapeAppearance_cornerSize 5
int styleable ShapeAppearance_cornerSizeBottomLeft 6
int styleable ShapeAppearance_cornerSizeBottomRight 7
int styleable ShapeAppearance_cornerSizeTopLeft 8
int styleable ShapeAppearance_cornerSizeTopRight 9
int[] styleable ShapeableImageView { 0x7f030163, 0x7f030164, 0x7f030165, 0x7f030166, 0x7f030167, 0x7f030168, 0x7f030169, 0x7f030438, 0x7f030440, 0x7f030498, 0x7f030499 }
int styleable ShapeableImageView_contentPadding 0
int styleable ShapeableImageView_contentPaddingBottom 1
int styleable ShapeableImageView_contentPaddingEnd 2
int styleable ShapeableImageView_contentPaddingLeft 3
int styleable ShapeableImageView_contentPaddingRight 4
int styleable ShapeableImageView_contentPaddingStart 5
int styleable ShapeableImageView_contentPaddingTop 6
int styleable ShapeableImageView_shapeAppearance 7
int styleable ShapeableImageView_shapeAppearanceOverlay 8
int styleable ShapeableImageView_strokeColor 9
int styleable ShapeableImageView_strokeWidth 10
int[] styleable SideSheetBehavior_Layout { 0x0101011f, 0x01010120, 0x01010440, 0x7f03005d, 0x7f030076, 0x7f03016e, 0x7f030438, 0x7f030440 }
int styleable SideSheetBehavior_Layout_android_maxWidth 0
int styleable SideSheetBehavior_Layout_android_maxHeight 1
int styleable SideSheetBehavior_Layout_android_elevation 2
int styleable SideSheetBehavior_Layout_backgroundTint 3
int styleable SideSheetBehavior_Layout_behavior_draggable 4
int styleable SideSheetBehavior_Layout_coplanarSiblingViewId 5
int styleable SideSheetBehavior_Layout_shapeAppearance 6
int styleable SideSheetBehavior_Layout_shapeAppearanceOverlay 7
int[] styleable SignInButton { 0x7f0300a3, 0x7f03013c, 0x7f030425 }
int styleable SignInButton_buttonSize 0
int styleable SignInButton_colorScheme 1
int styleable SignInButton_scopeUris 2
int[] styleable Slider { 0x0101000e, 0x01010024, 0x01010146, 0x010102de, 0x010102df, 0x7f03025b, 0x7f03025c, 0x7f0302b7, 0x7f0302b8, 0x7f030376, 0x7f03051b, 0x7f03051c, 0x7f03051d, 0x7f030522, 0x7f030523, 0x7f030524, 0x7f030528, 0x7f030529, 0x7f03052a, 0x7f03052b, 0x7f03052c, 0x7f030530, 0x7f030531, 0x7f030532, 0x7f030554, 0x7f030555, 0x7f030556, 0x7f03055b, 0x7f03055c, 0x7f03055d }
int styleable Slider_android_enabled 0
int styleable Slider_android_value 1
int styleable Slider_android_stepSize 2
int styleable Slider_android_valueFrom 3
int styleable Slider_android_valueTo 4
int styleable Slider_haloColor 5
int styleable Slider_haloRadius 6
int styleable Slider_labelBehavior 7
int styleable Slider_labelStyle 8
int styleable Slider_minTouchTargetSize 9
int styleable Slider_thumbColor 10
int styleable Slider_thumbElevation 11
int styleable Slider_thumbHeight 12
int styleable Slider_thumbRadius 13
int styleable Slider_thumbStrokeColor 14
int styleable Slider_thumbStrokeWidth 15
int styleable Slider_thumbTrackGapSize 16
int styleable Slider_thumbWidth 17
int styleable Slider_tickColor 18
int styleable Slider_tickColorActive 19
int styleable Slider_tickColorInactive 20
int styleable Slider_tickRadiusActive 21
int styleable Slider_tickRadiusInactive 22
int styleable Slider_tickVisible 23
int styleable Slider_trackColor 24
int styleable Slider_trackColorActive 25
int styleable Slider_trackColorInactive 26
int styleable Slider_trackHeight 27
int styleable Slider_trackInsideCornerSize 28
int styleable Slider_trackStopIndicatorSize 29
int[] styleable Snackbar { 0x7f030460, 0x7f030461, 0x7f030462 }
int styleable Snackbar_snackbarButtonStyle 0
int styleable Snackbar_snackbarStyle 1
int styleable Snackbar_snackbarTextViewStyle 2
int[] styleable SnackbarLayout { 0x0101011f, 0x7f030025, 0x7f03003f, 0x7f03005a, 0x7f03005d, 0x7f03005e, 0x7f0301cf, 0x7f030363, 0x7f030438, 0x7f030440 }
int styleable SnackbarLayout_android_maxWidth 0
int styleable SnackbarLayout_actionTextColorAlpha 1
int styleable SnackbarLayout_animationMode 2
int styleable SnackbarLayout_backgroundOverlayColorAlpha 3
int styleable SnackbarLayout_backgroundTint 4
int styleable SnackbarLayout_backgroundTintMode 5
int styleable SnackbarLayout_elevation 6
int styleable SnackbarLayout_maxActionInlineWidth 7
int styleable SnackbarLayout_shapeAppearance 8
int styleable SnackbarLayout_shapeAppearanceOverlay 9
int[] styleable Spinner { 0x010100b2, 0x01010176, 0x0101017b, 0x01010262, 0x7f0303f2 }
int styleable Spinner_android_entries 0
int styleable Spinner_android_popupBackground 1
int styleable Spinner_android_prompt 2
int styleable Spinner_android_dropDownWidth 3
int styleable Spinner_popupTheme 4
int[] styleable SplitPairFilter { 0x7f030404, 0x7f03042d, 0x7f03042e }
int styleable SplitPairFilter_primaryActivityName 0
int styleable SplitPairFilter_secondaryActivityAction 1
int styleable SplitPairFilter_secondaryActivityName 2
int[] styleable SplitPairRule { 0x7f03003e, 0x7f0300f9, 0x7f030213, 0x7f030214, 0x7f030467, 0x7f030468, 0x7f030469, 0x7f03046a, 0x7f03046b, 0x7f03046c, 0x7f03046d, 0x7f0304d2 }
int styleable SplitPairRule_animationBackgroundColor 0
int styleable SplitPairRule_clearTop 1
int styleable SplitPairRule_finishPrimaryWithSecondary 2
int styleable SplitPairRule_finishSecondaryWithPrimary 3
int styleable SplitPairRule_splitLayoutDirection 4
int styleable SplitPairRule_splitMaxAspectRatioInLandscape 5
int styleable SplitPairRule_splitMaxAspectRatioInPortrait 6
int styleable SplitPairRule_splitMinHeightDp 7
int styleable SplitPairRule_splitMinSmallestWidthDp 8
int styleable SplitPairRule_splitMinWidthDp 9
int styleable SplitPairRule_splitRatio 10
int styleable SplitPairRule_tag 11
int[] styleable SplitPlaceholderRule { 0x7f03003e, 0x7f030212, 0x7f0303e5, 0x7f030467, 0x7f030468, 0x7f030469, 0x7f03046a, 0x7f03046b, 0x7f03046c, 0x7f03046d, 0x7f03048c, 0x7f0304d2 }
int styleable SplitPlaceholderRule_animationBackgroundColor 0
int styleable SplitPlaceholderRule_finishPrimaryWithPlaceholder 1
int styleable SplitPlaceholderRule_placeholderActivityName 2
int styleable SplitPlaceholderRule_splitLayoutDirection 3
int styleable SplitPlaceholderRule_splitMaxAspectRatioInLandscape 4
int styleable SplitPlaceholderRule_splitMaxAspectRatioInPortrait 5
int styleable SplitPlaceholderRule_splitMinHeightDp 6
int styleable SplitPlaceholderRule_splitMinSmallestWidthDp 7
int styleable SplitPlaceholderRule_splitMinWidthDp 8
int styleable SplitPlaceholderRule_splitRatio 9
int styleable SplitPlaceholderRule_stickyPlaceholder 10
int styleable SplitPlaceholderRule_tag 11
int[] styleable State { 0x010100d0, 0x7f03015a }
int styleable State_android_id 0
int styleable State_constraints 1
int[] styleable StateListDrawable { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable StateListDrawable_android_dither 0
int styleable StateListDrawable_android_visible 1
int styleable StateListDrawable_android_variablePadding 2
int styleable StateListDrawable_android_constantSize 3
int styleable StateListDrawable_android_enterFadeDuration 4
int styleable StateListDrawable_android_exitFadeDuration 5
int[] styleable StateListDrawableItem { 0x01010199 }
int styleable StateListDrawableItem_android_drawable 0
int[] styleable StateSet { 0x7f03019c }
int styleable StateSet_defaultState 0
int[] styleable StripeCardFormView { 0x7f030055, 0x7f0300b8 }
int styleable StripeCardFormView_backgroundColorStateList 0
int styleable StripeCardFormView_cardFormStyle 1
int[] styleable StripeCountryAutoCompleteTextInputLayout { 0x7f030180, 0x7f030181 }
int styleable StripeCountryAutoCompleteTextInputLayout_countryAutoCompleteStyle 0
int styleable StripeCountryAutoCompleteTextInputLayout_countryItemLayout 1
int[] styleable SwipeRefreshLayout { 0x7f0304ac }
int styleable SwipeRefreshLayout_swipeRefreshLayoutProgressSpinnerBackgroundColor 0
int[] styleable SwitchCompat { 0x01010124, 0x01010125, 0x01010142, 0x7f030451, 0x7f03046e, 0x7f0304ad, 0x7f0304ae, 0x7f0304b2, 0x7f030525, 0x7f030526, 0x7f030527, 0x7f030553, 0x7f03055f, 0x7f030560 }
int styleable SwitchCompat_android_textOn 0
int styleable SwitchCompat_android_textOff 1
int styleable SwitchCompat_android_thumb 2
int styleable SwitchCompat_showText 3
int styleable SwitchCompat_splitTrack 4
int styleable SwitchCompat_switchMinWidth 5
int styleable SwitchCompat_switchPadding 6
int styleable SwitchCompat_switchTextAppearance 7
int styleable SwitchCompat_thumbTextPadding 8
int styleable SwitchCompat_thumbTint 9
int styleable SwitchCompat_thumbTintMode 10
int styleable SwitchCompat_track 11
int styleable SwitchCompat_trackTint 12
int styleable SwitchCompat_trackTintMode 13
int[] styleable SwitchMaterial { 0x7f030578 }
int styleable SwitchMaterial_useMaterialThemeColors 0
int[] styleable SwitchPreference { 0x010101ef, 0x010101f0, 0x010101f1, 0x0101036b, 0x0101036c, 0x7f0301ab, 0x7f0304aa, 0x7f0304ab, 0x7f0304b3, 0x7f0304b4 }
int styleable SwitchPreference_android_summaryOn 0
int styleable SwitchPreference_android_summaryOff 1
int styleable SwitchPreference_android_disableDependentsState 2
int styleable SwitchPreference_android_switchTextOn 3
int styleable SwitchPreference_android_switchTextOff 4
int styleable SwitchPreference_disableDependentsState 5
int styleable SwitchPreference_summaryOff 6
int styleable SwitchPreference_summaryOn 7
int styleable SwitchPreference_switchTextOff 8
int styleable SwitchPreference_switchTextOn 9
int[] styleable SwitchPreferenceCompat { 0x010101ef, 0x010101f0, 0x010101f1, 0x0101036b, 0x0101036c, 0x7f0301ab, 0x7f0304aa, 0x7f0304ab, 0x7f0304b3, 0x7f0304b4 }
int styleable SwitchPreferenceCompat_android_summaryOn 0
int styleable SwitchPreferenceCompat_android_summaryOff 1
int styleable SwitchPreferenceCompat_android_disableDependentsState 2
int styleable SwitchPreferenceCompat_android_switchTextOn 3
int styleable SwitchPreferenceCompat_android_switchTextOff 4
int styleable SwitchPreferenceCompat_disableDependentsState 5
int styleable SwitchPreferenceCompat_summaryOff 6
int styleable SwitchPreferenceCompat_summaryOn 7
int styleable SwitchPreferenceCompat_switchTextOff 8
int styleable SwitchPreferenceCompat_switchTextOn 9
int[] styleable TabItem { 0x01010002, 0x010100f2, 0x0101014f }
int styleable TabItem_android_icon 0
int styleable TabItem_android_layout 1
int styleable TabItem_android_text 2
int[] styleable TabLayout { 0x7f0304b5, 0x7f0304b6, 0x7f0304b7, 0x7f0304b8, 0x7f0304b9, 0x7f0304ba, 0x7f0304bb, 0x7f0304bc, 0x7f0304bd, 0x7f0304be, 0x7f0304bf, 0x7f0304c0, 0x7f0304c1, 0x7f0304c2, 0x7f0304c3, 0x7f0304c4, 0x7f0304c5, 0x7f0304c6, 0x7f0304c7, 0x7f0304c8, 0x7f0304c9, 0x7f0304ca, 0x7f0304cc, 0x7f0304cd, 0x7f0304cf, 0x7f0304d0, 0x7f0304d1 }
int styleable TabLayout_tabBackground 0
int styleable TabLayout_tabContentStart 1
int styleable TabLayout_tabGravity 2
int styleable TabLayout_tabIconTint 3
int styleable TabLayout_tabIconTintMode 4
int styleable TabLayout_tabIndicator 5
int styleable TabLayout_tabIndicatorAnimationDuration 6
int styleable TabLayout_tabIndicatorAnimationMode 7
int styleable TabLayout_tabIndicatorColor 8
int styleable TabLayout_tabIndicatorFullWidth 9
int styleable TabLayout_tabIndicatorGravity 10
int styleable TabLayout_tabIndicatorHeight 11
int styleable TabLayout_tabInlineLabel 12
int styleable TabLayout_tabMaxWidth 13
int styleable TabLayout_tabMinWidth 14
int styleable TabLayout_tabMode 15
int styleable TabLayout_tabPadding 16
int styleable TabLayout_tabPaddingBottom 17
int styleable TabLayout_tabPaddingEnd 18
int styleable TabLayout_tabPaddingStart 19
int styleable TabLayout_tabPaddingTop 20
int styleable TabLayout_tabRippleColor 21
int styleable TabLayout_tabSelectedTextAppearance 22
int styleable TabLayout_tabSelectedTextColor 23
int styleable TabLayout_tabTextAppearance 24
int styleable TabLayout_tabTextColor 25
int styleable TabLayout_tabUnboundedRipple 26
int[] styleable TextAppearance { 0x01010095, 0x01010096, 0x01010097, 0x01010098, 0x0101009a, 0x0101009b, 0x01010161, 0x01010162, 0x01010163, 0x01010164, 0x010103ac, 0x01010585, 0x7f030239, 0x7f030242, 0x7f0304d8, 0x7f03050f }
int styleable TextAppearance_android_textSize 0
int styleable TextAppearance_android_typeface 1
int styleable TextAppearance_android_textStyle 2
int styleable TextAppearance_android_textColor 3
int styleable TextAppearance_android_textColorHint 4
int styleable TextAppearance_android_textColorLink 5
int styleable TextAppearance_android_shadowColor 6
int styleable TextAppearance_android_shadowDx 7
int styleable TextAppearance_android_shadowDy 8
int styleable TextAppearance_android_shadowRadius 9
int styleable TextAppearance_android_fontFamily 10
int styleable TextAppearance_android_textFontWeight 11
int styleable TextAppearance_fontFamily 12
int styleable TextAppearance_fontVariationSettings 13
int styleable TextAppearance_textAllCaps 14
int styleable TextAppearance_textLocale 15
int[] styleable TextEffects { 0x01010095, 0x01010096, 0x01010097, 0x0101014f, 0x01010161, 0x01010162, 0x01010163, 0x01010164, 0x010103ac, 0x7f030081, 0x7f030082, 0x7f030506, 0x7f030510, 0x7f030511 }
int styleable TextEffects_android_textSize 0
int styleable TextEffects_android_typeface 1
int styleable TextEffects_android_textStyle 2
int styleable TextEffects_android_text 3
int styleable TextEffects_android_shadowColor 4
int styleable TextEffects_android_shadowDx 5
int styleable TextEffects_android_shadowDy 6
int styleable TextEffects_android_shadowRadius 7
int styleable TextEffects_android_fontFamily 8
int styleable TextEffects_borderRound 9
int styleable TextEffects_borderRoundPercent 10
int styleable TextEffects_textFillColor 11
int styleable TextEffects_textOutlineColor 12
int styleable TextEffects_textOutlineThickness 13
int[] styleable TextInputEditText { 0x7f03050a }
int styleable TextInputEditText_textInputLayoutFocusedRectEnabled 0
int[] styleable TextInputLayout { 0x0101000e, 0x0101009a, 0x0101011f, 0x0101013f, 0x01010150, 0x01010157, 0x0101015a, 0x7f03008b, 0x7f03008c, 0x7f03008d, 0x7f03008e, 0x7f03008f, 0x7f030090, 0x7f030091, 0x7f030092, 0x7f030093, 0x7f030094, 0x7f030095, 0x7f03017a, 0x7f03017b, 0x7f03017c, 0x7f03017d, 0x7f03017e, 0x7f03017f, 0x7f030184, 0x7f030185, 0x7f0301d7, 0x7f0301d8, 0x7f0301d9, 0x7f0301da, 0x7f0301db, 0x7f0301dc, 0x7f0301dd, 0x7f0301de, 0x7f0301e7, 0x7f0301e8, 0x7f0301e9, 0x7f0301ea, 0x7f0301eb, 0x7f0301ec, 0x7f0301ee, 0x7f0301ef, 0x7f0301f3, 0x7f03025f, 0x7f030260, 0x7f030261, 0x7f030262, 0x7f030268, 0x7f030269, 0x7f03026a, 0x7f03026b, 0x7f0303d3, 0x7f0303d4, 0x7f0303d5, 0x7f0303d6, 0x7f0303d7, 0x7f0303e6, 0x7f0303e7, 0x7f0303e8, 0x7f0303ff, 0x7f030400, 0x7f030401, 0x7f030438, 0x7f030440, 0x7f030478, 0x7f030479, 0x7f03047a, 0x7f03047b, 0x7f03047c, 0x7f03047d, 0x7f03047e, 0x7f0304a5, 0x7f0304a6, 0x7f0304a7 }
int styleable TextInputLayout_android_enabled 0
int styleable TextInputLayout_android_textColorHint 1
int styleable TextInputLayout_android_maxWidth 2
int styleable TextInputLayout_android_minWidth 3
int styleable TextInputLayout_android_hint 4
int styleable TextInputLayout_android_maxEms 5
int styleable TextInputLayout_android_minEms 6
int styleable TextInputLayout_boxBackgroundColor 7
int styleable TextInputLayout_boxBackgroundMode 8
int styleable TextInputLayout_boxCollapsedPaddingTop 9
int styleable TextInputLayout_boxCornerRadiusBottomEnd 10
int styleable TextInputLayout_boxCornerRadiusBottomStart 11
int styleable TextInputLayout_boxCornerRadiusTopEnd 12
int styleable TextInputLayout_boxCornerRadiusTopStart 13
int styleable TextInputLayout_boxStrokeColor 14
int styleable TextInputLayout_boxStrokeErrorColor 15
int styleable TextInputLayout_boxStrokeWidth 16
int styleable TextInputLayout_boxStrokeWidthFocused 17
int styleable TextInputLayout_counterEnabled 18
int styleable TextInputLayout_counterMaxLength 19
int styleable TextInputLayout_counterOverflowTextAppearance 20
int styleable TextInputLayout_counterOverflowTextColor 21
int styleable TextInputLayout_counterTextAppearance 22
int styleable TextInputLayout_counterTextColor 23
int styleable TextInputLayout_cursorColor 24
int styleable TextInputLayout_cursorErrorColor 25
int styleable TextInputLayout_endIconCheckable 26
int styleable TextInputLayout_endIconContentDescription 27
int styleable TextInputLayout_endIconDrawable 28
int styleable TextInputLayout_endIconMinSize 29
int styleable TextInputLayout_endIconMode 30
int styleable TextInputLayout_endIconScaleType 31
int styleable TextInputLayout_endIconTint 32
int styleable TextInputLayout_endIconTintMode 33
int styleable TextInputLayout_errorAccessibilityLiveRegion 34
int styleable TextInputLayout_errorContentDescription 35
int styleable TextInputLayout_errorEnabled 36
int styleable TextInputLayout_errorIconDrawable 37
int styleable TextInputLayout_errorIconTint 38
int styleable TextInputLayout_errorIconTintMode 39
int styleable TextInputLayout_errorTextAppearance 40
int styleable TextInputLayout_errorTextColor 41
int styleable TextInputLayout_expandedHintEnabled 42
int styleable TextInputLayout_helperText 43
int styleable TextInputLayout_helperTextEnabled 44
int styleable TextInputLayout_helperTextTextAppearance 45
int styleable TextInputLayout_helperTextTextColor 46
int styleable TextInputLayout_hintAnimationEnabled 47
int styleable TextInputLayout_hintEnabled 48
int styleable TextInputLayout_hintTextAppearance 49
int styleable TextInputLayout_hintTextColor 50
int styleable TextInputLayout_passwordToggleContentDescription 51
int styleable TextInputLayout_passwordToggleDrawable 52
int styleable TextInputLayout_passwordToggleEnabled 53
int styleable TextInputLayout_passwordToggleTint 54
int styleable TextInputLayout_passwordToggleTintMode 55
int styleable TextInputLayout_placeholderText 56
int styleable TextInputLayout_placeholderTextAppearance 57
int styleable TextInputLayout_placeholderTextColor 58
int styleable TextInputLayout_prefixText 59
int styleable TextInputLayout_prefixTextAppearance 60
int styleable TextInputLayout_prefixTextColor 61
int styleable TextInputLayout_shapeAppearance 62
int styleable TextInputLayout_shapeAppearanceOverlay 63
int styleable TextInputLayout_startIconCheckable 64
int styleable TextInputLayout_startIconContentDescription 65
int styleable TextInputLayout_startIconDrawable 66
int styleable TextInputLayout_startIconMinSize 67
int styleable TextInputLayout_startIconScaleType 68
int styleable TextInputLayout_startIconTint 69
int styleable TextInputLayout_startIconTintMode 70
int styleable TextInputLayout_suffixText 71
int styleable TextInputLayout_suffixTextAppearance 72
int styleable TextInputLayout_suffixTextColor 73
int[] styleable ThemeAdapterAppCompatTheme { 0x01010031, 0x01010036, 0x010103ac, 0x7f030114, 0x7f03011b, 0x7f030134, 0x7f030136, 0x7f030239, 0x7f03028f, 0x7f03058d }
int styleable ThemeAdapterAppCompatTheme_android_colorBackground 0
int styleable ThemeAdapterAppCompatTheme_android_textColorPrimary 1
int styleable ThemeAdapterAppCompatTheme_android_fontFamily 2
int styleable ThemeAdapterAppCompatTheme_colorAccent 3
int styleable ThemeAdapterAppCompatTheme_colorError 4
int styleable ThemeAdapterAppCompatTheme_colorPrimary 5
int styleable ThemeAdapterAppCompatTheme_colorPrimaryDark 6
int styleable ThemeAdapterAppCompatTheme_fontFamily 7
int styleable ThemeAdapterAppCompatTheme_isLightTheme 8
int styleable ThemeAdapterAppCompatTheme_windowActionBar 9
int[] styleable ThemeAdapterMaterial3Theme { 0x01010031, 0x010103ac, 0x7f03011b, 0x7f03011c, 0x7f03011d, 0x7f030120, 0x7f030121, 0x7f030122, 0x7f030123, 0x7f030127, 0x7f030128, 0x7f03012b, 0x7f03012c, 0x7f03012d, 0x7f03012e, 0x7f03012f, 0x7f030132, 0x7f030133, 0x7f030134, 0x7f030135, 0x7f030139, 0x7f03013d, 0x7f03013e, 0x7f030142, 0x7f03014a, 0x7f03014b, 0x7f03014d, 0x7f03014e, 0x7f0301d1, 0x7f030239, 0x7f03028f, 0x7f030291, 0x7f030427, 0x7f030439, 0x7f03043a, 0x7f03043b, 0x7f03043c, 0x7f03043d, 0x7f0304db, 0x7f0304dc, 0x7f0304dd, 0x7f0304e0, 0x7f0304e1, 0x7f0304e2, 0x7f0304e9, 0x7f0304ea, 0x7f0304eb, 0x7f0304ec, 0x7f0304ed, 0x7f0304ee, 0x7f0304fb, 0x7f0304fc, 0x7f0304fd }
int styleable ThemeAdapterMaterial3Theme_android_colorBackground 0
int styleable ThemeAdapterMaterial3Theme_android_fontFamily 1
int styleable ThemeAdapterMaterial3Theme_colorError 2
int styleable ThemeAdapterMaterial3Theme_colorErrorContainer 3
int styleable ThemeAdapterMaterial3Theme_colorOnBackground 4
int styleable ThemeAdapterMaterial3Theme_colorOnError 5
int styleable ThemeAdapterMaterial3Theme_colorOnErrorContainer 6
int styleable ThemeAdapterMaterial3Theme_colorOnPrimary 7
int styleable ThemeAdapterMaterial3Theme_colorOnPrimaryContainer 8
int styleable ThemeAdapterMaterial3Theme_colorOnSecondary 9
int styleable ThemeAdapterMaterial3Theme_colorOnSecondaryContainer 10
int styleable ThemeAdapterMaterial3Theme_colorOnSurface 11
int styleable ThemeAdapterMaterial3Theme_colorOnSurfaceInverse 12
int styleable ThemeAdapterMaterial3Theme_colorOnSurfaceVariant 13
int styleable ThemeAdapterMaterial3Theme_colorOnTertiary 14
int styleable ThemeAdapterMaterial3Theme_colorOnTertiaryContainer 15
int styleable ThemeAdapterMaterial3Theme_colorOutline 16
int styleable ThemeAdapterMaterial3Theme_colorOutlineVariant 17
int styleable ThemeAdapterMaterial3Theme_colorPrimary 18
int styleable ThemeAdapterMaterial3Theme_colorPrimaryContainer 19
int styleable ThemeAdapterMaterial3Theme_colorPrimaryInverse 20
int styleable ThemeAdapterMaterial3Theme_colorSecondary 21
int styleable ThemeAdapterMaterial3Theme_colorSecondaryContainer 22
int styleable ThemeAdapterMaterial3Theme_colorSurface 23
int styleable ThemeAdapterMaterial3Theme_colorSurfaceInverse 24
int styleable ThemeAdapterMaterial3Theme_colorSurfaceVariant 25
int styleable ThemeAdapterMaterial3Theme_colorTertiary 26
int styleable ThemeAdapterMaterial3Theme_colorTertiaryContainer 27
int styleable ThemeAdapterMaterial3Theme_elevationOverlayColor 28
int styleable ThemeAdapterMaterial3Theme_fontFamily 29
int styleable ThemeAdapterMaterial3Theme_isLightTheme 30
int styleable ThemeAdapterMaterial3Theme_isMaterial3Theme 31
int styleable ThemeAdapterMaterial3Theme_scrimBackground 32
int styleable ThemeAdapterMaterial3Theme_shapeAppearanceCornerExtraLarge 33
int styleable ThemeAdapterMaterial3Theme_shapeAppearanceCornerExtraSmall 34
int styleable ThemeAdapterMaterial3Theme_shapeAppearanceCornerLarge 35
int styleable ThemeAdapterMaterial3Theme_shapeAppearanceCornerMedium 36
int styleable ThemeAdapterMaterial3Theme_shapeAppearanceCornerSmall 37
int styleable ThemeAdapterMaterial3Theme_textAppearanceBodyLarge 38
int styleable ThemeAdapterMaterial3Theme_textAppearanceBodyMedium 39
int styleable ThemeAdapterMaterial3Theme_textAppearanceBodySmall 40
int styleable ThemeAdapterMaterial3Theme_textAppearanceDisplayLarge 41
int styleable ThemeAdapterMaterial3Theme_textAppearanceDisplayMedium 42
int styleable ThemeAdapterMaterial3Theme_textAppearanceDisplaySmall 43
int styleable ThemeAdapterMaterial3Theme_textAppearanceHeadlineLarge 44
int styleable ThemeAdapterMaterial3Theme_textAppearanceHeadlineMedium 45
int styleable ThemeAdapterMaterial3Theme_textAppearanceHeadlineSmall 46
int styleable ThemeAdapterMaterial3Theme_textAppearanceLabelLarge 47
int styleable ThemeAdapterMaterial3Theme_textAppearanceLabelMedium 48
int styleable ThemeAdapterMaterial3Theme_textAppearanceLabelSmall 49
int styleable ThemeAdapterMaterial3Theme_textAppearanceTitleLarge 50
int styleable ThemeAdapterMaterial3Theme_textAppearanceTitleMedium 51
int styleable ThemeAdapterMaterial3Theme_textAppearanceTitleSmall 52
int[] styleable ThemeAdapterMaterialTheme { 0x01010031, 0x010103ac, 0x7f03011b, 0x7f03011d, 0x7f030120, 0x7f030122, 0x7f030127, 0x7f03012b, 0x7f030134, 0x7f03013b, 0x7f03013d, 0x7f030141, 0x7f030142, 0x7f030239, 0x7f03028f, 0x7f030292, 0x7f03043e, 0x7f03043f, 0x7f030441, 0x7f0304d9, 0x7f0304da, 0x7f0304de, 0x7f0304df, 0x7f0304e3, 0x7f0304e4, 0x7f0304e5, 0x7f0304e6, 0x7f0304e7, 0x7f0304e8, 0x7f0304f4, 0x7f0304f9, 0x7f0304fa }
int styleable ThemeAdapterMaterialTheme_android_colorBackground 0
int styleable ThemeAdapterMaterialTheme_android_fontFamily 1
int styleable ThemeAdapterMaterialTheme_colorError 2
int styleable ThemeAdapterMaterialTheme_colorOnBackground 3
int styleable ThemeAdapterMaterialTheme_colorOnError 4
int styleable ThemeAdapterMaterialTheme_colorOnPrimary 5
int styleable ThemeAdapterMaterialTheme_colorOnSecondary 6
int styleable ThemeAdapterMaterialTheme_colorOnSurface 7
int styleable ThemeAdapterMaterialTheme_colorPrimary 8
int styleable ThemeAdapterMaterialTheme_colorPrimaryVariant 9
int styleable ThemeAdapterMaterialTheme_colorSecondary 10
int styleable ThemeAdapterMaterialTheme_colorSecondaryVariant 11
int styleable ThemeAdapterMaterialTheme_colorSurface 12
int styleable ThemeAdapterMaterialTheme_fontFamily 13
int styleable ThemeAdapterMaterialTheme_isLightTheme 14
int styleable ThemeAdapterMaterialTheme_isMaterialTheme 15
int styleable ThemeAdapterMaterialTheme_shapeAppearanceLargeComponent 16
int styleable ThemeAdapterMaterialTheme_shapeAppearanceMediumComponent 17
int styleable ThemeAdapterMaterialTheme_shapeAppearanceSmallComponent 18
int styleable ThemeAdapterMaterialTheme_textAppearanceBody1 19
int styleable ThemeAdapterMaterialTheme_textAppearanceBody2 20
int styleable ThemeAdapterMaterialTheme_textAppearanceButton 21
int styleable ThemeAdapterMaterialTheme_textAppearanceCaption 22
int styleable ThemeAdapterMaterialTheme_textAppearanceHeadline1 23
int styleable ThemeAdapterMaterialTheme_textAppearanceHeadline2 24
int styleable ThemeAdapterMaterialTheme_textAppearanceHeadline3 25
int styleable ThemeAdapterMaterialTheme_textAppearanceHeadline4 26
int styleable ThemeAdapterMaterialTheme_textAppearanceHeadline5 27
int styleable ThemeAdapterMaterialTheme_textAppearanceHeadline6 28
int styleable ThemeAdapterMaterialTheme_textAppearanceOverline 29
int styleable ThemeAdapterMaterialTheme_textAppearanceSubtitle1 30
int styleable ThemeAdapterMaterialTheme_textAppearanceSubtitle2 31
int[] styleable ThemeAdapterShapeAppearance { 0x7f03016f, 0x7f030175, 0x7f030176, 0x7f030177, 0x7f030178, 0x7f030179 }
int styleable ThemeAdapterShapeAppearance_cornerFamily 0
int styleable ThemeAdapterShapeAppearance_cornerSize 1
int styleable ThemeAdapterShapeAppearance_cornerSizeBottomLeft 2
int styleable ThemeAdapterShapeAppearance_cornerSizeBottomRight 3
int styleable ThemeAdapterShapeAppearance_cornerSizeTopLeft 4
int styleable ThemeAdapterShapeAppearance_cornerSizeTopRight 5
int[] styleable ThemeAdapterTextAppearance { 0x01010095, 0x01010096, 0x01010097, 0x01010098, 0x01010161, 0x01010162, 0x01010163, 0x01010164, 0x010103ac, 0x010104b6, 0x010104b7, 0x01010570, 0x0101057f, 0x01010585, 0x7f030239, 0x7f03030f }
int styleable ThemeAdapterTextAppearance_android_textSize 0
int styleable ThemeAdapterTextAppearance_android_typeface 1
int styleable ThemeAdapterTextAppearance_android_textStyle 2
int styleable ThemeAdapterTextAppearance_android_textColor 3
int styleable ThemeAdapterTextAppearance_android_shadowColor 4
int styleable ThemeAdapterTextAppearance_android_shadowDx 5
int styleable ThemeAdapterTextAppearance_android_shadowDy 6
int styleable ThemeAdapterTextAppearance_android_shadowRadius 7
int styleable ThemeAdapterTextAppearance_android_fontFamily 8
int styleable ThemeAdapterTextAppearance_android_letterSpacing 9
int styleable ThemeAdapterTextAppearance_android_fontFeatureSettings 10
int styleable ThemeAdapterTextAppearance_android_fontVariationSettings 11
int styleable ThemeAdapterTextAppearance_android_lineHeight 12
int styleable ThemeAdapterTextAppearance_android_textFontWeight 13
int styleable ThemeAdapterTextAppearance_fontFamily 14
int styleable ThemeAdapterTextAppearance_lineHeight 15
int[] styleable ThemeEnforcement { 0x01010034, 0x7f0301df, 0x7f0301e0 }
int styleable ThemeEnforcement_android_textAppearance 0
int styleable ThemeEnforcement_enforceMaterialTheme 1
int styleable ThemeEnforcement_enforceTextAppearance 2
int[] styleable Toolbar { 0x010100af, 0x01010140, 0x7f03009d, 0x7f030108, 0x7f030109, 0x7f03015d, 0x7f03015e, 0x7f03015f, 0x7f030160, 0x7f030161, 0x7f030162, 0x7f030322, 0x7f030324, 0x7f030364, 0x7f03036d, 0x7f0303ae, 0x7f0303af, 0x7f0303f2, 0x7f0304a0, 0x7f0304a2, 0x7f0304a3, 0x7f030536, 0x7f03053a, 0x7f03053b, 0x7f03053c, 0x7f03053d, 0x7f03053e, 0x7f03053f, 0x7f030541, 0x7f030542 }
int styleable Toolbar_android_gravity 0
int styleable Toolbar_android_minHeight 1
int styleable Toolbar_buttonGravity 2
int styleable Toolbar_collapseContentDescription 3
int styleable Toolbar_collapseIcon 4
int styleable Toolbar_contentInsetEnd 5
int styleable Toolbar_contentInsetEndWithActions 6
int styleable Toolbar_contentInsetLeft 7
int styleable Toolbar_contentInsetRight 8
int styleable Toolbar_contentInsetStart 9
int styleable Toolbar_contentInsetStartWithNavigation 10
int styleable Toolbar_logo 11
int styleable Toolbar_logoDescription 12
int styleable Toolbar_maxButtonHeight 13
int styleable Toolbar_menu 14
int styleable Toolbar_navigationContentDescription 15
int styleable Toolbar_navigationIcon 16
int styleable Toolbar_popupTheme 17
int styleable Toolbar_subtitle 18
int styleable Toolbar_subtitleTextAppearance 19
int styleable Toolbar_subtitleTextColor 20
int styleable Toolbar_title 21
int styleable Toolbar_titleMargin 22
int styleable Toolbar_titleMarginBottom 23
int styleable Toolbar_titleMarginEnd 24
int styleable Toolbar_titleMarginStart 25
int styleable Toolbar_titleMarginTop 26
int styleable Toolbar_titleMargins 27
int styleable Toolbar_titleTextAppearance 28
int styleable Toolbar_titleTextColor 29
int[] styleable Tooltip { 0x01010034, 0x01010098, 0x010100d5, 0x010100f6, 0x0101013f, 0x01010140, 0x0101014f, 0x7f03005d, 0x7f03044d }
int styleable Tooltip_android_textAppearance 0
int styleable Tooltip_android_textColor 1
int styleable Tooltip_android_padding 2
int styleable Tooltip_android_layout_margin 3
int styleable Tooltip_android_minWidth 4
int styleable Tooltip_android_minHeight 5
int styleable Tooltip_android_text 6
int styleable Tooltip_backgroundTint 7
int styleable Tooltip_showMarker 8
int[] styleable Transform { 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f030561 }
int styleable Transform_android_transformPivotX 0
int styleable Transform_android_transformPivotY 1
int styleable Transform_android_translationX 2
int styleable Transform_android_translationY 3
int styleable Transform_android_scaleX 4
int styleable Transform_android_scaleY 5
int styleable Transform_android_rotation 6
int styleable Transform_android_rotationX 7
int styleable Transform_android_rotationY 8
int styleable Transform_android_translationZ 9
int styleable Transform_android_elevation 10
int styleable Transform_transformPivotTarget 11
int[] styleable Transition { 0x010100d0, 0x7f030051, 0x7f030156, 0x7f030157, 0x7f0301c9, 0x7f0302c4, 0x7f0303a3, 0x7f0303d8, 0x7f030476, 0x7f030562, 0x7f030564 }
int styleable Transition_android_id 0
int styleable Transition_autoTransition 1
int styleable Transition_constraintSetEnd 2
int styleable Transition_constraintSetStart 3
int styleable Transition_duration 4
int styleable Transition_layoutDuringTransition 5
int styleable Transition_motionInterpolator 6
int styleable Transition_pathMotionArc 7
int styleable Transition_staggered 8
int styleable Transition_transitionDisable 9
int styleable Transition_transitionFlags 10
int[] styleable Variant { 0x7f03015a, 0x7f030417, 0x7f030418, 0x7f030419, 0x7f03041a }
int styleable Variant_constraints 0
int styleable Variant_region_heightLessThan 1
int styleable Variant_region_heightMoreThan 2
int styleable Variant_region_widthLessThan 3
int styleable Variant_region_widthMoreThan 4
int[] styleable View { 0x01010000, 0x010100da, 0x7f0303c9, 0x7f0303cc, 0x7f030519 }
int styleable View_android_theme 0
int styleable View_android_focusable 1
int styleable View_paddingEnd 2
int styleable View_paddingStart 3
int styleable View_theme 4
int[] styleable ViewBackgroundHelper { 0x010100d4, 0x7f03005d, 0x7f03005e }
int styleable ViewBackgroundHelper_android_background 0
int styleable ViewBackgroundHelper_backgroundTint 1
int styleable ViewBackgroundHelper_backgroundTintMode 2
int[] styleable ViewPager2 { 0x010100c4 }
int styleable ViewPager2_android_orientation 0
int[] styleable ViewStubCompat { 0x010100d0, 0x010100f2, 0x010100f3 }
int styleable ViewStubCompat_android_id 0
int styleable ViewStubCompat_android_layout 1
int styleable ViewStubCompat_android_inflatedId 2
int[] styleable ViewTransition { 0x010100d0, 0x7f030000, 0x7f030001, 0x7f0300fa, 0x7f0301c9, 0x7f03027b, 0x7f03027c, 0x7f0303a3, 0x7f0303a8, 0x7f0303c1, 0x7f0303d8, 0x7f030437, 0x7f030562, 0x7f030573, 0x7f03057f }
int styleable ViewTransition_android_id 0
int styleable ViewTransition_SharedValue 1
int styleable ViewTransition_SharedValueId 2
int styleable ViewTransition_clearsTag 3
int styleable ViewTransition_duration 4
int styleable ViewTransition_ifTagNotSet 5
int styleable ViewTransition_ifTagSet 6
int styleable ViewTransition_motionInterpolator 7
int styleable ViewTransition_motionTarget 8
int styleable ViewTransition_onStateTransition 9
int styleable ViewTransition_pathMotionArc 10
int styleable ViewTransition_setsTag 11
int styleable ViewTransition_transitionDisable 12
int styleable ViewTransition_upDuration 13
int styleable ViewTransition_viewTransitionMode 14
int[] styleable WalletFragmentOptions { 0x7f030041, 0x7f0301e5, 0x7f030248, 0x7f030249 }
int styleable WalletFragmentOptions_appTheme 0
int styleable WalletFragmentOptions_environment 1
int styleable WalletFragmentOptions_fragmentMode 2
int styleable WalletFragmentOptions_fragmentStyle 3
int[] styleable WalletFragmentStyle { 0x7f0300a9, 0x7f0300aa, 0x7f0300ab, 0x7f0300ac, 0x7f03032c, 0x7f03032d, 0x7f03032e, 0x7f03032f, 0x7f030330, 0x7f030331, 0x7f030332 }
int styleable WalletFragmentStyle_buyButtonAppearance 0
int styleable WalletFragmentStyle_buyButtonHeight 1
int styleable WalletFragmentStyle_buyButtonText 2
int styleable WalletFragmentStyle_buyButtonWidth 3
int styleable WalletFragmentStyle_maskedWalletDetailsBackground 4
int styleable WalletFragmentStyle_maskedWalletDetailsButtonBackground 5
int styleable WalletFragmentStyle_maskedWalletDetailsButtonTextAppearance 6
int styleable WalletFragmentStyle_maskedWalletDetailsHeaderTextAppearance 7
int styleable WalletFragmentStyle_maskedWalletDetailsLogoImageType 8
int styleable WalletFragmentStyle_maskedWalletDetailsLogoTextColor 9
int styleable WalletFragmentStyle_maskedWalletDetailsTextAppearance 10
int[] styleable include { 0x7f030155 }
int styleable include_constraintSet 0
int xml file_provider_path_checkout 0x7f150000
int xml image_share_filepaths 0x7f150001
int xml provider_paths 0x7f150002
